#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
WebSearchAgentLocalMerged - <PERSON><PERSON><PERSON> bản hợp nhất của WebSearchAgentLocal

Module này cung cấp một lớp hợp nhất từ nhiều phiên bản của WebSearchAgent
với nhiều tính năng tích hợp sẵn.
"""

import os
import re
import time
import json
import logging
import warnings
import hashlib
import traceback
import random
from typing import List, Dict, Any, Optional, Tuple, Union, Set, Callable
from datetime import datetime
from urllib.parse import urlparse, urljoin, urlunparse, quote_plus, urlencode
from collections import Counter, defaultdict, deque
import math
import tempfile
import urllib
import urllib.robotparser
from concurrent.futures import ThreadPoolExecutor, as_completed
from urllib.robotparser import RobotFileParser

# Import thư viện phổ biến
import requests
from bs4 import BeautifulSoup

# Thử import các module tù<PERSON> chọn
try:
    from playwright.sync_api import sync_playwright
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False

# Thiết lập logging
try:
    from ..utils.structured_logging import get_logger
    logger = get_logger(__name__)
except ImportError:
    # Fallback logging nếu không import được
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

# Thử import các utility modules - ưu tiên từ shared hoặc đã có sẵn
try:
    from ..utils.cache_utils import Cache, CacheEntry, create_cache_key, determine_ttl_by_content_type, get_cache
except ImportError:
    logger.warning("Không thể import cache_utils module")

try:
    from ..utils.query_utils import (
        clean_query, extract_keywords, decompose_query, optimize_query,
        calculate_query_complexity, extract_entities, extract_main_topic,
        extract_facets, generate_alternative_queries, query_similarity
    )
except ImportError:
    logger.warning("Không thể import query_utils module")

try:
    from ..utils.result_utils import (
        extract_domain, get_domain_credibility, rerank_by_credibility,
        merge_similar_results, create_simple_answer, filter_results_by_keywords
    )
except ImportError:
    logger.warning("Không thể import result_utils module")

try:
    from ..utils.error_utils import (
        safe_execute, handle_network_errors, format_error_message,
        DeepResearchError, SearchError, CrawlerError, NetworkError
    )
except ImportError:
    logger.warning("Không thể import error_utils module")

# Vietnamese text processing - ưu tiên module đã có sẵn
try:
    from ..utils.vietnamese_utils import (
        is_vietnamese_text, fix_vietnamese_encoding, improve_vietnamese_paragraphs,
        remove_vietnamese_boilerplate, identify_important_vietnamese_phrases,
        detect_vietnamese, normalize_vietnamese_text, adapt_prompt_for_vietnamese
    )
    VIETNAMESE_UTILS_AVAILABLE = True
except ImportError:
    logger.warning("Không thể import vietnamese_utils module")
    VIETNAMESE_UTILS_AVAILABLE = False

# Vietnamese text processor - advanced processing
try:
    from ..utils.vietnamese_text_processor import VietnameseTextProcessor
    VIETNAMESE_TEXT_PROCESSOR_AVAILABLE = True
except ImportError:
    logger.warning("Không thể import VietnameseTextProcessor")
    VIETNAMESE_TEXT_PROCESSOR_AVAILABLE = False

# Language handler - multilingual support
try:
    from ..utils.shared.language_handler import LanguageHandler
    LANGUAGE_HANDLER_AVAILABLE = True
except ImportError:
    logger.warning("Không thể import LanguageHandler")
    LANGUAGE_HANDLER_AVAILABLE = False

try:
    from ..utils.base_utils import BaseUtils
except ImportError:
    logger.warning("Không thể import BaseUtils")

try:
    from ..utils.query_utils import QueryUtils
except ImportError:
    logger.warning("Không thể import QueryUtils")

try:
    from ..utils.cache_utils import SmartCache
except ImportError:
    logger.warning("Không thể import SmartCache")

try:
    from ..utils.performance_monitor import PerformanceMonitor
except ImportError:
    logger.warning("Không thể import PerformanceMonitor")

try:
    from ..utils.fake_news_detector import FakeNewsDetector
except ImportError:
    logger.warning("Không thể import FakeNewsDetector")

try:
    from ..utils.multilingual_processor import MultilingualProcessor
except ImportError:
    logger.warning("Không thể import MultilingualProcessor")

try:
    from ..utils.adaptive_search_optimizer import AdaptiveSearchOptimizer
except ImportError:
    logger.warning("Không thể import AdaptiveSearchOptimizer")

# LLM Integration - AI analysis
try:
    from ..rl_tuning.environment.llm_integration import LLMIntegration
    LLM_INTEGRATION_AVAILABLE = True
except ImportError:
    logger.warning("Không thể import LLMIntegration")
    LLM_INTEGRATION_AVAILABLE = False

try:
    from ..utils.credibility_utils import (
        evaluate_factual_accuracy, evaluate_source_diversity, evaluate_content_richness,
        extract_factual_statements, check_statement_support, check_contradiction
    )
except ImportError:
    logger.warning("Không thể import credibility_utils module")

try:
    from ..utils.shared.captcha_handler import CaptchaHandler
except ImportError:
    logger.warning("Không thể import CaptchaHandler")

try:
    from ..utils.shared.pagination_handler import PaginationHandler
except ImportError:
    logger.warning("Không thể import PaginationHandler")

try:
    from ..utils.shared.user_agent_manager import UserAgentManager
except ImportError:
    logger.warning("Không thể import UserAgentManager")

try:
    from ..utils.shared.site_structure_handler import SiteStructureHandler
except ImportError:
    logger.warning("Không thể import SiteStructureHandler")

try:
    from ..utils.shared.playwright_handler import PlaywrightHandler
except ImportError:
    logger.warning("Không thể import PlaywrightHandler")

try:
    from ..utils.shared.file_processor import FileProcessor
except ImportError:
    logger.warning("Không thể import FileProcessor")

try:
    from ..integrations.llm.base_analyzer import BaseLLMAnalyzer
except ImportError:
    logger.warning("Không thể import BaseLLMAnalyzer")

try:
    from ..utils.feedback_system import FeedbackSystem
except ImportError:
    logger.warning("Không thể import FeedbackSystem")

try:
    from ..agents.adaptive_crawler_integration import AdaptiveCrawlerIntegration
except ImportError:
    logger.warning("Không thể import AdaptiveCrawlerIntegration")

try:
    from ..utils.shared.config_manager import ConfigManager, get_config_manager
except ImportError:
    logger.warning("Không thể import ConfigManager")

# Suppressing deprecation warnings
warnings.filterwarnings("ignore", category=DeprecationWarning)

# Mô tả version
__version__ = "2.0.0"
__author__ = "Deep Research Team"
__description__ = "WebSearchAgentLocal Merged - Integrated all best features"

# Cố gắng import các dependencies không bắt buộc - Chỉ import khi cần
NLTK_AVAILABLE = False
TRANSFORMERS_AVAILABLE = False
SPACY_AVAILABLE = False
NEWSPAPER_AVAILABLE = False
PLAYWRIGHT_AVAILABLE = False
PYPDF_AVAILABLE = False
DOCX_AVAILABLE = False
PANDAS_AVAILABLE = False

# Cố gắng import FileProcessor - Sử dụng phiên bản từ shared utils
try:
    from ..utils.shared.file_processor import FileProcessor as SharedFileProcessor, extract_file
    FileProcessor = SharedFileProcessor
except ImportError:
    try:
        from ..utils.file_processor import FileProcessor, extract_file
    except ImportError:
        logger.warning("Warning: FileProcessor could not be imported")
        FileProcessor = None
        extract_file = None

class WebSearchAgentLocalMerged:
    """
    Agent tìm kiếm web cục bộ với các tính năng nâng cao tích hợp.

    Phiên bản hợp nhất với:
    - Phân tích độ tin cậy
    - Trích xuất nội dung nâng cao
    - Tăng cường truy vấn
    - Phân tích ngữ nghĩa
    - Tích hợp LLM
    - Phát hiện tin giả
    - Tối ưu hóa hiệu suất
    """

    def __init__(self, **kwargs):
        """Khởi tạo WebSearchAgentLocalMerged với các tùy chọn.

        Args:
            **kwargs: Các tham số tùy chọn cho WebSearchAgentLocalMerged
        """
        # Thiết lập logging
        self.verbose = kwargs.get('verbose', False)
        logging_level = logging.DEBUG if self.verbose else logging.INFO
        logging.basicConfig(level=logging_level,
                            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                            handlers=[logging.StreamHandler()])
        self.logger = logging.getLogger(__name__)

        # Các tùy chọn tìm kiếm
        self.enable_credibility_evaluation = kwargs.get('enable_credibility_evaluation', True)
        self.enable_query_enhancement = kwargs.get('enable_query_enhancement', True)
        self.use_advanced_extraction = kwargs.get('use_advanced_extraction', True)
        self.filter_unreliable_sources = kwargs.get('filter_unreliable_sources', True)
        self.rerank_by_credibility = kwargs.get('rerank_by_credibility', True)
        self.min_credibility_score = kwargs.get('min_credibility_score', 0.3)
        self.data_directory = kwargs.get('data_directory', None)
        self.deep_crawl = kwargs.get('deep_crawl', False)
        self.use_llm = kwargs.get('use_llm', False)
        self.max_results = kwargs.get('max_results', 10)
        self.timeout = kwargs.get('timeout', 30)
        self.max_retries = kwargs.get('max_retries', 3)
        self.verify_ssl = kwargs.get('verify_ssl', True)

        # Các tùy chọn cache
        self.use_cache = kwargs.get('use_cache', True)
        self.cache_ttl = kwargs.get('cache_ttl', 3600)  # 1 giờ mặc định
        self.adaptive_ttl = kwargs.get('adaptive_ttl', True)  # TTL động

        # Các tùy chọn mới từ danh sách yêu cầu
        self.respect_robots_txt = kwargs.get('respect_robots_txt', True)
        self.user_agent = kwargs.get('user_agent', f"WebSearchAgentLocalMerged/{__version__}")
        self.user_agents = kwargs.get('user_agents', [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0"
        ])
        self.enable_rate_limiting = kwargs.get('enable_rate_limiting', True)
        self.rate_limit_delay = kwargs.get('rate_limit_delay', 1.0)
        self.use_proxy_rotation = kwargs.get('use_proxy_rotation', False)
        self.proxies = kwargs.get('proxies', [])
        self.enable_feedback_system = kwargs.get('enable_feedback_system', False)
        self.enable_ml_quality_evaluation = kwargs.get('enable_ml_quality_evaluation', False)
        self.enable_resource_cleanup = kwargs.get('enable_resource_cleanup', True)
        self.enable_fallback_mechanisms = kwargs.get('enable_fallback_mechanisms', True)

        # Cache và robots.txt
        self._robots_cache = {}

        # Components
        self._llm_analyzer = None
        self.credibility_evaluator = None
        self._user_agent_manager = None
        self._playwright_handler = None
        self._pagination_handler = None
        self._file_processor = None
        self._site_structure_handler = None
        self._feedback_system = None
        self._adaptive_crawler_integration = None
        self._config_manager = None

        # Session management
        self.session = requests.Session()

        # Khởi tạo các thành phần
        self._initialize_components()

        # Khởi tạo AnswerQualityEvaluator
        try:
            from ..utils.answer_quality_evaluator import AnswerQualityEvaluator
            self.answer_quality_evaluator = AnswerQualityEvaluator(
                quality_threshold_high=kwargs.get('quality_threshold_high', 0.7),
                quality_threshold_medium=kwargs.get('quality_threshold_medium', 0.4),
                use_model_evaluation=kwargs.get('use_model_evaluation', False),
                model_config=kwargs.get('answer_quality_model_config', None),
                verbose=self.verbose
            )
            self.logger.info("AnswerQualityEvaluator được khởi tạo thành công")
        except ImportError as e:
            self.logger.warning("Không thể khởi tạo AnswerQualityEvaluator: %s", str(e))
            self.answer_quality_evaluator = None

        # Khởi tạo QuestionComplexityEvaluator
        try:
            from ..utils.question_complexity_evaluator import QuestionComplexityEvaluator
            self.question_complexity_evaluator = QuestionComplexityEvaluator(
                complexity_threshold_high=kwargs.get('complexity_threshold_high', 0.7),
                complexity_threshold_medium=kwargs.get('complexity_threshold_medium', 0.4),
                use_domain_knowledge=kwargs.get('use_domain_knowledge', True),
                domain_keywords=kwargs.get('domain_keywords', None),
                verbose=self.verbose
            )
            self.logger.info("QuestionComplexityEvaluator được khởi tạo thành công")
        except ImportError as e:
            self.logger.warning("Không thể khởi tạo QuestionComplexityEvaluator: %s", str(e))
            self.question_complexity_evaluator = None

        # Khởi tạo cache nâng cao nếu cần
        if self.use_cache:
            try:
                from ..utils.cache_utils import Cache, get_cache
                self._smart_cache = get_cache(
                    default_ttl=self.cache_ttl,
                    max_size=1000,  # Giới hạn 1000 entries
                    adaptive_ttl=self.adaptive_ttl
                )
                self.logger.info("Smart Cache được kích hoạt với TTL động")
            except ImportError:
                # Fallback to simple cache
                self.cache = {}
                self.cache_ttl_map = {}  # TTL cho từng cache entry
                self._smart_cache = None
                self.logger.info("Cache đơn giản được kích hoạt")

        # Khởi tạo file processor sử dụng instance đã được import
        self.file_processor = None
        if FileProcessor:
            try:
                self.file_processor = FileProcessor()
                self.logger.info("FileProcessor được khởi tạo thành công")
            except Exception as e:
                self.logger.warning("Không thể khởi tạo FileProcessor: %s", str(e))
                self.file_processor = None

        # Các bộ đếm và chỉ số hiệu suất
        self.requests_count = 0
        self.successful_requests_count = 0
        self.start_time = time.time()

        self.logger.info("WebSearchAgentLocalMerged khởi tạo hoàn tất")

        # Kiểm tra thiết lập
        self._verify_dictionaries()

    def _initialize_components(self):
        """
        Khởi tạo các thành phần của agent.
        """
        # Thiết lập thư mục dữ liệu
        if not self.data_directory:
            # Sử dụng thư mục mặc định
            home_dir = os.path.expanduser("~")
            self.data_directory = os.path.join(home_dir, ".deep_research_data")

        # Tạo thư mục nếu chưa tồn tại
        if not os.path.exists(self.data_directory):
            os.makedirs(self.data_directory, exist_ok=True)
            logger.info("Đã tạo thư mục dữ liệu tại: %s", self.data_directory)

        # Thiết lập các thư mục con
        self.cache_dir = os.path.join(self.data_directory, "cache")
        self.dictionaries_dir = os.path.join(self.data_directory, "dictionaries")
        self.models_dir = os.path.join(self.data_directory, "models")
        self.downloads_dir = os.path.join(self.data_directory, "downloads")

        # Tạo các thư mục con nếu chưa tồn tại
        for directory in [self.cache_dir, self.dictionaries_dir, self.models_dir, self.downloads_dir]:
            if not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)

        # Khởi tạo CaptchaHandler nếu có sẵn
        self._captcha_handler = None
        try:
            # Thử import từ thư mục utils
            try:
                from src.deep_research_core.utils.captcha_handler import CaptchaHandler
                captcha_handler_available = True
            except ImportError:
                # Thử import từ thư mục utils/shared
                try:
                    from src.deep_research_core.utils.shared.captcha_handler import CaptchaHandler
                    captcha_handler_available = True
                except ImportError:
                    # Thử import từ các thư mục khác
                    try:
                        from ..utils.shared.captcha_handler import CaptchaHandler
                        captcha_handler_available = True
                    except ImportError:
                        captcha_handler_available = False
                        logger.warning("CaptchaHandler không khả dụng. Xử lý CAPTCHA sẽ bị hạn chế.")

            if captcha_handler_available:
                self._captcha_handler = CaptchaHandler(
                    use_playwright=PLAYWRIGHT_AVAILABLE,
                    browser_emulation=True,
                    vietnamese_support=True
                )
                logger.info("CaptchaHandler được khởi tạo thành công")
        except Exception as e:
            logger.warning("Không thể khởi tạo CaptchaHandler: %s", str(e))

        # Khởi tạo UserAgentManager
        try:
            if 'UserAgentManager' in globals():
                self._user_agent_manager = UserAgentManager(
                    user_agents=self.user_agents,
                    rotate=True,
                    update_frequency=24 * 60 * 60,  # 1 ngày
                    browser_types=["chrome", "firefox", "safari"],
                    platform_types=["desktop", "mobile"]
                )
                logger.info("UserAgentManager được khởi tạo thành công")
        except Exception as e:
            logger.warning("Không thể khởi tạo UserAgentManager: %s", str(e))

        # Khởi tạo PlaywrightHandler nếu có sẵn
        try:
            if PLAYWRIGHT_AVAILABLE and 'PlaywrightHandler' in globals():
                self._playwright_handler = PlaywrightHandler(
                    headless=True,
                    browser_type="chromium",
                    stealth_mode=True,
                    timeout=self.timeout,
                    user_agent_manager=self._user_agent_manager,
                    captcha_handler=self._captcha_handler
                )
                logger.info("PlaywrightHandler được khởi tạo thành công")
        except Exception as e:
            logger.warning("Không thể khởi tạo PlaywrightHandler: %s", str(e))

        # Khởi tạo PaginationHandler
        try:
            if 'PaginationHandler' in globals():
                self._pagination_handler = PaginationHandler()
                logger.info("PaginationHandler được khởi tạo thành công")
        except Exception as e:
            logger.warning("Không thể khởi tạo PaginationHandler: %s", str(e))

        # Khởi tạo FileProcessor
        try:
            if 'FileProcessor' in globals():
                self._file_processor = FileProcessor(
                    download_path=self.downloads_dir
                )
                logger.info("FileProcessor được khởi tạo thành công")
        except Exception as e:
            logger.warning("Không thể khởi tạo FileProcessor: %s", str(e))

        # Khởi tạo SiteStructureHandler
        try:
            if 'SiteStructureHandler' in globals():
                self._site_structure_handler = SiteStructureHandler()
                logger.info("SiteStructureHandler được khởi tạo thành công")
        except Exception as e:
            logger.warning("Không thể khởi tạo SiteStructureHandler: %s", str(e))

        # Khởi tạo AdaptiveCrawlerIntegration
        try:
            if 'AdaptiveCrawlerIntegration' in globals():
                self._adaptive_crawler_integration = AdaptiveCrawlerIntegration(
                    user_agent_manager=self._user_agent_manager,
                    captcha_handler=self._captcha_handler,
                    playwright_handler=self._playwright_handler,
                    file_processor=self._file_processor,
                    respect_robots_txt=self.respect_robots_txt
                )
                logger.info("AdaptiveCrawlerIntegration được khởi tạo thành công")
        except Exception as e:
            logger.warning("Không thể khởi tạo AdaptiveCrawlerIntegration: %s", str(e))

        # Khởi tạo ConfigManager
        try:
            if 'ConfigManager' in globals():
                self._config_manager = ConfigManager(
                    config_file=os.path.join(self.data_directory, "config.json"),
                    auto_save=True,
                    verbose=self.verbose
                )
                logger.info("ConfigManager được khởi tạo thành công")
        except Exception as e:
            logger.warning("Không thể khởi tạo ConfigManager: %s", str(e))

        # Khởi tạo FeedbackSystem
        try:
            if self.enable_feedback_system and 'FeedbackSystem' in globals():
                self._feedback_system = FeedbackSystem(
                    data_path=os.path.join(self.data_directory, "feedback")
                )
                logger.info("FeedbackSystem được khởi tạo thành công")
        except Exception as e:
            logger.warning("Không thể khởi tạo FeedbackSystem: %s", str(e))

        # Khởi tạo LLM
        if self.use_llm:
            try:
                # Thử import từ thư mục integrations
                try:
                    from src.deep_research_core.integrations.llm import create_llm_analyzer, BaseLLMAnalyzer
                    llm_analyzer_available = True
                except ImportError:
                    # Thử import từ các thư mục khác
                    try:
                        from ..integrations.llm import create_llm_analyzer, BaseLLMAnalyzer
                        llm_analyzer_available = True
                    except ImportError:
                        llm_analyzer_available = False
                        logger.warning("LLMAnalyzer không khả dụng. Phân tích LLM sẽ bị hạn chế.")

                if llm_analyzer_available:
                    self._llm_analyzer = create_llm_analyzer(
                        model_name="gpt-3.5-turbo",  # Mặc định
                        temperature=0.1,
                        max_tokens=1024,
                        api_key=None,  # Sẽ lấy từ biến môi trường
                        verbose=self.verbose
                    )
                    logger.info("LLMAnalyzer được khởi tạo thành công")
            except Exception as e:
                logger.warning("Không thể khởi tạo LLMAnalyzer: %s", str(e))

        # Khởi tạo LanguageHandler để hỗ trợ đa ngôn ngữ
        try:
            from ..utils.shared.language_handler import LanguageHandler
            self._language_handler = LanguageHandler(
                default_lang='en',
                detection_method='auto',
                min_text_length=20,
                verbose=self.verbose
            )
            logger.info("LanguageHandler được khởi tạo thành công")
        except ImportError:
            logger.warning("Không thể import LanguageHandler")
            self._language_handler = None
        except Exception as e:
            logger.warning("Không thể khởi tạo LanguageHandler: %s", str(e))
            self._language_handler = None

        # Khởi tạo BaseLLMAnalyzer để tích hợp LLM
        try:
            if self.use_llm and 'BaseLLMAnalyzer' in globals():
                # Thử import LLMAnalyzer từ module có sẵn
                try:
                    from ..integrations.llm.base_analyzer import BaseLLMAnalyzer
                    self._llm_analyzer = BaseLLMAnalyzer(
                        cache_enabled=True,
                        verbose=self.verbose
                    )
                    logger.info("BaseLLMAnalyzer được khởi tạo thành công")
                except ImportError:
                    logger.warning("Không thể import BaseLLMAnalyzer")
        except Exception as e:
            logger.warning("Không thể khởi tạo BaseLLMAnalyzer: %s", str(e))

    def search(self, query: str, **kwargs):
        """
        Tìm kiếm thông tin dựa trên truy vấn.

        Args:
            query: Truy vấn tìm kiếm
            **kwargs: Các tham số tùy chọn bao gồm:
                - num_results: Số lượng kết quả tối đa (mặc định: 10)
                - language: Ngôn ngữ tìm kiếm (mặc định: "auto")
                - engine: Search engine cụ thể để sử dụng (mặc định: None - tự động chọn)
                - method: Phương thức tìm kiếm ("standard", "adaptive", "deep")
                - get_content: Có tự động tải nội dung hay không (mặc định: False)
                - force_refresh: Bỏ qua cache và tìm kiếm lại (mặc định: False)
                - evaluate_question: Đánh giá độ phức tạp của câu hỏi (mặc định: False)
                - evaluate_answer: Đánh giá chất lượng câu trả lời (mặc định: False)
                - deep_crawl: Thực hiện deep crawl cho các trang kết quả (mặc định: False)
                - decompose_query: Phân rã câu hỏi phức tạp thành các câu đơn giản hơn (mặc định: False)
                - optimize_query: Tối ưu hóa truy vấn (mặc định: True)

        Returns:
            Dictionary chứa kết quả tìm kiếm
        """
        # Import các utility mới
        try:
            from src.deep_research_core.utils.result_utils import (
                extract_domain, get_domain_credibility, rerank_by_credibility,
                merge_similar_results, create_simple_answer, filter_results_by_keywords
            )
        except ImportError:
            # Fallback sử dụng các hàm nội bộ nếu không import được
            extract_domain = self._extract_domain
            get_domain_credibility = self._get_domain_credibility
            rerank_by_credibility = None
            merge_similar_results = None
            create_simple_answer = None
            filter_results_by_keywords = None

        try:
            from src.deep_research_core.utils.error_utils import (
                safe_execute, handle_network_errors, format_error_message
            )
        except ImportError:
            # Fallback
            safe_execute = None
            handle_network_errors = None
            format_error_message = None

        try:
            from src.deep_research_core.utils.vietnamese_utils import (
                is_vietnamese_text, fix_vietnamese_encoding, improve_vietnamese_paragraphs
            )
        except ImportError:
            # Fallback
            is_vietnamese_text = self._is_vietnamese_text
            fix_vietnamese_encoding = self._fix_vietnamese_encoding
            improve_vietnamese_paragraphs = self._improve_vietnamese_paragraphs

        try:
            from src.deep_research_core.utils.credibility_utils import (
                evaluate_factual_accuracy, evaluate_source_diversity, evaluate_content_richness
            )
        except ImportError:
            # Fallback
            evaluate_factual_accuracy = self._evaluate_factual_accuracy
            evaluate_source_diversity = self._evaluate_source_diversity
            evaluate_content_richness = self._evaluate_content_richness

        # Lấy các tham số từ kwargs
        num_results = kwargs.get('num_results', 10)
        language = kwargs.get('language', 'auto')
        engine = kwargs.get('engine', None)
        method = kwargs.get('method', 'standard')
        get_content = kwargs.get('get_content', False)
        force_refresh = kwargs.get('force_refresh', False)
        evaluate_question = kwargs.get('evaluate_question', False)
        evaluate_answer = kwargs.get('evaluate_answer', False)
        deep_crawl = kwargs.get('deep_crawl', self.deep_crawl)
        decompose_query = kwargs.get('decompose_query', False)
        optimize_query = kwargs.get('optimize_query', True)

        if self.verbose:
            self.logger.info("Tìm kiếm: %s", query)

        # Kiểm tra cache trước khi tìm kiếm
        cache_key = self._get_cache_key(query, **kwargs)
        if not force_refresh:
            cached_result = self._get_from_cache(cache_key)
            if cached_result:
                if self.verbose:
                    self.logger.info("Sử dụng kết quả từ cache")
                return cached_result

        # Kiểm tra nếu truy vấn là tiếng Việt
        is_vietnamese = is_vietnamese_text(query)
        if is_vietnamese and self.verbose:
            self.logger.info("Đã phát hiện truy vấn tiếng Việt, áp dụng xử lý đặc biệt")

        # Đánh giá độ phức tạp của câu hỏi nếu cần
        query_complexity = None
        if evaluate_question or decompose_query:
            query_complexity = self._calculate_query_complexity(query)
            if self.verbose:
                self.logger.info("Độ phức tạp câu hỏi: %.2f", query_complexity)

        # Phân rã truy vấn phức tạp nếu cần
        sub_queries = []
        if decompose_query and query_complexity and query_complexity > 0.7:
            sub_queries = self._decompose_query(query)
            if sub_queries and self.verbose:
                self.logger.info("Đã phân rã thành %d truy vấn con: %s", len(sub_queries), sub_queries)

        try:
            results = []

            # Nếu có sub_queries, tìm kiếm cho từng truy vấn con
            if sub_queries and len(sub_queries) > 1:
                all_sub_results = []
                for sub_query in sub_queries:
                    # Thực hiện tìm kiếm thật cho từng truy vấn con
                    sub_results = self._perform_real_search(sub_query, num_results // len(sub_queries))
                    all_sub_results.extend(sub_results)

                # Kết hợp kết quả từ các truy vấn con
                results = all_sub_results[:num_results]
            else:
                # Tìm kiếm với truy vấn gốc - THỰC HIỆN TÌM KIẾM THẬT
                results = self._perform_real_search(query, num_results)

            # Tải nội dung đầy đủ nếu được yêu cầu
            if get_content:
                for result in results:
                    if 'url' in result and not result.get('content'):
                        try:
                            # Gọi _fetch_url để lấy nội dung
                            content_result = self._fetch_url(result['url'])
                            if content_result and 'content' in content_result:
                                result['content'] = content_result['content']

                                # Xử lý đặc biệt cho nội dung tiếng Việt
                                if is_vietnamese and 'content' in result:
                                    result['content'] = improve_vietnamese_paragraphs(result['content'])
                        except Exception as e:
                            if self.verbose:
                                self.logger.warning("Không thể tải nội dung từ %s: %s", result['url'], str(e))

            # Deep crawl nếu được yêu cầu
            if deep_crawl:
                for i, result in enumerate(results[:3]):  # Chỉ deep crawl 3 kết quả đầu tiên
                    if 'url' in result:
                        try:
                            # Gọi hàm deep crawl
                            crawl_result = self._deep_crawl_improved(
                                result['url'],
                                max_depth=2,
                                max_pages=10,
                                same_domain_only=True
                            )
                            if crawl_result and 'pages' in crawl_result:
                                result['related_pages'] = crawl_result['pages']
                        except Exception as e:
                            if self.verbose:
                                self.logger.warning("Không thể deep crawl %s: %s", result['url'], str(e))

            # Đánh giá độ tin cậy
            if self.enable_credibility_evaluation:
                for result in results:
                    domain = extract_domain(result.get("url", ""))
                    result["domain"] = domain
                    result["credibility_score"] = get_domain_credibility(domain) or 0.5

                # Sắp xếp lại kết quả theo độ tin cậy
                if self.rerank_by_credibility and rerank_by_credibility:
                    results = rerank_by_credibility(results, min_score=self.min_credibility_score)
                elif self.rerank_by_credibility:
                    # Fallback nếu không có hàm utility
                    results.sort(key=lambda x: x.get('credibility_score', 0), reverse=True)

                # Lọc các nguồn không đáng tin cậy
                if self.filter_unreliable_sources:
                    original_count = len(results)
                    results = [r for r in results if r.get('credibility_score', 0) >= self.min_credibility_score]
                    if self.verbose and len(results) < original_count:
                        self.logger.info("Đã lọc %d kết quả không đáng tin cậy", original_count - len(results))

            # Gộp các kết quả tương tự
            if merge_similar_results:
                original_count = len(results)
                results = merge_similar_results(results)
                if self.verbose and len(results) < original_count:
                    self.logger.info("Đã gộp %s kết quả tương tự", original_count - len(results))

            # Tạo câu trả lời đơn giản
            simple_answer = ""
            if create_simple_answer:
                simple_answer = create_simple_answer(results, query)
            else:
                # Fallback
                simple_answer = f"Theo kết quả tìm kiếm, {query} là một chủ đề có nhiều thông tin từ các nguồn khác nhau. Bạn có thể tìm hiểu thêm từ các liên kết được cung cấp."

            # Đánh giá kết quả tìm kiếm
            results_evaluation = None
            if evaluate_answer:
                results_evaluation = self._evaluate_search_results_quality(query, results)

            # Chuẩn bị kết quả trả về
            response = {
                "query": query,
                "results": results[:num_results],  # Giới hạn số lượng kết quả
                "simple_answer": simple_answer,
                "method_used": method,
                "is_vietnamese": is_vietnamese
            }

            # Thêm đánh giá câu hỏi nếu được yêu cầu
            if evaluate_question:
                response["question_evaluation"] = self.evaluate_question_complexity(query)

            # Thêm đánh giá câu trả lời nếu được yêu cầu
            if evaluate_answer:
                if results_evaluation:
                    response["results_evaluation"] = results_evaluation
                response["answer_evaluation"] = self.evaluate_answer_quality(
                    simple_answer, query)

            # Lưu kết quả vào cache với TTL động
            cache_ttl = self._determine_cache_ttl(query, "search")
            self._save_to_cache(cache_key, response, ttl=cache_ttl)

            return response

        except Exception as e:
            error_message = str(e)
            if format_error_message:
                error_message = format_error_message(e)

            self.logger.error("Lỗi khi tìm kiếm: %s", error_message)

            # In traceback nếu verbose
            if self.verbose:
                traceback_str = traceback.format_exc()
                self.logger.debug("Traceback: %s", traceback_str)

            # Trả về response lỗi
            return {
                "query": query,
                "error": error_message,
                "results": [],
                "simple_answer": f"Rất tiếc, đã xảy ra lỗi khi tìm kiếm: {error_message}"
            }

    def _verify_dictionaries(self):
        """
        Kiểm tra và tạo các từ điển cần thiết nếu chưa tồn tại.
        """
        # Danh sách các từ điển cần thiết
        dictionaries = {
            'reliable_domains.json': {
                'description': 'Danh sách các domain đáng tin cậy',
                'default': {
                    'trusted_news': ['bbc.com', 'reuters.com', 'apnews.com', 'npr.org', 'cnn.com', 'nytimes.com'],
                    'trusted_education': ['edu', 'ac.uk', 'harvard.edu', 'mit.edu', 'stanford.edu'],
                    'trusted_government': ['gov', 'europa.eu', 'who.int', 'un.org'],
                    'trusted_reference': ['wikipedia.org', 'britannica.com', 'merriam-webster.com']
                }
            },
            'unreliable_domains.json': {
                'description': 'Danh sách các domain không đáng tin cậy',
                'default': {
                    'fake_news': [],
                    'click_bait': [],
                    'conspiracy': [],
                    'spam': []
                }
            },
            'search_engines.json': {
                'description': 'Cấu hình cho các search engine',
                'default': {
                    'general': ['google', 'bing', 'duckduckgo'],
                    'academic': ['google_scholar', 'semantic_scholar', 'pubmed'],
                    'news': ['google_news', 'bing_news'],
                    'vietnamese': ['coccoc', 'google_vn'],
                    'specialized': []
                }
            },
            'query_patterns.json': {
                'description': 'Các mẫu truy vấn phổ biến',
                'default': {
                    'comparison': ['so sánh', 'compare', 'vs', 'versus', 'differences between'],
                    'definition': ['là gì', 'what is', 'define', 'definition of', 'meaning of'],
                    'how_to': ['làm thế nào', 'how to', 'how do i', 'cách', 'phương pháp'],
                    'cause_effect': ['tại sao', 'why', 'causes of', 'effects of', 'nguyên nhân', 'hậu quả'],
                    'list': ['danh sách', 'list of', 'examples of', 'types of'],
                    'fact': ['có thật không', 'is it true', 'fact check', 'kiểm chứng']
                }
            },
            'vietnamese_stopwords.txt': {
                'description': 'Danh sách các stopword tiếng Việt',
                'default': '\n'.join([
                    'và', 'của', 'cho', 'là', 'để', 'trong', 'được', 'với', 'có', 'không',
                    'người', 'những', 'này', 'khi', 'từ', 'một', 'bạn', 'đã', 'các', 'đến',
                    'sẽ', 'về', 'vì', 'như', 'ra', 'nên', 'cũng', 'thì', 'tôi', 'lại',
                    'còn', 'theo', 'nhưng', 'rất', 'nhiều', 'đó', 'cần', 'mà', 'ai', 'lên',
                    'chỉ', 'sau', 'trên', 'vào', 'nếu', 'nào', 'đang', 'hay', 'tại', 'sự'
                ])
            }
        }

        # Kiểm tra và tạo từng từ điển
        for filename, info in dictionaries.items():
            file_path = os.path.join(self.dictionaries_dir, filename)

            # Nếu file chưa tồn tại, tạo mới với dữ liệu mặc định
            if not os.path.exists(file_path):
                logger.info("Creating dictionary file: %s", filename)

                # Xác định loại file dựa trên phần mở rộng
                if filename.endswith('.json'):
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(info['default'], f, indent=4, ensure_ascii=False)
                else:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(info['default'])

                logger.info("Created %s: %s", filename, info['description'])
            else:
                logger.debug("Dictionary file exists: %s", filename)

    def _fix_vietnamese_encoding(self, text: str) -> str:
        """
        Sửa lỗi encoding tiếng Việt phổ biến.

        Args:
            text: Văn bản cần sửa

        Returns:
            Văn bản đã sửa lỗi encoding
        """
        if not text:
            return ""

        # Danh sách các cặp thay thế phổ biến
        replacements = [
            # Các dấu thanh
            ('à', 'à'), ('á', 'á'), ('ả', 'ả'), ('ã', 'ã'), ('ạ', 'ạ'),
            ('ă', 'ă'), ('ằ', 'ằ'), ('ắ', 'ắ'), ('ẳ', 'ẳ'), ('ẵ', 'ẵ'), ('ặ', 'ặ'),
            ('â', 'â'), ('ầ', 'ầ'), ('ấ', 'ấ'), ('ẩ', 'ẩ'), ('ẫ', 'ẫ'), ('ậ', 'ậ'),
            ('è', 'è'), ('é', 'é'), ('ẻ', 'ẻ'), ('ẽ', 'ẽ'), ('ẹ', 'ẹ'),
            ('ê', 'ê'), ('ề', 'ề'), ('ế', 'ế'), ('ể', 'ể'), ('ễ', 'ễ'), ('ệ', 'ệ'),
            ('ì', 'ì'), ('í', 'í'), ('ỉ', 'ỉ'), ('ĩ', 'ĩ'), ('ị', 'ị'),
            ('ò', 'ò'), ('ó', 'ó'), ('ỏ', 'ỏ'), ('õ', 'õ'), ('ọ', 'ọ'),
            ('ô', 'ô'), ('ồ', 'ồ'), ('ố', 'ố'), ('ổ', 'ổ'), ('ỗ', 'ỗ'), ('ộ', 'ộ'),
            ('ơ', 'ơ'), ('ờ', 'ờ'), ('ớ', 'ớ'), ('ở', 'ở'), ('ỡ', 'ỡ'), ('ợ', 'ợ'),
            ('ù', 'ù'), ('ú', 'ú'), ('ủ', 'ủ'), ('ũ', 'ũ'), ('ụ', 'ụ'),
            ('ư', 'ư'), ('ừ', 'ừ'), ('ứ', 'ứ'), ('ử', 'ử'), ('ữ', 'ữ'), ('ự', 'ự'),
            ('ỳ', 'ỳ'), ('ý', 'ý'), ('ỷ', 'ỷ'), ('ỹ', 'ỹ'), ('ỵ', 'ỵ'),
            ('đ', 'đ'),

            # Các lỗi phổ biến khi encode/decode (đã chuyển sang dạng an toàn)
            ('Ã\xc2\xa4', 'à'), ('Ã\xc2\xa1', 'á'), ('áº\xc2\xa3', 'ả'), ('Ã\xc2\xa3', 'ã'), ('áº\xc2\xa1', 'ạ'),
            ('Ã\xc2\x84\xc2\x83', 'ă'), ('áº\xc2\xb1', 'ằ'), ('áº\xc2\xaf', 'ắ'), ('áº\xc2\xb3', 'ẳ'), ('áº\xc2\xb5', 'ẵ'), ('áº\xc2\xb7', 'ặ'),
            ('Ã\xc2\xa2', 'â'), ('áº\xc2\xa7', 'ầ'), ('áº\xc2\xa5', 'ấ'), ('áº\xc2\xa9', 'ẩ'), ('áº\xc2\xab', 'ẫ'), ('áº\xc2\xad', 'ậ'),
            ('Ã\xc2\xa8', 'è'), ('Ã\xc2\xa9', 'é'), ('áº\xc2\xbb', 'ẻ'), ('áº\xc2\xbd', 'ẽ'), ('áº\xc2\xb9', 'ẹ'),
            ('Ã\xc2\xaa', 'ê'), ('á»\xc2\x81', 'ề'), ('áº\xc2\xbf', 'ế'), ('á»\xc2\x83', 'ể'), ('á»\xc2\x85', 'ễ'), ('á»\xc2\x87', 'ệ'),
            ('Ã\xc2\xac', 'ì'), ('Ã\xc2\xad', 'í'), ('á»\xc2\x89', 'ỉ'), ('Ã\xc2\x84\xc2\xa9', 'ĩ'), ('á»\xc2\x8b', 'ị'),
            ('Ã\xc2\xb2', 'ò'), ('Ã\xc2\xb3', 'ó'), ('á»\xc2\x8f', 'ỏ'), ('Ã\xc2\xb5', 'õ'), ('á»\xc2\x8d', 'ọ'),
            ('Ã\xc2\xb4', 'ô'), ('á»\xc2\x93', 'ồ'), ('á»\xc2\x91', 'ố'), ('á»\xc2\x95', 'ổ'), ('á»\xc2\x97', 'ỗ'), ('á»\xc2\x99', 'ộ'),
            ('Ã\xc2\x86\xc2\xa1', 'ơ'), ('á»\xc2\x9d', 'ờ'), ('á»\xc2\x9b', 'ớ'), ('á»\xc2\x9f', 'ở'), ('á»\xc2\xa1', 'ỡ'), ('á»\xc2\xa3', 'ợ'),
            ('Ã\xc2\xb9', 'ù'), ('Ã\xc2\xba', 'ú'), ('á»\xc2\xa7', 'ủ'), ('Ã\xc2\x85\xc2\xa9', 'ũ'), ('á»\xc2\xa5', 'ụ'),
            ('Ã\xc2\x86\xc2\xb0', 'ư'), ('á»\xc2\xab', 'ừ'), ('á»\xc2\xa9', 'ứ'), ('á»\xc2\xad', 'ử'), ('á»\xc2\xaf', 'ữ'), ('á»\xc2\xb1', 'ự'),
            ('á»\xc2\xb3', 'ỳ'), ('Ã\xc2\xbd', 'ý'), ('á»\xc2\xb7', 'ỷ'), ('á»\xc2\xb9', 'ỹ'), ('á»\xc2\xb5', 'ỵ'),
            ('Ã\xc2\x84\xc2\x91', 'đ'), ('&#273;', 'đ'), ('&#272;', 'Đ')
        ]

        # Thực hiện các thay thế
        for wrong, correct in replacements:
            text = text.replace(wrong, correct)

        # Xử lý các entity HTML phổ biến
        text = text.replace('&nbsp;', ' ')
        text = text.replace('&amp;', '&')
        text = text.replace('&lt;', '<')
        text = text.replace('&gt;', '>')
        text = text.replace('&quot;', '"')

        # Xử lý các khoảng trắng dư thừa
        text = re.sub(r'\s+', ' ', text)

        return text.strip()

    def _is_vietnamese_text(self, text: str) -> bool:
        """
        Kiểm tra xem văn bản có phải là tiếng Việt không.

        Args:
            text: Văn bản cần kiểm tra

        Returns:
            True nếu văn bản là tiếng Việt, False nếu không
        """
        if not text or len(text.strip()) < 10:
            return False

        # Danh sách các ký tự đặc biệt trong tiếng Việt
        vietnamese_chars = set('áàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđ')

        # Đếm số lượng ký tự tiếng Việt
        text_lower = text.lower()
        vn_char_count = sum(1 for c in text_lower if c in vietnamese_chars)

        # Nếu có ít nhất 2 ký tự tiếng Việt và tỷ lệ ký tự tiếng Việt so với tổng số ký tự (không kể khoảng trắng) > 5%
        total_chars = len([c for c in text_lower if c.isalpha()])
        if total_chars == 0:
            return False

        vn_char_ratio = vn_char_count / total_chars

        # Kiểm tra các từ tiếng Việt phổ biến
        vietnamese_words = ['của', 'và', 'các', 'những', 'trong', 'với', 'cho', 'được', 'không', 'này', 'đó', 'có', 'là']
        word_count = sum(1 for word in vietnamese_words if word in text_lower)

        return (vn_char_count >= 2 and vn_char_ratio >= 0.05) or word_count >= 3

    def evaluate_question_complexity(self, query: str) -> Dict[str, Any]:
        """
        Đánh giá độ phức tạp của câu hỏi.

        Args:
            query: Câu hỏi cần đánh giá

        Returns:
            Dictionary chứa thông tin đánh giá
        """
        if not query:
            return {
                "complexity_level": "low",
                "complexity_score": 0.1,
                "question_type": "invalid",
                "question_characteristics": ["Câu hỏi trống hoặc không hợp lệ"],
                "entities": [],
                "keywords": [],
                "recommended_strategy": {
                    "search_method": "simple",
                    "max_depth": 1,
                    "max_pages": 3,
                    "decompose_query": False,
                    "use_multiple_engines": False
                }
            }

        # Sử dụng QuestionComplexityEvaluator nếu có sẵn
        if self.question_complexity_evaluator:
            try:
                return self.question_complexity_evaluator.evaluate(query)
            except Exception as e:
                self.logger.warning("Lỗi khi sử dụng QuestionComplexityEvaluator: %s", str(e))

        # Fallback: Tính toán độ phức tạp cơ bản
        complexity_score = self._calculate_query_complexity(query)

        # Xác định mức độ phức tạp
        if complexity_score < 0.3:
            complexity_level = "low"
        elif complexity_score < 0.7:
            complexity_level = "medium"
        else:
            complexity_level = "high"

        # Phân tích loại câu hỏi
        question_type = self._analyze_question_type(query)

        # Trích xuất thực thể và từ khóa
        entities = self._extract_entities(query)
        keywords = self._extract_keywords(query)

        # Đề xuất chiến lược tìm kiếm
        recommended_strategy = self._recommend_search_strategy(complexity_score, question_type)

        return {
            "complexity_level": complexity_level,
            "complexity_score": complexity_score,
            "question_type": question_type,
            "question_characteristics": self._analyze_question_characteristics(query),
            "entities": entities,
            "keywords": keywords,
            "recommended_strategy": recommended_strategy
        }

    def evaluate_answer_quality(self, answer: str, query: Optional[str] = None) -> Dict[str, Any]:
        """
        Đánh giá chất lượng câu trả lời.

        Args:
            answer: Câu trả lời cần đánh giá
            query: Câu hỏi gốc (tùy chọn)

        Returns:
            Dictionary chứa kết quả đánh giá
        """
        if not answer or not answer.strip():
            return {
                "quality_score": 0.0,
                "accuracy_score": 0.0,
                "completeness_score": 0.0,
                "relevance_score": 0.0,
                "clarity_score": 0.0,
                "explanation": "Câu trả lời trống",
                "strengths": [],
                "weaknesses": ["Không có nội dung"],
                "suggestions": ["Cần cung cấp câu trả lời có nội dung"]
            }

        # Sử dụng AnswerQualityEvaluator nếu có sẵn
        if self.answer_quality_evaluator:
            try:
                return self.answer_quality_evaluator.evaluate(answer, query)
            except Exception as e:
                self.logger.warning("Lỗi khi sử dụng AnswerQualityEvaluator: %s", str(e))

        # Fallback: Đánh giá cơ bản
        # Đánh giá các khía cạnh
        accuracy_score = self._evaluate_accuracy(answer, query) if query else 0.5
        completeness_score = self._evaluate_completeness(answer, query).get('completeness_score', 0.5)
        relevance_score = self._evaluate_relevance(answer, query).get('relevance_score', 0.5) if query else 0.5
        clarity_score = self._evaluate_clarity(answer)

        # Tính điểm tổng thể
        quality_score = (accuracy_score + completeness_score + relevance_score + clarity_score) / 4

        # Phân tích điểm mạnh và yếu
        strengths, weaknesses, suggestions = self._analyze_answer_quality(
            accuracy_score, completeness_score, relevance_score, clarity_score
        )

        return {
            "quality_score": quality_score,
            "accuracy_score": accuracy_score,
            "completeness_score": completeness_score,
            "relevance_score": relevance_score,
            "clarity_score": clarity_score,
            "explanation": self._generate_quality_explanation(quality_score),
            "strengths": strengths,
            "weaknesses": weaknesses,
            "suggestions": suggestions
        }

    def _combine_vietnamese_diacritic(self, vowel: str, diacritic: str) -> str:
        """
        Kết hợp nguyên âm và dấu trong tiếng Việt.

        Args:
            vowel: Nguyên âm
            diacritic: Dấu

        Returns:
            Ký tự có dấu
        """
        # Bảng ánh xạ các nguyên âm không dấu sang các nguyên âm có dấu
        # Dạng: {nguyên_âm: {dấu: ký_tự_có_dấu}}
        diacritic_map = {
            'a': {
                'grave': 'à', 'acute': 'á', 'hook': 'ả', 'tilde': 'ã', 'dot': 'ạ'
            },
            'ă': {
                'grave': 'ằ', 'acute': 'ắ', 'hook': 'ẳ', 'tilde': 'ẵ', 'dot': 'ặ'
            },
            'â': {
                'grave': 'ầ', 'acute': 'ấ', 'hook': 'ẩ', 'tilde': 'ẫ', 'dot': 'ậ'
            },
            'e': {
                'grave': 'è', 'acute': 'é', 'hook': 'ẻ', 'tilde': 'ẽ', 'dot': 'ẹ'
            },
            'ê': {
                'grave': 'ề', 'acute': 'ế', 'hook': 'ể', 'tilde': 'ễ', 'dot': 'ệ'
            },
            'i': {
                'grave': 'ì', 'acute': 'í', 'hook': 'ỉ', 'tilde': 'ĩ', 'dot': 'ị'
            },
            'o': {
                'grave': 'ò', 'acute': 'ó', 'hook': 'ỏ', 'tilde': 'õ', 'dot': 'ọ'
            },
            'ô': {
                'grave': 'ồ', 'acute': 'ố', 'hook': 'ổ', 'tilde': 'ỗ', 'dot': 'ộ'
            },
            'ơ': {
                'grave': 'ờ', 'acute': 'ớ', 'hook': 'ở', 'tilde': 'ỡ', 'dot': 'ợ'
            },
            'u': {
                'grave': 'ù', 'acute': 'ú', 'hook': 'ủ', 'tilde': 'ũ', 'dot': 'ụ'
            },
            'ư': {
                'grave': 'ừ', 'acute': 'ứ', 'hook': 'ử', 'tilde': 'ữ', 'dot': 'ự'
            },
            'y': {
                'grave': 'ỳ', 'acute': 'ý', 'hook': 'ỷ', 'tilde': 'ỹ', 'dot': 'ỵ'
            }
        }

        # Xử lý chữ in hoa
        is_upper = vowel.isupper()
        vowel_lower = vowel.lower()

        # Kiểm tra nguyên âm có trong ánh xạ không
        if vowel_lower not in diacritic_map:
            return vowel  # Trả về nguyên âm gốc nếu không tìm thấy

        # Kiểm tra dấu có trong ánh xạ không
        if diacritic not in diacritic_map[vowel_lower]:
            return vowel  # Trả về nguyên âm gốc nếu không tìm thấy

        # Lấy ký tự có dấu
        result = diacritic_map[vowel_lower][diacritic]

        # Trả về dạng in hoa nếu nguyên âm ban đầu là in hoa
        return result.upper() if is_upper else result

    def _analyze_question_type(self, query: str) -> str:
        """
        Phân tích loại câu hỏi.

        Args:
            query: Câu hỏi cần phân tích

        Returns:
            Loại câu hỏi
        """
        query_lower = query.lower()

        # Câu hỏi định nghĩa
        if any(pattern in query_lower for pattern in ['là gì', 'what is', 'define', 'definition']):
            return "definition"

        # Câu hỏi so sánh
        if any(pattern in query_lower for pattern in ['so sánh', 'compare', 'vs', 'versus', 'khác nhau']):
            return "comparison"

        # Câu hỏi hướng dẫn
        if any(pattern in query_lower for pattern in ['làm thế nào', 'how to', 'cách', 'phương pháp']):
            return "how_to"

        # Câu hỏi nguyên nhân
        if any(pattern in query_lower for pattern in ['tại sao', 'why', 'nguyên nhân', 'lý do']):
            return "causal"

        # Câu hỏi danh sách
        if any(pattern in query_lower for pattern in ['danh sách', 'list', 'examples', 'ví dụ']):
            return "list"

        # Câu hỏi thông tin
        return "informational"

    def _analyze_question_characteristics(self, query: str) -> List[str]:
        """
        Phân tích đặc điểm của câu hỏi.

        Args:
            query: Câu hỏi cần phân tích

        Returns:
            Danh sách các đặc điểm
        """
        characteristics = []
        query_lower = query.lower()

        # Kiểm tra độ dài
        word_count = len(query.split())
        if word_count > 15:
            characteristics.append("Câu hỏi dài")
        elif word_count < 5:
            characteristics.append("Câu hỏi ngắn")

        # Kiểm tra ngôn ngữ
        if self._is_vietnamese_text(query):
            characteristics.append("Tiếng Việt")
        else:
            characteristics.append("Tiếng Anh")

        # Kiểm tra tính phức tạp
        if any(word in query_lower for word in ['analyze', 'evaluate', 'phân tích', 'đánh giá']):
            characteristics.append("Yêu cầu phân tích")

        if '?' in query:
            characteristics.append("Câu hỏi trực tiếp")

        return characteristics

    def _recommend_search_strategy(self, complexity_score: float, question_type: str) -> Dict[str, Any]:
        """
        Đề xuất chiến lược tìm kiếm dựa trên độ phức tạp và loại câu hỏi.

        Args:
            complexity_score: Điểm phức tạp
            question_type: Loại câu hỏi

        Returns:
            Chiến lược tìm kiếm được đề xuất
        """
        strategy = {
            "search_method": "standard",
            "max_depth": 1,
            "max_pages": 5,
            "decompose_query": False,
            "use_multiple_engines": False,
            "use_deep_crawl": False
        }

        # Điều chỉnh dựa trên độ phức tạp
        if complexity_score > 0.7:
            strategy["search_method"] = "adaptive"
            strategy["max_depth"] = 3
            strategy["max_pages"] = 15
            strategy["decompose_query"] = True
            strategy["use_multiple_engines"] = True
            strategy["use_deep_crawl"] = True
        elif complexity_score > 0.4:
            strategy["max_depth"] = 2
            strategy["max_pages"] = 10
            strategy["use_multiple_engines"] = True

        # Điều chỉnh dựa trên loại câu hỏi
        if question_type in ["comparison", "causal"]:
            strategy["max_pages"] = max(strategy["max_pages"], 10)
            strategy["use_multiple_engines"] = True
        elif question_type == "definition":
            strategy["max_pages"] = 5
        elif question_type == "how_to":
            strategy["max_pages"] = 8

        return strategy

    def _evaluate_accuracy(self, answer: str, query: str) -> float:
        """
        Đánh giá tính chính xác của câu trả lời.

        Args:
            answer: Câu trả lời
            query: Câu hỏi gốc

        Returns:
            Điểm tính chính xác (0-1)
        """
        if not answer or not query:
            return 0.0

        # Kiểm tra mâu thuẫn nội bộ
        contradictions = self._check_internal_contradictions(answer)

        # Đếm số từ khóa quan trọng từ câu hỏi
        keywords = self._extract_keywords(query)
        keyword_coverage = sum(1 for kw in keywords if kw.lower() in answer.lower()) / max(1, len(keywords))

        # Tính điểm tính chính xác dựa trên các yếu tố
        base_score = 0.7  # Điểm cơ bản
        contradiction_penalty = min(0.4, len(contradictions) * 0.1)  # Trừ điểm cho mâu thuẫn
        keyword_bonus = keyword_coverage * 0.3  # Cộng điểm cho việc đề cập từ khóa

        accuracy_score = base_score - contradiction_penalty + keyword_bonus
        return max(0.0, min(1.0, accuracy_score))

    def _evaluate_clarity(self, answer: str) -> float:
        """
        Đánh giá độ rõ ràng của câu trả lời.

        Args:
            answer: Câu trả lời

        Returns:
            Điểm độ rõ ràng (0-1)
        """
        if not answer:
            return 0.0

        # Tính độ dài câu trung bình
        sentences = re.split(r'[.!?]+', answer)
        sentences = [s.strip() for s in sentences if s.strip()]

        if not sentences:
            return 0.0

        avg_sentence_length = sum(len(s.split()) for s in sentences) / len(sentences)

        # Điểm dựa trên độ dài câu (câu quá dài hoặc quá ngắn đều không tốt)
        if 10 <= avg_sentence_length <= 20:
            length_score = 1.0
        elif 5 <= avg_sentence_length < 10 or 20 < avg_sentence_length <= 30:
            length_score = 0.7
        else:
            length_score = 0.4

        # Kiểm tra cấu trúc
        structure_score = 0.5
        if len(sentences) > 1:
            structure_score += 0.2
        if any(word in answer.lower() for word in ['đầu tiên', 'thứ hai', 'cuối cùng', 'first', 'second', 'finally']):
            structure_score += 0.3

        return min(1.0, (length_score + structure_score) / 2)

    def _check_internal_contradictions(self, text: str) -> List[str]:
        """
        Kiểm tra mâu thuẫn nội bộ trong văn bản.

        Args:
            text: Văn bản cần kiểm tra

        Returns:
            Danh sách các mâu thuẫn tìm thấy
        """
        contradictions = []

        # Các cặp từ mâu thuẫn
        contradiction_pairs = [
            (['có', 'yes', 'true', 'đúng'], ['không', 'no', 'false', 'sai']),
            (['tăng', 'increase', 'rise'], ['giảm', 'decrease', 'fall']),
            (['lớn', 'big', 'large'], ['nhỏ', 'small', 'little']),
            (['nhanh', 'fast', 'quick'], ['chậm', 'slow']),
            (['tốt', 'good', 'positive'], ['xấu', 'bad', 'negative'])
        ]

        text_lower = text.lower()
        for positive_words, negative_words in contradiction_pairs:
            has_positive = any(word in text_lower for word in positive_words)
            has_negative = any(word in text_lower for word in negative_words)

            if has_positive and has_negative:
                contradictions.append(f"Mâu thuẫn giữa {positive_words[0]} và {negative_words[0]}")

        return contradictions

    def _analyze_answer_quality(self, accuracy_score: float, completeness_score: float,
                               relevance_score: float, clarity_score: float) -> Tuple[List[str], List[str], List[str]]:
        """
        Phân tích điểm mạnh, yếu và đề xuất cải thiện.

        Args:
            accuracy_score: Điểm tính chính xác
            completeness_score: Điểm tính đầy đủ
            relevance_score: Điểm độ liên quan
            clarity_score: Điểm độ rõ ràng

        Returns:
            Tuple gồm (strengths, weaknesses, suggestions)
        """
        strengths = []
        weaknesses = []
        suggestions = []

        # Phân tích điểm mạnh
        if accuracy_score >= 0.8:
            strengths.append("Thông tin chính xác cao")
        if completeness_score >= 0.8:
            strengths.append("Nội dung đầy đủ")
        if relevance_score >= 0.8:
            strengths.append("Liên quan chặt chẽ với câu hỏi")
        if clarity_score >= 0.8:
            strengths.append("Trình bày rõ ràng")

        # Phân tích điểm yếu và đề xuất
        if accuracy_score < 0.6:
            weaknesses.append("Tính chính xác thấp")
            suggestions.append("Kiểm tra lại thông tin và nguồn tham khảo")

        if completeness_score < 0.6:
            weaknesses.append("Thiếu thông tin")
            suggestions.append("Bổ sung thêm chi tiết và khía cạnh khác")

        if relevance_score < 0.6:
            weaknesses.append("Ít liên quan đến câu hỏi")
            suggestions.append("Tập trung vào nội dung trả lời trực tiếp câu hỏi")

        if clarity_score < 0.6:
            weaknesses.append("Trình bày chưa rõ ràng")
            suggestions.append("Cải thiện cấu trúc và cách diễn đạt")

        return strengths, weaknesses, suggestions

    def _generate_quality_explanation(self, quality_score: float) -> str:
        """
        Tạo giải thích cho điểm chất lượng.

        Args:
            quality_score: Điểm chất lượng

        Returns:
            Giải thích về điểm chất lượng
        """
        if quality_score >= 0.8:
            return "Câu trả lời có chất lượng cao với thông tin chính xác và đầy đủ"
        elif quality_score >= 0.6:
            return "Câu trả lời có chất lượng tốt nhưng có thể cải thiện thêm"
        elif quality_score >= 0.4:
            return "Câu trả lời có chất lượng trung bình, cần bổ sung thông tin"
        else:
            return "Câu trả lời có chất lượng thấp, cần cải thiện đáng kể"

    def _create_simple_answer(self, results: List[Dict[str, Any]], query: str) -> str:
        """
        Tạo câu trả lời đơn giản từ kết quả tìm kiếm.

        Args:
            results: Danh sách kết quả tìm kiếm
            query: Câu hỏi gốc

        Returns:
            Câu trả lời đơn giản
        """
        if not results:
            return f"Không tìm thấy thông tin về '{query}'. Vui lòng thử với từ khóa khác."

        # Lấy thông tin từ kết quả đầu tiên
        first_result = results[0]
        title = first_result.get('title', '')
        snippet = first_result.get('snippet', '')
        source = first_result.get('source', first_result.get('url', ''))

        # Tạo câu trả lời dựa trên loại câu hỏi
        question_type = self._analyze_question_type(query)

        if question_type == "definition":
            answer = f"Theo thông tin tìm được, {query} "
            if snippet:
                # Tìm định nghĩa trong snippet
                definition_start = snippet.lower().find(query.lower())
                if definition_start != -1:
                    answer += snippet[definition_start:].split('.')[0] + "."
                else:
                    answer += f"được mô tả như sau: {snippet[:200]}..."
            else:
                answer += f"có thông tin chi tiết tại {title}."

        elif question_type == "how_to":
            answer = f"Để {query.replace('làm thế nào', '').replace('how to', '').strip()}, "
            if snippet:
                answer += f"theo hướng dẫn: {snippet[:200]}..."
            else:
                answer += f"bạn có thể tham khảo hướng dẫn chi tiết tại {title}."

        else:
            # Câu trả lời chung
            answer = f"Về {query}, "
            if snippet:
                answer += f"{snippet[:200]}..."
            else:
                answer += f"có thông tin chi tiết tại {title}."

        # Thêm nguồn
        if source and not source.startswith('http'):
            answer += f" (Nguồn: {source})"

        return answer

    def _perform_adaptive_search(self, query: str, num_results: int) -> List[Dict[str, Any]]:
        """
        Thực hiện tìm kiếm thích ứng cho câu hỏi phức tạp.

        Args:
            query: Truy vấn tìm kiếm
            num_results: Số lượng kết quả mong muốn

        Returns:
            Danh sách kết quả tìm kiếm
        """
        results = []

        # Phân rã truy vấn thành các phần nhỏ hơn
        sub_queries = self._decompose_query(query)

        if sub_queries and len(sub_queries) > 1:
            # Tìm kiếm cho từng truy vấn con
            for sub_query in sub_queries[:3]:  # Giới hạn 3 truy vấn con
                sub_results = self._perform_standard_search(sub_query, num_results // len(sub_queries))
                results.extend(sub_results)
        else:
            # Tìm kiếm với truy vấn gốc
            results = self._perform_standard_search(query, num_results)

        # Loại bỏ trùng lặp
        seen_urls = set()
        unique_results = []
        for result in results:
            url = result.get('url', '')
            if url and url not in seen_urls:
                seen_urls.add(url)
                unique_results.append(result)

        return unique_results[:num_results]

    def _perform_standard_search(self, query: str, num_results: int) -> List[Dict[str, Any]]:
        """
        Thực hiện tìm kiếm tiêu chuẩn.

        Args:
            query: Truy vấn tìm kiếm
            num_results: Số lượng kết quả mong muốn

        Returns:
            Danh sách kết quả tìm kiếm
        """
        # Đây là implementation mẫu - trong thực tế sẽ tích hợp với search engine thực
        results = []

        for i in range(min(num_results, 5)):
            result = {
                "title": f"Kết quả {i+1} cho: {query}",
                "url": f"https://example{i+1}.com/search?q={query}",
                "snippet": f"Thông tin về {query}. Đây là kết quả tìm kiếm số {i+1} với nội dung liên quan đến truy vấn.",
                "source": f"example{i+1}.com",
                "credibility_score": 0.7 + (i * 0.05),  # Điểm tin cậy giả lập
                "timestamp": datetime.now().isoformat()
            }
            results.append(result)

        return results

    def _add_content_to_results(self, results: List[Dict[str, Any]], query: str) -> List[Dict[str, Any]]:
        """
        Thêm nội dung đầy đủ vào kết quả tìm kiếm.

        Args:
            results: Danh sách kết quả tìm kiếm
            query: Truy vấn gốc

        Returns:
            Danh sách kết quả đã được bổ sung nội dung
        """
        enhanced_results = []

        for result in results:
            enhanced_result = result.copy()
            url = result.get('url', '')

            if url and not result.get('content'):
                try:
                    # Tải nội dung từ URL
                    content_data = self._fetch_url(url)
                    if content_data and 'content' in content_data:
                        enhanced_result['content'] = content_data['content']
                        enhanced_result['content_length'] = len(content_data['content'])

                        # Xử lý nội dung tiếng Việt
                        if self._is_vietnamese_text(content_data['content']):
                            enhanced_result['content'] = self._improve_vietnamese_paragraphs(
                                enhanced_result['content']
                            )
                            enhanced_result['language'] = 'vi'
                        else:
                            enhanced_result['language'] = 'en'

                        # Trích xuất thông tin bổ sung
                        enhanced_result['keywords'] = self._extract_keywords_from_content(
                            content_data['content']
                        )
                        enhanced_result['summary'] = self._create_content_summary(
                            content_data['content'], query
                        )

                except Exception as e:
                    self.logger.warning("Không thể tải nội dung từ %s: %s", url, str(e))
                    enhanced_result['content_error'] = str(e)

            enhanced_results.append(enhanced_result)

        return enhanced_results

    def check_content_disinformation(self, content: str, query: str = None) -> Dict[str, Any]:
        """
        Kiểm tra thông tin sai lệch trong nội dung.

        Args:
            content: Nội dung cần kiểm tra
            query: Truy vấn liên quan (tùy chọn)

        Returns:
            Kết quả kiểm tra thông tin sai lệch
        """
        result = {
            "is_disinformation": False,
            "confidence_score": 0.5,
            "warning_signs": [],
            "credibility_indicators": [],
            "fact_check_needed": False,
            "explanation": "",
            "recommendations": []
        }

        if not content or len(content.strip()) < 10:
            result["explanation"] = "Nội dung quá ngắn để đánh giá"
            return result

        content_lower = content.lower()
        warning_signs = []
        credibility_indicators = []

        # Kiểm tra các dấu hiệu cảnh báo
        warning_patterns = [
            "tin độc quyền", "exclusive news", "bí mật được tiết lộ", "secret revealed",
            "chính phủ che giấu", "government cover-up", "âm mưu", "conspiracy",
            "họ không muốn bạn biết", "they don't want you to know",
            "sự thật được che đậy", "truth hidden", "fake news", "tin giả"
        ]

        for pattern in warning_patterns:
            if pattern in content_lower:
                warning_signs.append(f"Phát hiện cụm từ nghi ngờ: '{pattern}'")

        # Kiểm tra các chỉ số tin cậy
        credibility_patterns = [
            "nghiên cứu cho thấy", "research shows", "theo báo cáo", "according to report",
            "chuyên gia khẳng định", "experts confirm", "dữ liệu chính thức", "official data",
            "nguồn tin đáng tin cậy", "reliable source"
        ]

        for pattern in credibility_patterns:
            if pattern in content_lower:
                credibility_indicators.append(f"Tìm thấy chỉ số tin cậy: '{pattern}'")

        # Kiểm tra cấu trúc và ngôn ngữ
        sentences = re.split(r'[.!?]+', content)
        sentences = [s.strip() for s in sentences if s.strip()]

        # Kiểm tra câu cảm thán quá nhiều
        exclamation_count = content.count('!')
        if exclamation_count > len(sentences) * 0.3:
            warning_signs.append("Sử dụng quá nhiều dấu cảm thán")

        # Kiểm tra từ ngữ cực đoan
        extreme_words = [
            "tuyệt đối", "hoàn toàn", "không bao giờ", "luôn luôn", "chắc chắn 100%",
            "absolutely", "completely", "never", "always", "100% certain"
        ]

        extreme_count = sum(1 for word in extreme_words if word in content_lower)
        if extreme_count > 3:
            warning_signs.append("Sử dụng nhiều từ ngữ cực đoan")

        # Tính điểm tin cậy
        warning_score = len(warning_signs) * 0.2
        credibility_score = len(credibility_indicators) * 0.15

        confidence_score = max(0.0, min(1.0, 0.5 + credibility_score - warning_score))

        # Xác định có phải thông tin sai lệch không
        is_disinformation = confidence_score < 0.3 or len(warning_signs) >= 3

        result.update({
            "is_disinformation": is_disinformation,
            "confidence_score": confidence_score,
            "warning_signs": warning_signs,
            "credibility_indicators": credibility_indicators,
            "fact_check_needed": len(warning_signs) > 0 or confidence_score < 0.6
        })

        # Tạo giải thích
        if is_disinformation:
            result["explanation"] = "Nội dung có dấu hiệu thông tin sai lệch"
            result["recommendations"] = [
                "Kiểm tra nguồn gốc thông tin",
                "Tìm kiếm thông tin từ nhiều nguồn khác nhau",
                "Xác minh với các tổ chức fact-check"
            ]
        elif confidence_score < 0.6:
            result["explanation"] = "Nội dung cần được kiểm chứng thêm"
            result["recommendations"] = [
                "Tìm thêm nguồn tham khảo",
                "Kiểm tra tính cập nhật của thông tin"
            ]
        else:
            result["explanation"] = "Nội dung có vẻ đáng tin cậy"
            result["recommendations"] = [
                "Vẫn nên kiểm tra nguồn gốc",
                "So sánh với thông tin từ nguồn khác"
            ]

        return result

    def _extract_keywords_from_content(self, content: str) -> List[str]:
        """
        Trích xuất từ khóa từ nội dung.

        Args:
            content: Nội dung cần trích xuất từ khóa

        Returns:
            Danh sách từ khóa
        """
        if not content:
            return []

        # Tách từ và loại bỏ stopwords
        words = re.findall(r'\b\w+\b', content.lower())

        # Stopwords tiếng Việt và tiếng Anh
        stopwords = {
            'và', 'của', 'cho', 'là', 'để', 'trong', 'được', 'với', 'có', 'không',
            'người', 'những', 'này', 'khi', 'từ', 'một', 'bạn', 'đã', 'các', 'đến',
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'from', 'up', 'about', 'into', 'through', 'during'
        }

        # Lọc từ khóa
        keywords = [word for word in words if len(word) > 3 and word not in stopwords]

        # Đếm tần suất và lấy từ khóa phổ biến nhất
        word_freq = Counter(keywords)
        return [word for word, freq in word_freq.most_common(10)]

    def _create_content_summary(self, content: str, query: str) -> str:
        """
        Tạo tóm tắt nội dung liên quan đến truy vấn.

        Args:
            content: Nội dung cần tóm tắt
            query: Truy vấn liên quan

        Returns:
            Tóm tắt nội dung
        """
        if not content:
            return ""

        # Tách thành các câu
        sentences = re.split(r'[.!?]+', content)
        sentences = [s.strip() for s in sentences if s.strip() and len(s) > 20]

        if not sentences:
            return content[:200] + "..." if len(content) > 200 else content

        # Tìm câu có liên quan nhất đến truy vấn
        query_words = set(query.lower().split())
        sentence_scores = []

        for sentence in sentences:
            sentence_words = set(sentence.lower().split())
            overlap = len(query_words.intersection(sentence_words))
            score = overlap / len(query_words) if query_words else 0
            sentence_scores.append((sentence, score))

        # Sắp xếp theo điểm số và lấy 2-3 câu tốt nhất
        sentence_scores.sort(key=lambda x: x[1], reverse=True)
        top_sentences = [s[0] for s in sentence_scores[:3]]

        summary = '. '.join(top_sentences)
        return summary[:300] + "..." if len(summary) > 300 else summary

    def _perform_deep_crawl(self, url: str, max_depth: int = 2, max_pages: int = 10,
                           same_domain_only: bool = True) -> Dict[str, Any]:
        """
        Thực hiện deep crawl một trang web.

        Args:
            url: URL gốc để bắt đầu crawl
            max_depth: Độ sâu tối đa
            max_pages: Số trang tối đa
            same_domain_only: Chỉ crawl trong cùng domain

        Returns:
            Kết quả deep crawl
        """
        result = {
            "start_url": url,
            "pages_crawled": 0,
            "pages": [],
            "errors": [],
            "total_content_length": 0,
            "crawl_time": 0,
            "status": "completed"
        }

        start_time = time.time()

        try:
            # Sử dụng AdaptiveCrawlerIntegration nếu có sẵn
            if hasattr(self, '_adaptive_crawler_integration') and self._adaptive_crawler_integration:
                try:
                    crawl_result = self._adaptive_crawler_integration.crawl_website(
                        url, max_depth=max_depth, max_pages=max_pages
                    )
                    if crawl_result:
                        result.update(crawl_result)
                        result["crawl_time"] = time.time() - start_time
                        return result
                except Exception as e:
                    self.logger.warning("Lỗi khi sử dụng AdaptiveCrawlerIntegration: %s", str(e))

            # Fallback: Crawl đơn giản
            visited_urls = set()
            to_visit = [(url, 0)]  # (url, depth)
            base_domain = self._extract_domain(url) if same_domain_only else None

            while to_visit and len(result["pages"]) < max_pages:
                current_url, depth = to_visit.pop(0)

                if current_url in visited_urls or depth > max_depth:
                    continue

                visited_urls.add(current_url)

                try:
                    # Tải nội dung trang
                    page_data = self._fetch_url(current_url)
                    if page_data and 'content' in page_data:
                        page_info = {
                            "url": current_url,
                            "title": page_data.get('title', ''),
                            "content": page_data['content'],
                            "content_length": len(page_data['content']),
                            "depth": depth,
                            "links_found": [],
                            "timestamp": datetime.now().isoformat()
                        }

                        # Trích xuất links nếu chưa đạt độ sâu tối đa
                        if depth < max_depth:
                            links = self._extract_links_from_content(page_data['content'], current_url)
                            page_info["links_found"] = links

                            # Thêm links vào danh sách cần visit
                            for link in links:
                                if same_domain_only:
                                    link_domain = self._extract_domain(link)
                                    if link_domain == base_domain:
                                        to_visit.append((link, depth + 1))
                                else:
                                    to_visit.append((link, depth + 1))

                        result["pages"].append(page_info)
                        result["total_content_length"] += len(page_data['content'])
                        result["pages_crawled"] += 1

                        # Thêm delay để tránh overload server
                        time.sleep(1)

                except Exception as e:
                    error_info = {
                        "url": current_url,
                        "error": str(e),
                        "depth": depth,
                        "timestamp": datetime.now().isoformat()
                    }
                    result["errors"].append(error_info)
                    self.logger.warning("Lỗi khi crawl %s: %s", current_url, str(e))

            result["crawl_time"] = time.time() - start_time

        except Exception as e:
            result["status"] = "failed"
            result["error"] = str(e)
            result["crawl_time"] = time.time() - start_time
            self.logger.error("Lỗi trong quá trình deep crawl: %s", str(e))

        return result

    def _extract_links_from_content(self, content: str, base_url: str) -> List[str]:
        """
        Trích xuất links từ nội dung HTML.

        Args:
            content: Nội dung HTML
            base_url: URL gốc để resolve relative links

        Returns:
            Danh sách các links
        """
        links = []

        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(content, 'html.parser')

            # Tìm tất cả thẻ a có href
            for link_tag in soup.find_all('a', href=True):
                href = link_tag['href']

                # Bỏ qua các link không hợp lệ
                if href.startswith('#') or href.startswith('mailto:') or href.startswith('tel:'):
                    continue

                # Resolve relative URLs
                if href.startswith('http'):
                    full_url = href
                else:
                    from urllib.parse import urljoin
                    full_url = urljoin(base_url, href)

                if full_url not in links:
                    links.append(full_url)

        except Exception as e:
            self.logger.warning("Lỗi khi trích xuất links: %s", str(e))

        return links[:20]  # Giới hạn số lượng links

    def analyze_content_with_llm(self, content: str, query: str = None) -> Dict[str, Any]:
        """
        Phân tích nội dung bằng LLM.

        Args:
            content: Nội dung cần phân tích
            query: Truy vấn liên quan (tùy chọn)

        Returns:
            Kết quả phân tích từ LLM
        """
        result = {
            "analysis_available": False,
            "summary": "",
            "key_points": [],
            "sentiment": "neutral",
            "topics": [],
            "relevance_to_query": 0.5,
            "confidence": 0.5,
            "explanation": ""
        }

        # Kiểm tra xem có LLM integration không
        if hasattr(self, '_llm_analyzer') and self._llm_analyzer:
            try:
                # Sử dụng LLM analyzer
                analysis = self._llm_analyzer.analyze_content(content, query)
                if analysis:
                    result.update(analysis)
                    result["analysis_available"] = True
                    return result
            except Exception as e:
                self.logger.warning("Lỗi khi sử dụng LLM analyzer: %s", str(e))

        # Fallback: Phân tích cơ bản không dùng LLM
        if not content or len(content.strip()) < 10:
            result["explanation"] = "Nội dung quá ngắn để phân tích"
            return result

        # Tạo summary đơn giản
        sentences = re.split(r'[.!?]+', content)
        sentences = [s.strip() for s in sentences if s.strip() and len(s) > 20]

        if sentences:
            # Lấy 2-3 câu đầu tiên làm summary
            result["summary"] = '. '.join(sentences[:3])

            # Trích xuất key points (câu có từ khóa quan trọng)
            key_indicators = ['quan trọng', 'chính', 'đặc biệt', 'cần lưu ý', 'important', 'key', 'main', 'significant']
            key_points = []

            for sentence in sentences:
                if any(indicator in sentence.lower() for indicator in key_indicators):
                    key_points.append(sentence.strip())

            result["key_points"] = key_points[:5]

        # Phân tích sentiment đơn giản
        positive_words = ['tốt', 'hay', 'xuất sắc', 'tuyệt vời', 'good', 'great', 'excellent', 'amazing']
        negative_words = ['xấu', 'tệ', 'kém', 'thất bại', 'bad', 'terrible', 'poor', 'failure']

        content_lower = content.lower()
        positive_count = sum(1 for word in positive_words if word in content_lower)
        negative_count = sum(1 for word in negative_words if word in content_lower)

        if positive_count > negative_count:
            result["sentiment"] = "positive"
        elif negative_count > positive_count:
            result["sentiment"] = "negative"
        else:
            result["sentiment"] = "neutral"

        # Tính relevance với query nếu có
        if query:
            query_words = set(query.lower().split())
            content_words = set(content.lower().split())
            overlap = len(query_words.intersection(content_words))
            result["relevance_to_query"] = overlap / len(query_words) if query_words else 0

        result["confidence"] = 0.6  # Confidence thấp hơn vì không dùng LLM
        result["explanation"] = "Phân tích cơ bản không sử dụng LLM"

        return result

    def _decode_html_entity(self, entity: str) -> str:
        """
        Giải mã HTML entity.

        Args:
            entity: HTML entity cần giải mã

        Returns:
            Ký tự tương ứng
        """
        # Danh sách các HTML entity phổ biến
        html_entities = {
            '&nbsp;': ' ', '&lt;': '<', '&gt;': '>', '&amp;': '&', '&quot;': '"', '&apos;': "'",
            '&cent;': '¢', '&pound;': '£', '&yen;': '¥', '&euro;': '€', '&copy;': '©', '&reg;': '®',

            # Các dấu tiếng Việt
            '&agrave;': 'à', '&aacute;': 'á', '&acirc;': 'â', '&atilde;': 'ã', '&auml;': 'ä', '&aring;': 'å',
            '&aelig;': 'æ', '&ccedil;': 'ç', '&egrave;': 'è', '&eacute;': 'é', '&ecirc;': 'ê', '&euml;': 'ë',
            '&igrave;': 'ì', '&iacute;': 'í', '&icirc;': 'î', '&iuml;': 'ï', '&eth;': 'ð', '&ntilde;': 'ñ',
            '&ograve;': 'ò', '&oacute;': 'ó', '&ocirc;': 'ô', '&otilde;': 'õ', '&ouml;': 'ö',
            '&oslash;': 'ø', '&ugrave;': 'ù', '&uacute;': 'ú', '&ucirc;': 'û', '&uuml;': 'ü',
            '&yacute;': 'ý', '&thorn;': 'þ', '&yuml;': 'ÿ', '&fnof;': 'ƒ',

            # Các entity tiếng Việt đặc biệt
            '&#273;': 'đ', '&#272;': 'Đ',
            '&#432;': 'ư', '&#431;': 'Ư',
            '&#417;': 'ơ', '&#416;': 'Ơ'
        }

        # Kiểm tra có phải entity dạng tên không
        if entity in html_entities:
            return html_entities[entity]

        # Kiểm tra có phải entity dạng số không (&#123;)
        if entity.startswith('&#'):
            try:
                # Loại bỏ '&#' và ';' rồi chuyển thành số
                code_point = int(entity[2:-1])
                # Chuyển mã số thành ký tự Unicode
                return chr(code_point)
            except (ValueError, OverflowError):
                return entity  # Trả về entity gốc nếu không thể chuyển đổi

        # Trả về entity gốc nếu không phù hợp với bất kỳ trường hợp nào
        return entity

    def _improve_vietnamese_paragraphs(self, content: str) -> str:
        """
        Cải thiện đoạn văn tiếng Việt, đảm bảo đúng cú pháp và định dạng.

        Args:
            content: Nội dung cần cải thiện

        Returns:
            Nội dung đã được cải thiện
        """
        if not content or not self._is_vietnamese_text(content):
            return content

        # Sửa lỗi mã hóa tiếng Việt
        content = self._fix_vietnamese_encoding(content)

        # Loại bỏ khoảng trắng dư thừa
        content = re.sub(r'\s+', ' ', content)

        # Sửa lỗi dấu câu
        content = re.sub(r'(?<=[.,!?]) (?=[A-ZÁÀẢÃẠĂẮẰẲẴẶÂẤẦẨẪẬÉÈẺẼẸÊẾỀỂỄỆÍÌỈĨỊÓÒỎÕỌÔỐỒỔỖỘƠỚỜỞỠỢÚÙỦŨỤƯỨỪỬỮỰÝỲỶỸỴĐ])', '. ', content)

        # Chuẩn hóa dấu câu (đảm bảo khoảng trắng sau dấu câu)
        content = re.sub(r'([.,!?;:])([^\s])', r'\1 \2', content)

        # Sửa lỗi viết hoa
        sentences = re.split(r'([.!?] )', content)
        result = []
        for i in range(0, len(sentences), 2):
            if i+1 < len(sentences):
                sentence = sentences[i]
                if sentence and sentence[0].islower():
                    sentence = sentence[0].upper() + sentence[1:]
                result.append(sentence + sentences[i+1])
            else:
                result.append(sentences[i])

        content = ''.join(result)

        # Chia thành các đoạn văn hợp lý
        paragraphs = content.split('\n')
        improved_paragraphs = []

        for p in paragraphs:
            # Loại bỏ đoạn quá ngắn hoặc không có ý nghĩa
            if len(p.strip()) < 10 or not any(c.isalpha() for c in p):
                continue

            improved_paragraphs.append(p.strip())

        # Kết hợp lại thành văn bản hoàn chỉnh
        return '\n\n'.join(improved_paragraphs)

    def _remove_vietnamese_boilerplate(self, content: str) -> str:
        """
        Loại bỏ nội dung trùng lặp, không cần thiết trong văn bản tiếng Việt.

        Args:
            content: Nội dung cần xử lý

        Returns:
            Nội dung đã được làm sạch
        """
        if not content:
            return ""

        # Danh sách các mẫu boilerplate phổ biến trong trang web tiếng Việt
        boilerplate_patterns = [
            r'(?i)bản quyền thuộc về.*',
            r'(?i)copyright.*\d{4}.*',
            r'(?i)all rights reserved.*',
            r'(?i)tất cả các quyền được bảo lưu.*',
            r'(?i)liên hệ quảng cáo.*',
            r'(?i)liên hệ:.*@.*\.(com|vn|net)',
            r'(?i)địa chỉ:.*quận.*thành phố.*',
            r'(?i)theo dõi chúng tôi trên.*',
            r'(?i)follow us on.*',
            r'(?i)hotline:.*\d{10}',
            r'(?i)giấy phép.*số.*cục báo chí.*',
            r'(?i)chịu trách nhiệm quản lý nội dung:.*',
            r'(?i)không sao chép nội dung này dưới mọi hình thức.*',
            r'(?i)nội dung được sao chép không được quá.*%.*',
            r'(?i)đăng ký nhận tin.*',
            r'(?i)đăng ký.*nhận bản tin.*',
            r'(?i)email của bạn sẽ không.*công khai.*',
            r'(?i)để lại bình luận.*',
            r'(?i)thông báo về.*',
            r'(?i)trang chủ.*giới thiệu.*liên hệ.*',
            r'(?i)tin nóng.*tin mới.*'
        ]

        # Áp dụng các mẫu để loại bỏ boilerplate
        for pattern in boilerplate_patterns:
            content = re.sub(pattern, '', content)

        # Loại bỏ các dòng quá ngắn (thường là menu, tiêu đề, v.v.)
        lines = content.split('\n')
        cleaned_lines = [line for line in lines if len(line.strip()) > 20 or (len(line.strip()) > 0 and sum(c.isalpha() for c in line) / len(line) > 0.7)]

        # Loại bỏ các dòng trùng lặp
        unique_lines = []
        for line in cleaned_lines:
            line_stripped = line.strip()
            if line_stripped and line_stripped not in unique_lines:
                unique_lines.append(line_stripped)

        # Kết hợp lại thành văn bản sạch
        return '\n'.join(unique_lines)

    def _remove_vietnamese_tones(self, text: str) -> str:
        """
        Loại bỏ dấu trong tiếng Việt.

        Args:
            text: Văn bản cần xử lý

        Returns:
            Văn bản không dấu
        """
        if not text:
            return ""

        # Bảng ánh xạ các ký tự có dấu sang không dấu
        tone_marks = {
            'à': 'a', 'á': 'a', 'ả': 'a', 'ã': 'a', 'ạ': 'a',
            'ă': 'a', 'ằ': 'a', 'ắ': 'a', 'ẳ': 'a', 'ẵ': 'a', 'ặ': 'a',
            'â': 'a', 'ầ': 'a', 'ấ': 'a', 'ẩ': 'a', 'ẫ': 'a', 'ậ': 'a',
            'è': 'e', 'é': 'e', 'ẻ': 'e', 'ẽ': 'e', 'ẹ': 'e',
            'ê': 'e', 'ề': 'e', 'ế': 'e', 'ể': 'e', 'ễ': 'e', 'ệ': 'e',
            'ì': 'i', 'í': 'i', 'ỉ': 'i', 'ĩ': 'i', 'ị': 'i',
            'ò': 'o', 'ó': 'o', 'ỏ': 'o', 'õ': 'o', 'ọ': 'o',
            'ô': 'o', 'ồ': 'o', 'ố': 'o', 'ổ': 'o', 'ỗ': 'o', 'ộ': 'o',
            'ơ': 'o', 'ờ': 'o', 'ớ': 'o', 'ở': 'o', 'ỡ': 'o', 'ợ': 'o',
            'ù': 'u', 'ú': 'u', 'ủ': 'u', 'ũ': 'u', 'ụ': 'u',
            'ư': 'u', 'ừ': 'u', 'ứ': 'u', 'ử': 'u', 'ữ': 'u', 'ự': 'u',
            'ỳ': 'y', 'ý': 'y', 'ỷ': 'y', 'ỹ': 'y', 'ỵ': 'y',
            'đ': 'd',
            'À': 'A', 'Á': 'A', 'Ả': 'A', 'Ã': 'A', 'Ạ': 'A',
            'Ă': 'A', 'Ằ': 'A', 'Ắ': 'A', 'Ẳ': 'A', 'Ẵ': 'A', 'Ặ': 'A',
            'Â': 'A', 'Ầ': 'A', 'Ấ': 'A', 'Ẩ': 'A', 'Ẫ': 'A', 'Ậ': 'A',
            'È': 'E', 'É': 'E', 'Ẻ': 'E', 'Ẽ': 'E', 'Ẹ': 'E',
            'Ê': 'E', 'Ề': 'E', 'Ế': 'E', 'Ể': 'E', 'Ễ': 'E', 'Ệ': 'E',
            'Ì': 'I', 'Í': 'I', 'Ỉ': 'I', 'Ĩ': 'I', 'Ị': 'I',
            'Ò': 'O', 'Ó': 'O', 'Ỏ': 'O', 'Õ': 'O', 'Ọ': 'O',
            'Ô': 'O', 'Ồ': 'O', 'Ố': 'O', 'Ổ': 'O', 'Ỗ': 'O', 'Ộ': 'O',
            'Ơ': 'O', 'Ờ': 'O', 'Ớ': 'O', 'Ở': 'O', 'Ỡ': 'O', 'Ợ': 'O',
            'Ù': 'U', 'Ú': 'U', 'Ủ': 'U', 'Ũ': 'U', 'Ụ': 'U',
            'Ư': 'U', 'Ừ': 'U', 'Ứ': 'U', 'Ử': 'U', 'Ữ': 'U', 'Ự': 'U',
            'Ỳ': 'Y', 'Ý': 'Y', 'Ỷ': 'Y', 'Ỹ': 'Y', 'Ỵ': 'Y',
            'Đ': 'D'
        }

        # Thực hiện thay thế
        for char, replace in tone_marks.items():
            text = text.replace(char, replace)

        return text

    def _extract_domain(self, url: str) -> str:
        """
        Trích xuất tên miền từ URL.

        Args:
            url: URL cần trích xuất tên miền

        Returns:
            Tên miền đã trích xuất hoặc chuỗi rỗng nếu không thể trích xuất
        """
        try:
            # Chuẩn hóa URL
            if not url.startswith(('http://', 'https://')):
                url = 'http://' + url

            # Phân tích URL
            parsed_url = urllib.parse.urlparse(url)
            domain = parsed_url.netloc

            # Loại bỏ www nếu có
            domain = re.sub(r'^www\.', '', domain)

            return domain.lower() if domain else ""
        except Exception as e:
            logger.warning("Không thể trích xuất domain từ URL %s: %s", url, str(e))
            return ""

    def _get_domain_credibility(self, domain: str) -> Optional[float]:
        """
        Đánh giá độ tin cậy của tên miền.

        Args:
            domain: Tên miền cần đánh giá

        Returns:
            Điểm tin cậy từ 0.0 đến 1.0, hoặc None nếu không xác định được
        """
        if not domain:
            return None

        # Danh sách các tên miền tin cậy cao (có thể mở rộng hoặc tải từ file)
        trusted_domains = {
            # Tin tức quốc tế
            'reuters.com': 0.95,
            'apnews.com': 0.95,
            'bbc.com': 0.90,
            'bbc.co.uk': 0.90,
            'npr.org': 0.88,
            'washingtonpost.com': 0.85,
            'nytimes.com': 0.85,
            'wsj.com': 0.85,
            'bloomberg.com': 0.87,
            'economist.com': 0.87,

            # Khoa học
            'nature.com': 0.92,
            'science.org': 0.92,
            'scientificamerican.com': 0.90,
            'nationalgeographic.com': 0.89,
            'smithsonianmag.com': 0.88,

            # Các tin tức tổng hợp
            'theguardian.com': 0.83,
            'ft.com': 0.86,
            'time.com': 0.80,
            'theatlantic.com': 0.82,
            'cnn.com': 0.75,

            # Nguồn học thuật và chính phủ
            'edu': 0.85,
            'gov': 0.85,
            'who.int': 0.90,
            'nih.gov': 0.90,
            'cdc.gov': 0.90,

            # Nguồn Việt Nam
            'vnexpress.net': 0.80,
            'tuoitre.vn': 0.80,
            'thanhnien.vn': 0.80,
            'vietnamnet.vn': 0.75,
            'dantri.com.vn': 0.75,
            'nhandan.vn': 0.80,
            'baomoi.com': 0.70,
            'vtv.vn': 0.85,
            'vov.vn': 0.80,
            'gov.vn': 0.85,
            'moh.gov.vn': 0.85,
            'moet.gov.vn': 0.85
        }

        # Danh sách các tên miền không tin cậy (có thể mở rộng hoặc tải từ file)
        untrusted_domains = {
            'theonion.com': 0.10,  # Satirical news
            'clickhole.com': 0.10,  # Satirical news
            'infowars.com': 0.15,
            'naturalnews.com': 0.15,
            'worldnewsdailyreport.com': 0.10,
            'empirenews.net': 0.10,
            'nationalreport.net': 0.10,
            'huzlers.com': 0.10,
            'thedcgazette.com': 0.15,
            'libertywriters.com': 0.15,
            'react365.com': 0.10
        }

        # Kiểm tra nếu domain trùng khớp chính xác với danh sách tin cậy
        if domain in trusted_domains:
            return trusted_domains[domain]

        # Kiểm tra nếu domain trùng khớp chính xác với danh sách không tin cậy
        if domain in untrusted_domains:
            return untrusted_domains[domain]

        # Kiểm tra nếu domain kết thúc bằng một trong các domain tin cậy
        for known_domain, score in trusted_domains.items():
            if domain.endswith(known_domain):
                return score

        # Kiểm tra nếu domain kết thúc bằng một trong các domain không tin cậy
        for known_domain, score in untrusted_domains.items():
            if domain.endswith(known_domain):
                return score

        # Nếu không tìm thấy, trả về None (không biết)
        return None

    def _calculate_query_complexity(self, query: str) -> float:
        """
        Tính toán độ phức tạp của truy vấn.

        Args:
            query: Truy vấn cần đánh giá

        Returns:
            Điểm độ phức tạp từ 0.0 đến 1.0
        """
        if not query:
            return 0.0

        complexity = 0.0

        # 1. Độ dài truy vấn
        words = query.split()
        word_count = len(words)

        if word_count <= 3:
            complexity += 0.1
        elif word_count <= 6:
            complexity += 0.3
        elif word_count <= 10:
            complexity += 0.5
        elif word_count <= 15:
            complexity += 0.7
        else:
            complexity += 0.9

        # 2. Số lượng mệnh đề (ước lượng thông qua dấu phẩy, liên từ)
        clause_indicators = [",", " and ", " or ", " but ", " because ", " since ",
                            " as ", " if ", " when ", " while ",
                            " và ", " hoặc ", " nhưng ", " bởi vì ", " vì ", " nếu ",
                            " khi ", " trong khi "]

        clause_count = 1  # Ít nhất 1 mệnh đề
        for indicator in clause_indicators:
            clause_count += query.lower().count(indicator)

        if clause_count == 1:
            complexity += 0.0
        elif clause_count == 2:
            complexity += 0.2
        elif clause_count == 3:
            complexity += 0.4
        else:
            complexity += 0.6

        # 3. Sự hiện diện của từ khóa phức tạp
        complex_keywords = ["compare", "contrast", "analyze", "evaluate", "explain",
                          "describe", "define", "identify", "discuss", "examine",
                          "so sánh", "phân tích", "đánh giá", "giải thích", "mô tả",
                          "định nghĩa", "nhận diện", "thảo luận", "kiểm tra"]

        keyword_count = 0
        for keyword in complex_keywords:
            if keyword in query.lower():
                keyword_count += 1

        if keyword_count >= 2:
            complexity += 0.4
        elif keyword_count == 1:
            complexity += 0.2

        # 4. Các từ chỉ câu hỏi phức tạp
        complex_question_indicators = ["why", "how", "what if", "in what way", "to what extent",
                                    "tại sao", "như thế nào", "bằng cách nào", "nếu", "đến mức nào"]

        for indicator in complex_question_indicators:
            if indicator in query.lower():
                complexity += 0.3
                break

        # 5. Phân tích ngữ nghĩa đơn giản
        query_lower = query.lower()

        # Truy vấn tìm kiếm về nhiều khái niệm
        if "and" in query_lower or "or" in query_lower or "vs" in query_lower or "versus" in query_lower or "compared to" in query_lower:
            complexity += 0.2

        # Truy vấn tìm kiếm kết quả có điều kiện
        if "if" in query_lower or "when" in query_lower or "where" in query_lower:
            complexity += 0.2

        # Truy vấn đòi hỏi phân tích sâu
        if "pros and cons" in query_lower or "advantages and disadvantages" in query_lower or "benefits and drawbacks" in query_lower:
            complexity += 0.3

        # Chuẩn hóa điểm số từ 0.0 đến 1.0
        return min(complexity, 1.0)

    def _extract_entities(self, query: str) -> List[str]:
        """
        Trích xuất các thực thể từ truy vấn.

        Args:
            query: Truy vấn cần phân tích

        Returns:
            Danh sách các thực thể
        """
        # Phương pháp đơn giản: tìm các cụm danh từ
        # Trong triển khai thực tế, nên sử dụng NER (Named Entity Recognition)

        entities = []
        words = query.split()

        # Tìm các từ viết hoa (có thể là tên riêng)
        capitalized_words = []
        for i, word in enumerate(words):
            if len(word) > 0 and word[0].isupper() and i > 0:  # Bỏ qua từ đầu câu
                capitalized_words.append(word)

        # Nếu tìm thấy các từ viết hoa liên tiếp, ghép chúng lại
        if capitalized_words:
            current_entity = ""
            for i, word in enumerate(words):
                if word in capitalized_words:
                    if current_entity:
                        current_entity += " " + word
                    else:
                        current_entity = word
                elif current_entity:
                    entities.append(current_entity)
                    current_entity = ""

            if current_entity:  # Thêm thực thể cuối cùng nếu có
                entities.append(current_entity)

        # Nếu không tìm thấy thực thể qua phương pháp trên
        if not entities:
            # Tìm các cụm từ giữa dấu ngoặc kép
            quote_pattern = r'"([^"]+)"'
            quoted_entities = re.findall(quote_pattern, query)
            entities.extend(quoted_entities)

        # Nếu vẫn không tìm thấy, thử tìm các cụm danh từ phổ biến
        if not entities:
            noun_phrases = []
            current_phrase = ""

            for word in words:
                if word.lower() in ["the", "a", "an", "this", "that", "these", "those",
                                  "các", "những", "một", "hai", "ba", "nhiều"]:
                    if current_phrase:
                        current_phrase += " " + word
                    else:
                        current_phrase = word
                elif current_phrase:
                    current_phrase += " " + word
                    if word.lower() in ["of", "in", "on", "at", "by", "with", "from", "to",
                                      "của", "trong", "trên", "tại", "bởi", "với", "từ", "đến"]:
                        continue
                    else:
                        noun_phrases.append(current_phrase.strip())
                        current_phrase = ""

            if current_phrase:  # Thêm cụm từ cuối cùng nếu có
                noun_phrases.append(current_phrase.strip())

            # Loại bỏ các cụm từ quá ngắn
            noun_phrases = [np for np in noun_phrases if len(np.split()) > 1]
            entities.extend(noun_phrases)

        return entities

    def detect_content_language(self, content: str) -> str:
        """
        Phát hiện ngôn ngữ của nội dung.

        Args:
            content: Nội dung cần phát hiện ngôn ngữ

        Returns:
            Mã ngôn ngữ (ISO 639-1)
        """
        if self._language_handler:
            try:
                return self._language_handler.detect_language(content)
            except Exception as e:
                self.logger.warning(f"Lỗi khi phát hiện ngôn ngữ: {e}")

        # Fallback: phát hiện đơn giản
        return self._detect_language_simple(content)

    def _detect_language_simple(self, content: str) -> str:
        """
        Phát hiện ngôn ngữ đơn giản (fallback).

        Args:
            content: Nội dung cần phát hiện ngôn ngữ

        Returns:
            Mã ngôn ngữ
        """
        if not content:
            return 'en'

        # Kiểm tra tiếng Việt
        vietnamese_chars = set('áàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđ')
        vietnamese_count = sum(1 for c in content.lower() if c in vietnamese_chars)

        if vietnamese_count > len(content) * 0.05:
            return 'vi'

        # Mặc định tiếng Anh
        return 'en'

    def normalize_content_language(self, content: str, language: str = None) -> str:
        """
        Chuẩn hóa nội dung theo ngôn ngữ.

        Args:
            content: Nội dung cần chuẩn hóa
            language: Ngôn ngữ (nếu None sẽ tự động phát hiện)

        Returns:
            Nội dung đã chuẩn hóa
        """
        if not content:
            return content

        if not language:
            language = self.detect_content_language(content)

        if self._language_handler:
            try:
                if language == 'vi':
                    return self._language_handler.normalize_vietnamese_text(content)
                else:
                    # Chuẩn hóa cơ bản cho ngôn ngữ khác
                    return content.strip()
            except Exception as e:
                self.logger.warning(f"Lỗi khi chuẩn hóa ngôn ngữ: {e}")

        return content.strip()

    def extract_keywords_multilingual(self, content: str, language: str = None, max_keywords: int = 10) -> List[str]:
        """
        Trích xuất từ khóa đa ngôn ngữ.

        Args:
            content: Nội dung cần trích xuất từ khóa
            language: Ngôn ngữ (nếu None sẽ tự động phát hiện)
            max_keywords: Số từ khóa tối đa

        Returns:
            Danh sách từ khóa
        """
        if not content:
            return []

        if not language:
            language = self.detect_content_language(content)

        if self._language_handler:
            try:
                return self._language_handler.extract_keywords(content, language, max_keywords)
            except Exception as e:
                self.logger.warning(f"Lỗi khi trích xuất từ khóa: {e}")

        # Fallback: trích xuất từ khóa đơn giản
        return self._extract_keywords_simple(content, max_keywords)

    def _extract_keywords_simple(self, content: str, max_keywords: int = 10) -> List[str]:
        """
        Trích xuất từ khóa đơn giản (fallback).

        Args:
            content: Nội dung
            max_keywords: Số từ khóa tối đa

        Returns:
            Danh sách từ khóa
        """
        import re
        from collections import Counter

        # Tách từ
        words = re.findall(r'\b\w+\b', content.lower())

        # Loại bỏ từ ngắn
        words = [w for w in words if len(w) > 3]

        # Đếm tần suất
        word_freq = Counter(words)

        # Lấy từ khóa phổ biến nhất
        return [word for word, freq in word_freq.most_common(max_keywords)]

    def _get_cache_key(self, query: str, **kwargs) -> str:
        """
        Tạo cache key cho truy vấn.

        Args:
            query: Truy vấn tìm kiếm
            **kwargs: Các tham số bổ sung

        Returns:
            Cache key
        """
        import hashlib

        # Tạo key từ query và các tham số quan trọng
        key_parts = [query]

        # Thêm các tham số ảnh hưởng đến kết quả
        for param in ['num_results', 'language', 'engine', 'method']:
            if param in kwargs:
                key_parts.append(f"{param}:{kwargs[param]}")

        # Tạo hash để đảm bảo key ngắn gọn
        key_string = "|".join(key_parts)
        return hashlib.md5(key_string.encode()).hexdigest()

    def _get_from_cache(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """
        Lấy dữ liệu từ cache.

        Args:
            cache_key: Cache key

        Returns:
            Dữ liệu từ cache hoặc None
        """
        if not self.use_cache:
            return None

        try:
            if self._smart_cache:
                return self._smart_cache.get(cache_key)
            elif hasattr(self, 'cache'):
                # Fallback cache đơn giản
                if cache_key in self.cache:
                    # Kiểm tra TTL
                    if cache_key in self.cache_ttl_map:
                        if time.time() - self.cache_ttl_map[cache_key] > self.cache_ttl:
                            # Expired
                            del self.cache[cache_key]
                            del self.cache_ttl_map[cache_key]
                            return None
                    return self.cache[cache_key]
        except Exception as e:
            self.logger.warning(f"Lỗi khi lấy từ cache: {e}")

        return None

    def _save_to_cache(self, cache_key: str, data: Dict[str, Any], ttl: Optional[int] = None):
        """
        Lưu dữ liệu vào cache.

        Args:
            cache_key: Cache key
            data: Dữ liệu cần lưu
            ttl: Time to live (seconds)
        """
        if not self.use_cache:
            return

        try:
            if self._smart_cache:
                self._smart_cache.set(cache_key, data, ttl=ttl)
            elif hasattr(self, 'cache'):
                # Fallback cache đơn giản
                self.cache[cache_key] = data
                self.cache_ttl_map[cache_key] = time.time()
        except Exception as e:
            self.logger.warning(f"Lỗi khi lưu vào cache: {e}")

    def _determine_cache_ttl(self, query: str, content_type: str = "search") -> int:
        """
        Xác định TTL động cho cache dựa trên loại nội dung.

        Args:
            query: Truy vấn
            content_type: Loại nội dung (search, content, analysis)

        Returns:
            TTL tính bằng giây
        """
        if not self.adaptive_ttl:
            return self.cache_ttl

        # TTL cơ bản theo loại nội dung
        base_ttl = {
            'search': 3600,      # 1 giờ cho kết quả tìm kiếm
            'content': 7200,     # 2 giờ cho nội dung trang web
            'analysis': 1800,    # 30 phút cho phân tích LLM
            'credibility': 86400 # 24 giờ cho đánh giá độ tin cậy
        }.get(content_type, self.cache_ttl)

        # Điều chỉnh TTL dựa trên độ phức tạp của query
        complexity = self._calculate_query_complexity(query)

        if complexity > 0.8:
            # Query phức tạp -> TTL ngắn hơn (thông tin có thể thay đổi)
            return int(base_ttl * 0.5)
        elif complexity < 0.3:
            # Query đơn giản -> TTL dài hơn (thông tin ổn định)
            return int(base_ttl * 2)
        else:
            return base_ttl

    def get_cache_stats(self) -> Dict[str, Any]:
        """
        Lấy thống kê cache.

        Returns:
            Thống kê cache
        """
        if self._smart_cache:
            try:
                return self._smart_cache.get_stats()
            except Exception as e:
                self.logger.warning(f"Lỗi khi lấy thống kê cache: {e}")

        # Fallback stats cho cache đơn giản
        if hasattr(self, 'cache'):
            return {
                'current_size': len(self.cache),
                'cache_type': 'simple',
                'ttl_entries': len(self.cache_ttl_map)
            }

        return {'cache_enabled': False}

    def clear_cache(self):
        """Xóa toàn bộ cache."""
        try:
            if self._smart_cache:
                self._smart_cache.clear()
            elif hasattr(self, 'cache'):
                self.cache.clear()
                self.cache_ttl_map.clear()

            self.logger.info("Cache đã được xóa")
        except Exception as e:
            self.logger.warning(f"Lỗi khi xóa cache: {e}")

    def optimize_query(self, query: str, language: str = None) -> str:
        """
        Tối ưu hóa truy vấn để cải thiện kết quả tìm kiếm.

        Args:
            query: Truy vấn gốc
            language: Ngôn ngữ (nếu None sẽ tự động phát hiện)

        Returns:
            Truy vấn đã tối ưu hóa
        """
        if not query:
            return query

        # Phát hiện ngôn ngữ nếu cần
        if not language:
            language = self.detect_content_language(query)

        # Chuẩn hóa truy vấn
        optimized = query.strip()

        # Loại bỏ từ vô nghĩa
        if language == 'vi':
            stop_words = {'và', 'của', 'cho', 'là', 'để', 'trong', 'với', 'có', 'được', 'không'}
        else:
            stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for'}

        words = optimized.split()
        filtered_words = [w for w in words if w.lower() not in stop_words or len(words) <= 3]

        if filtered_words:
            optimized = ' '.join(filtered_words)

        # Thêm từ khóa quan trọng nếu cần
        if len(optimized.split()) < 3:
            # Truy vấn quá ngắn, có thể cần mở rộng
            keywords = self.extract_keywords_multilingual(optimized, language, 2)
            if keywords:
                optimized = f"{optimized} {' '.join(keywords[:1])}"

        return optimized

    def generate_alternative_queries(self, query: str, max_alternatives: int = 3) -> List[str]:
        """
        Tạo các truy vấn thay thế.

        Args:
            query: Truy vấn gốc
            max_alternatives: Số lượng truy vấn thay thế tối đa

        Returns:
            Danh sách truy vấn thay thế
        """
        if not query:
            return []

        alternatives = []

        # Phương pháp 1: Thêm từ đồng nghĩa
        synonyms = {
            'how': ['what is the way to', 'what is the method to'],
            'why': ['what is the reason for', 'what causes'],
            'what': ['which', 'what kind of'],
            'làm thế nào': ['cách nào để', 'phương pháp nào để'],
            'tại sao': ['vì sao', 'lý do gì'],
            'là gì': ['nghĩa là gì', 'có nghĩa là gì']
        }

        for word, syns in synonyms.items():
            if word in query.lower():
                for syn in syns:
                    alt = query.lower().replace(word, syn)
                    if alt != query.lower() and alt not in alternatives:
                        alternatives.append(alt)
                        if len(alternatives) >= max_alternatives:
                            return alternatives

        # Phương pháp 2: Thêm context
        if len(alternatives) < max_alternatives:
            context_additions = [
                f"{query} definition",
                f"{query} explanation",
                f"{query} examples"
            ]

            for addition in context_additions:
                if addition not in alternatives:
                    alternatives.append(addition)
                    if len(alternatives) >= max_alternatives:
                        break

        return alternatives[:max_alternatives]

    def detect_query_intent(self, query: str) -> Dict[str, Any]:
        """
        Phát hiện ý định của truy vấn.

        Args:
            query: Truy vấn cần phân tích

        Returns:
            Thông tin về ý định
        """
        intent = {
            'type': 'informational',
            'confidence': 0.5,
            'keywords': [],
            'entities': [],
            'question_type': None
        }

        if not query:
            return intent

        query_lower = query.lower()

        # Phát hiện loại câu hỏi
        question_patterns = {
            'definition': ['what is', 'define', 'meaning of', 'là gì', 'định nghĩa', 'có nghĩa'],
            'how_to': ['how to', 'how do', 'làm thế nào', 'cách nào'],
            'why': ['why', 'tại sao', 'vì sao'],
            'when': ['when', 'khi nào'],
            'where': ['where', 'ở đâu'],
            'who': ['who', 'ai'],
            'comparison': ['vs', 'versus', 'compare', 'so sánh', 'khác nhau'],
            'list': ['list of', 'types of', 'danh sách', 'các loại']
        }

        for q_type, patterns in question_patterns.items():
            for pattern in patterns:
                if pattern in query_lower:
                    intent['question_type'] = q_type
                    intent['confidence'] = 0.8
                    break
            if intent['question_type']:
                break

        # Trích xuất từ khóa và entities
        intent['keywords'] = self.extract_keywords_multilingual(query, max_keywords=5)
        intent['entities'] = self._extract_entities(query)

        return intent

    def crawl_with_javascript_support(self, url: str, wait_for_selector: str = None,
                                    scroll_to_bottom: bool = False,
                                    extract_dynamic_content: bool = True) -> Dict[str, Any]:
        """
        Crawl trang web với hỗ trợ JavaScript đầy đủ.

        Args:
            url: URL cần crawl
            wait_for_selector: CSS selector để đợi load
            scroll_to_bottom: Có scroll xuống cuối trang không
            extract_dynamic_content: Có trích xuất nội dung động không

        Returns:
            Kết quả crawl với nội dung JavaScript
        """
        if hasattr(self, '_playwright_handler') and self._playwright_handler:
            try:
                return self._playwright_handler.extract_content_with_js(
                    url=url,
                    wait_for_selector=wait_for_selector,
                    scroll_to_bottom=scroll_to_bottom,
                    extract_dynamic_content=extract_dynamic_content,
                    timeout=self.timeout
                )
            except Exception as e:
                self.logger.warning(f"Lỗi khi crawl với JavaScript: {e}")

        # Fallback: crawl thông thường
        return self._fetch_url(url)

    def crawl_spa_website(self, url: str, navigation_timeout: int = 30000,
                         wait_for_network_idle: bool = True) -> Dict[str, Any]:
        """
        Crawl Single Page Application (SPA).

        Args:
            url: URL của SPA
            navigation_timeout: Timeout cho navigation
            wait_for_network_idle: Đợi network idle

        Returns:
            Kết quả crawl SPA
        """
        if hasattr(self, '_playwright_handler') and self._playwright_handler:
            try:
                return self._playwright_handler.extract_spa_content(
                    url=url,
                    navigation_timeout=navigation_timeout,
                    wait_for_network_idle=wait_for_network_idle
                )
            except Exception as e:
                self.logger.warning(f"Lỗi khi crawl SPA: {e}")

        # Fallback
        return self._fetch_url(url)

    def crawl_with_infinite_scroll(self, url: str, max_scrolls: int = 5,
                                  scroll_delay: float = 2.0) -> Dict[str, Any]:
        """
        Crawl trang web có infinite scroll.

        Args:
            url: URL cần crawl
            max_scrolls: Số lần scroll tối đa
            scroll_delay: Thời gian delay giữa các lần scroll

        Returns:
            Kết quả crawl với toàn bộ nội dung
        """
        if hasattr(self, '_playwright_handler') and self._playwright_handler:
            try:
                return self._playwright_handler.extract_with_infinite_scroll(
                    url=url,
                    max_scrolls=max_scrolls,
                    scroll_delay=scroll_delay
                )
            except Exception as e:
                self.logger.warning(f"Lỗi khi crawl infinite scroll: {e}")

        # Fallback
        return self._fetch_url(url)

    def crawl_with_form_interaction(self, url: str, form_data: Dict[str, str],
                                   submit_selector: str = 'input[type="submit"]') -> Dict[str, Any]:
        """
        Crawl trang web với tương tác form.

        Args:
            url: URL có form
            form_data: Dữ liệu form cần điền
            submit_selector: CSS selector của nút submit

        Returns:
            Kết quả sau khi submit form
        """
        if hasattr(self, '_playwright_handler') and self._playwright_handler:
            try:
                return self._playwright_handler.interact_with_form(
                    url=url,
                    form_data=form_data,
                    submit_selector=submit_selector
                )
            except Exception as e:
                self.logger.warning(f"Lỗi khi tương tác form: {e}")

        # Fallback: POST request
        try:
            import requests
            response = requests.post(url, data=form_data, timeout=self.timeout)
            return {
                'url': url,
                'status_code': response.status_code,
                'content': response.text,
                'success': response.status_code == 200
            }
        except Exception as e:
            return {
                'url': url,
                'success': False,
                'error': str(e)
            }

    def crawl_with_pagination(self, base_url: str, max_pages: int = 10,
                             next_page_selector: str = 'a[rel="next"]') -> List[Dict[str, Any]]:
        """
        Crawl trang web có phân trang.

        Args:
            base_url: URL trang đầu tiên
            max_pages: Số trang tối đa
            next_page_selector: CSS selector của link trang tiếp theo

        Returns:
            Danh sách kết quả từ tất cả các trang
        """
        results = []
        current_url = base_url

        for page_num in range(max_pages):
            if self.verbose:
                self.logger.info(f"Crawling trang {page_num + 1}: {current_url}")

            # Crawl trang hiện tại
            page_result = self._fetch_url(current_url)
            if page_result.get('success'):
                results.append(page_result)

                # Tìm link trang tiếp theo
                if hasattr(self, '_playwright_handler') and self._playwright_handler:
                    try:
                        next_url = self._playwright_handler.get_next_page_url(
                            current_url, next_page_selector
                        )
                        if next_url and next_url != current_url:
                            current_url = next_url
                        else:
                            break
                    except Exception as e:
                        self.logger.warning(f"Không thể tìm trang tiếp theo: {e}")
                        break
                else:
                    # Fallback: parse HTML để tìm next link
                    try:
                        from bs4 import BeautifulSoup
                        soup = BeautifulSoup(page_result.get('content', ''), 'html.parser')
                        next_link = soup.select_one(next_page_selector)
                        if next_link and next_link.get('href'):
                            from urllib.parse import urljoin
                            next_url = urljoin(current_url, next_link['href'])
                            if next_url != current_url:
                                current_url = next_url
                            else:
                                break
                        else:
                            break
                    except Exception as e:
                        self.logger.warning(f"Lỗi khi parse HTML: {e}")
                        break
            else:
                break

        return results

    def _check_searxng_health(self, searxng_url: str, timeout: int = 3) -> bool:
        """
        Kiểm tra SearXNG instance có hoạt động không.

        Args:
            searxng_url (str): URL của SearXNG instance (e.g., 'http://localhost:8080')
            timeout (int, optional): Timeout cho health check trong giây. Defaults to 3.

        Returns:
            bool: True nếu SearXNG hoạt động và phản hồi đúng, False nếu không

        Note:
            Method này kiểm tra bằng cách gửi GET request đến SearXNG instance
            và tìm kiếm các indicators như 'searx', 'search', 'opensearch' trong response.
        """
        try:
            import requests

            # Thử truy cập trang chủ SearXNG
            response = requests.get(
                searxng_url,
                timeout=timeout,
                headers={'User-Agent': self.user_agent}
            )

            # Kiểm tra response có chứa SearXNG không
            if response.status_code == 200:
                content = response.text.lower()
                # Kiểm tra các dấu hiệu của SearXNG
                searxng_indicators = ['searx', 'search', 'opensearch', 'searxng']
                if any(indicator in content for indicator in searxng_indicators):
                    return True

            return False

        except Exception as e:
            if self.verbose:
                self.logger.debug(f"SearXNG health check failed for {searxng_url}: {e}")
            return False

    def _perform_real_search(self, query: str, num_results: int = 10) -> List[Dict[str, Any]]:
        """
        Thực hiện tìm kiếm thật với các search engines.

        Args:
            query (str): Truy vấn tìm kiếm
            num_results (int, optional): Số lượng kết quả mong muốn. Defaults to 10.

        Returns:
            List[Dict[str, Any]]: Danh sách kết quả tìm kiếm thật với các trường:
                - title (str): Tiêu đề kết quả
                - url (str): URL của kết quả
                - snippet (str): Đoạn trích nội dung
                - source (str): Nguồn search engine
                - score (float): Điểm số relevance

        Raises:
            Exception: Khi tất cả search engines thất bại
        """
        results = []

        try:
            # Ưu tiên 1: Sử dụng SearXNG nếu có sẵn
            searxng_results = self._search_with_searxng(query, num_results)
            if searxng_results:
                results.extend(searxng_results)
                if len(results) >= num_results:
                    return results[:num_results]

            # Ưu tiên 2: Sử dụng DuckDuckGo (không cần API key)
            ddg_results = self._search_with_duckduckgo(query, num_results - len(results))
            if ddg_results:
                results.extend(ddg_results)
                if len(results) >= num_results:
                    return results[:num_results]

            # Ưu tiên 3: Sử dụng Bing Search (nếu có API key)
            bing_results = self._search_with_bing(query, num_results - len(results))
            if bing_results:
                results.extend(bing_results)
                if len(results) >= num_results:
                    return results[:num_results]

            # Ưu tiên 4: Sử dụng Google Custom Search (nếu có API key)
            google_results = self._search_with_google(query, num_results - len(results))
            if google_results:
                results.extend(google_results)

            # Nếu vẫn không có kết quả, fallback về crawling trực tiếp
            if not results:
                results = self._fallback_search(query, num_results)

            return results[:num_results]

        except Exception as e:
            self.logger.error(f"Lỗi trong _perform_real_search: {e}")
            # Fallback về mock data nếu tất cả đều thất bại
            return self._fallback_search(query, num_results)

    def _search_with_searxng(self, query: str, num_results: int) -> List[Dict[str, Any]]:
        """
        Tìm kiếm với SearXNG.

        Args:
            query: Truy vấn tìm kiếm
            num_results: Số lượng kết quả

        Returns:
            Danh sách kết quả từ SearXNG
        """
        try:
            # Ưu tiên SearXNG local trước, sau đó mới đến các instances công khai
            searxng_instances = []

            # 1. Kiểm tra SearXNG local trước (ưu tiên cao nhất)
            local_instances = []

            # Kiểm tra environment variable trước
            custom_searxng_url = os.getenv('SEARXNG_LOCAL_URL')
            if custom_searxng_url:
                local_instances.append(custom_searxng_url)
                if self.verbose:
                    self.logger.info(f"Sử dụng SearXNG URL từ environment: {custom_searxng_url}")

            # Thêm các URL local mặc định
            default_local_instances = [
                "http://localhost:8080",
                "http://127.0.0.1:8080",
                "http://localhost:4000",
                "http://127.0.0.1:4000",
                "http://localhost:8888",
                "http://127.0.0.1:8888"
            ]
            local_instances.extend(default_local_instances)

            # Kiểm tra SearXNG local có hoạt động không
            for local_url in local_instances:
                if self._check_searxng_health(local_url):
                    searxng_instances.append(local_url)
                    if self.verbose:
                        self.logger.info(f"SearXNG local phát hiện: {local_url}")
                    break  # Chỉ cần 1 local instance hoạt động

            # 2. Thêm các SearXNG instances công khai làm fallback
            public_instances = [
                "https://searx.be",
                "https://search.sapti.me",
                "https://searx.tiekoetter.com",
                "https://searx.prvcy.eu",
                "https://search.bus-hit.me"
            ]
            searxng_instances.extend(public_instances)

            for instance in searxng_instances:
                try:
                    import requests

                    params = {
                        'q': query,
                        'format': 'json',
                        'engines': 'google,bing,duckduckgo',
                        'safesearch': '1'
                    }

                    response = requests.get(
                        f"{instance}/search",
                        params=params,
                        timeout=10,
                        headers={'User-Agent': self.user_agent}
                    )

                    if response.status_code == 200:
                        data = response.json()
                        results = []

                        for item in data.get('results', [])[:num_results]:
                            result = {
                                'title': item.get('title', ''),
                                'url': item.get('url', ''),
                                'snippet': item.get('content', ''),
                                'source': item.get('engine', 'searxng'),
                                'score': item.get('score', 0)
                            }
                            results.append(result)

                        if results:
                            if self.verbose:
                                self.logger.info(f"SearXNG ({instance}) trả về {len(results)} kết quả")
                            return results

                except Exception as e:
                    if self.verbose:
                        self.logger.warning(f"Lỗi với SearXNG instance {instance}: {e}")
                    continue

            return []

        except Exception as e:
            if self.verbose:
                self.logger.warning(f"Lỗi SearXNG search: {e}")
            return []

    def _search_with_duckduckgo(self, query: str, num_results: int) -> List[Dict[str, Any]]:
        """
        Tìm kiếm với DuckDuckGo (không cần API key).

        Args:
            query: Truy vấn tìm kiếm
            num_results: Số lượng kết quả

        Returns:
            Danh sách kết quả từ DuckDuckGo
        """
        try:
            # Sử dụng DuckDuckGo HTML search
            import requests
            from bs4 import BeautifulSoup
            import urllib.parse

            # Encode query
            encoded_query = urllib.parse.quote_plus(query)

            # DuckDuckGo search URL
            url = f"https://html.duckduckgo.com/html/?q={encoded_query}"

            headers = {
                'User-Agent': self.user_agent,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
            }

            response = requests.get(url, headers=headers, timeout=10)

            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                results = []

                # Parse DuckDuckGo results
                for result_div in soup.find_all('div', class_='result')[:num_results]:
                    try:
                        title_elem = result_div.find('a', class_='result__a')
                        snippet_elem = result_div.find('a', class_='result__snippet')

                        if title_elem:
                            title = title_elem.get_text(strip=True)
                            url = title_elem.get('href', '')
                            snippet = snippet_elem.get_text(strip=True) if snippet_elem else ''

                            result = {
                                'title': title,
                                'url': url,
                                'snippet': snippet,
                                'source': 'duckduckgo',
                                'score': 1.0
                            }
                            results.append(result)
                    except Exception as e:
                        if self.verbose:
                            self.logger.warning(f"Lỗi parse DuckDuckGo result: {e}")
                        continue

                if results and self.verbose:
                    self.logger.info(f"DuckDuckGo trả về {len(results)} kết quả")

                return results

            return []

        except Exception as e:
            if self.verbose:
                self.logger.warning(f"Lỗi DuckDuckGo search: {e}")
            return []

    def _search_with_bing(self, query: str, num_results: int) -> List[Dict[str, Any]]:
        """
        Tìm kiếm với Bing Search API (cần API key).

        Args:
            query: Truy vấn tìm kiếm
            num_results: Số lượng kết quả

        Returns:
            Danh sách kết quả từ Bing
        """
        try:
            # Kiểm tra API key
            bing_api_key = os.getenv('BING_SEARCH_API_KEY')
            if not bing_api_key:
                if self.verbose:
                    self.logger.info("Không có Bing API key, bỏ qua Bing search")
                return []

            import requests

            endpoint = "https://api.bing.microsoft.com/v7.0/search"
            headers = {'Ocp-Apim-Subscription-Key': bing_api_key}
            params = {
                'q': query,
                'count': min(num_results, 50),
                'mkt': 'en-US',
                'safesearch': 'Moderate'
            }

            response = requests.get(endpoint, headers=headers, params=params, timeout=10)

            if response.status_code == 200:
                data = response.json()
                results = []

                for item in data.get('webPages', {}).get('value', [])[:num_results]:
                    result = {
                        'title': item.get('name', ''),
                        'url': item.get('url', ''),
                        'snippet': item.get('snippet', ''),
                        'source': 'bing',
                        'score': 1.0
                    }
                    results.append(result)

                if results and self.verbose:
                    self.logger.info(f"Bing trả về {len(results)} kết quả")

                return results

            return []

        except Exception as e:
            if self.verbose:
                self.logger.warning(f"Lỗi Bing search: {e}")
            return []

    def _search_with_google(self, query: str, num_results: int) -> List[Dict[str, Any]]:
        """
        Tìm kiếm với Google Custom Search API (cần API key).

        Args:
            query: Truy vấn tìm kiếm
            num_results: Số lượng kết quả

        Returns:
            Danh sách kết quả từ Google
        """
        try:
            # Kiểm tra API key và Search Engine ID
            google_api_key = os.getenv('GOOGLE_SEARCH_API_KEY')
            google_cse_id = os.getenv('GOOGLE_CSE_ID')

            if not google_api_key or not google_cse_id:
                if self.verbose:
                    self.logger.info("Không có Google API key/CSE ID, bỏ qua Google search")
                return []

            import requests

            endpoint = "https://www.googleapis.com/customsearch/v1"
            params = {
                'key': google_api_key,
                'cx': google_cse_id,
                'q': query,
                'num': min(num_results, 10),
                'safe': 'medium'
            }

            response = requests.get(endpoint, params=params, timeout=10)

            if response.status_code == 200:
                data = response.json()
                results = []

                for item in data.get('items', [])[:num_results]:
                    result = {
                        'title': item.get('title', ''),
                        'url': item.get('link', ''),
                        'snippet': item.get('snippet', ''),
                        'source': 'google',
                        'score': 1.0
                    }
                    results.append(result)

                if results and self.verbose:
                    self.logger.info(f"Google trả về {len(results)} kết quả")

                return results

            return []

        except Exception as e:
            if self.verbose:
                self.logger.warning(f"Lỗi Google search: {e}")
            return []

    def _fallback_search(self, query: str, num_results: int) -> List[Dict[str, Any]]:
        """
        Fallback search khi tất cả search engines thất bại.

        Args:
            query: Truy vấn tìm kiếm
            num_results: Số lượng kết quả

        Returns:
            Danh sách kết quả fallback
        """
        if self.verbose:
            self.logger.warning("Sử dụng fallback search - tất cả search engines thất bại")

        # Trả về kết quả thông báo lỗi thay vì mock data
        return [{
            'title': f'Không thể tìm kiếm "{query}"',
            'url': '',
            'snippet': 'Rất tiếc, tất cả các search engines đều không khả dụng. Vui lòng thử lại sau hoặc kiểm tra kết nối mạng.',
            'source': 'fallback',
            'score': 0.0,
            'error': 'All search engines failed'
        }]

    def get_performance_stats(self) -> Dict[str, Any]:
        """
        Lấy thống kê hiệu suất của agent.

        Returns:
            Thống kê hiệu suất
        """
        current_time = time.time()
        uptime = current_time - self.start_time

        stats = {
            'uptime_seconds': uptime,
            'total_requests': self.requests_count,
            'successful_requests': self.successful_requests_count,
            'success_rate': (self.successful_requests_count / max(self.requests_count, 1)) * 100,
            'requests_per_second': self.requests_count / max(uptime, 1),
            'cache_stats': self.get_cache_stats(),
            'memory_usage': self._get_memory_usage(),
            'active_connections': self._get_active_connections()
        }

        return stats

    def _get_memory_usage(self) -> Dict[str, Any]:
        """Lấy thông tin sử dụng bộ nhớ."""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()

            return {
                'rss_mb': memory_info.rss / 1024 / 1024,  # Resident Set Size
                'vms_mb': memory_info.vms / 1024 / 1024,  # Virtual Memory Size
                'percent': process.memory_percent()
            }
        except ImportError:
            return {'error': 'psutil not available'}
        except Exception as e:
            return {'error': str(e)}

    def _get_active_connections(self) -> int:
        """Lấy số kết nối đang hoạt động."""
        try:
            # Đếm các handler đang hoạt động
            active = 0
            if hasattr(self, '_playwright_handler') and self._playwright_handler:
                active += 1
            if hasattr(self, 'session') and self.session:
                active += 1
            return active
        except Exception:
            return 0

    def optimize_performance(self, enable_compression: bool = True,
                           enable_keep_alive: bool = True,
                           max_pool_connections: int = 10) -> None:
        """
        Tối ưu hóa hiệu suất của agent.

        Args:
            enable_compression: Bật nén dữ liệu
            enable_keep_alive: Bật keep-alive connections
            max_pool_connections: Số kết nối tối đa trong pool
        """
        try:
            import requests
            from requests.adapters import HTTPAdapter
            from urllib3.util.retry import Retry

            # Tạo session mới với tối ưu hóa
            if not hasattr(self, 'session') or not self.session:
                self.session = requests.Session()

            # Cấu hình retry strategy
            retry_strategy = Retry(
                total=self.max_retries,
                backoff_factor=1,
                status_forcelist=[429, 500, 502, 503, 504],
            )

            # Cấu hình adapter với connection pooling
            adapter = HTTPAdapter(
                max_retries=retry_strategy,
                pool_connections=max_pool_connections,
                pool_maxsize=max_pool_connections
            )

            self.session.mount("http://", adapter)
            self.session.mount("https://", adapter)

            # Cấu hình headers cho tối ưu hóa
            headers = {
                'User-Agent': self.user_agent,
                'Accept-Encoding': 'gzip, deflate' if enable_compression else 'identity',
                'Connection': 'keep-alive' if enable_keep_alive else 'close'
            }
            self.session.headers.update(headers)

            self.logger.info("Đã tối ưu hóa hiệu suất agent")

        except Exception as e:
            self.logger.warning(f"Lỗi khi tối ưu hóa hiệu suất: {e}")

    def batch_process_urls(self, urls: List[str], batch_size: int = 5,
                          delay_between_batches: float = 1.0) -> List[Dict[str, Any]]:
        """
        Xử lý URLs theo batch để tối ưu hiệu suất.

        Args:
            urls: Danh sách URLs cần xử lý
            batch_size: Kích thước mỗi batch
            delay_between_batches: Thời gian delay giữa các batch

        Returns:
            Danh sách kết quả
        """
        results = []

        for i in range(0, len(urls), batch_size):
            batch = urls[i:i + batch_size]

            if self.verbose:
                self.logger.info(f"Xử lý batch {i//batch_size + 1}: {len(batch)} URLs")

            # Xử lý batch hiện tại
            batch_results = self.parallel_crawl(batch, max_workers=min(batch_size, 3))
            results.extend(batch_results)

            # Delay giữa các batch để tránh overload
            if i + batch_size < len(urls):
                time.sleep(delay_between_batches)

        return results

    def adaptive_timeout(self, url: str, base_timeout: int = None) -> int:
        """
        Tính toán timeout thích ứng dựa trên URL.

        Args:
            url: URL cần xử lý
            base_timeout: Timeout cơ bản

        Returns:
            Timeout được điều chỉnh
        """
        if not base_timeout:
            base_timeout = self.timeout

        # Điều chỉnh timeout dựa trên domain
        try:
            from urllib.parse import urlparse
            domain = urlparse(url).netloc.lower()

            # Các domain chậm thường cần timeout cao hơn
            slow_domains = {
                'archive.org': 2.0,
                'scholar.google.com': 1.5,
                'researchgate.net': 1.5,
                'thuvienphapluat.vn': 1.8
            }

            for slow_domain, multiplier in slow_domains.items():
                if slow_domain in domain:
                    return int(base_timeout * multiplier)

            # Timeout mặc định
            return base_timeout

        except Exception:
            return base_timeout

    def cleanup_resources(self):
        """Dọn dẹp tài nguyên để giải phóng bộ nhớ."""
        try:
            # Đóng session requests
            if hasattr(self, 'session') and self.session:
                self.session.close()
                self.session = None

            # Dọn dẹp cache
            if hasattr(self, '_smart_cache') and self._smart_cache:
                try:
                    self._smart_cache.clear()
                except Exception:
                    pass

            # Dọn dẹp cache đơn giản
            if hasattr(self, 'cache'):
                self.cache.clear()
                self.cache_ttl_map.clear()

            # Đóng Playwright handler
            if hasattr(self, '_playwright_handler') and self._playwright_handler:
                try:
                    self._playwright_handler.cleanup()
                except Exception:
                    pass

            self.logger.info("Đã dọn dẹp tài nguyên")

        except Exception as e:
            self.logger.warning(f"Lỗi khi dọn dẹp tài nguyên: {e}")

    def _extract_main_topic(self, query: str) -> str:
        """
        Trích xuất chủ đề chính từ truy vấn.

        Args:
            query: Truy vấn cần phân tích

        Returns:
            Chủ đề chính hoặc chuỗi rỗng nếu không tìm thấy
        """
        # Trích xuất các thực thể
        entities = self._extract_entities(query)

        if entities:
            # Ưu tiên thực thể dài nhất
            entities.sort(key=len, reverse=True)
            return entities[0]

        # Nếu không tìm thấy thực thể, thử tìm cụm từ quan trọng
        words = query.split()

        # Loại bỏ các từ hỏi và liên từ ở đầu câu
        question_words = ["what", "when", "where", "which", "who", "whom", "whose", "why", "how",
                         "cái gì", "khi nào", "ở đâu", "nào", "ai", "của ai", "tại sao", "như thế nào"]

        for qw in question_words:
            if query.lower().startswith(qw):
                cleaned_query = query[len(qw):].strip()
                if cleaned_query.startswith("is") or cleaned_query.startswith("are") or cleaned_query.startswith("does") or cleaned_query.startswith("do"):
                    cleaned_query = cleaned_query[cleaned_query.find(" ")+1:].strip()
                if cleaned_query.startswith("là") or cleaned_query.startswith("có phải"):
                    cleaned_query = cleaned_query[cleaned_query.find(" ")+1:].strip()

                # Lấy tối đa 4 từ tiếp theo làm chủ đề
                return " ".join(cleaned_query.split()[:4])

        # Nếu không phải câu hỏi, lấy 3-4 từ đầu tiên
        return " ".join(words[:min(4, len(words))])

    def _extract_facets(self, query: str) -> List[str]:
        """
        Trích xuất các khía cạnh từ truy vấn đa khía cạnh.

        Args:
            query: Truy vấn cần phân tích

        Returns:
            Danh sách các khía cạnh
        """
        query_lower = query.lower()
        facets = []

        # Danh sách các khía cạnh phổ biến
        common_facets = [
            "history", "culture", "economy", "politics", "geography", "climate",
            "population", "language", "religion", "education", "healthcare", "technology",
            "lịch sử", "văn hóa", "kinh tế", "chính trị", "địa lý", "khí hậu",
            "dân số", "ngôn ngữ", "tôn giáo", "giáo dục", "y tế", "công nghệ"
        ]

        # Tìm các khía cạnh được đề cập trong câu hỏi
        for facet in common_facets:
            if facet in query_lower:
                facets.append(facet)

        # Tìm các khía cạnh được liệt kê (phân tách bởi dấu phẩy hoặc "and")
        facet_list_patterns = [
            r"(?:about|on|regarding|concerning|về|liên quan đến|liên quan)\s+((?:\w+(?:,\s+|\s+and\s+|\s+và\s+))*\w+)",
            r"(?:aspects|khía cạnh|vấn đề|nội dung)\s+(?:of|về|liên quan đến)\s+((?:\w+(?:,\s+|\s+and\s+|\s+và\s+))*\w+)"
        ]

        for pattern in facet_list_patterns:
            matches = re.search(pattern, query_lower)
            if matches:
                facet_list = matches.group(1)
                individual_facets = re.split(r',\s+|\s+and\s+|\s+và\s+', facet_list)
                facets.extend(individual_facets)

        # Nếu không tìm thấy khía cạnh cụ thể
        if not facets:
            # Thêm một số khía cạnh mặc định dựa trên loại truy vấn
            if "history" in query_lower or "lịch sử" in query_lower:
                facets = ["origin", "development", "important events", "timeline"]
            elif "compare" in query_lower or "so sánh" in query_lower:
                facets = ["similarities", "differences", "advantages", "disadvantages"]
            elif "effect" in query_lower or "impact" in query_lower or "ảnh hưởng" in query_lower:
                facets = ["positive effects", "negative effects", "long-term impact", "solutions"]

        return facets

    def _extract_keywords(self, query: str) -> List[str]:
        """
        Trích xuất từ khóa quan trọng từ truy vấn.

        Args:
            query: Truy vấn cần phân tích

        Returns:
            Danh sách các từ khóa quan trọng
        """
        # Danh sách stopwords (từ không quan trọng)
        stopwords = ["the", "a", "an", "and", "or", "but", "if", "then", "so", "because",
                   "as", "that", "this", "these", "those", "to", "of", "in", "on", "at",
                   "by", "with", "about", "for", "from",
                   "các", "những", "và", "hoặc", "nhưng", "nếu", "thì", "vậy", "bởi vì",
                   "như", "đó", "này", "kia", "của", "trong", "trên", "tại", "cho", "từ"]

        # Tách từ và chuyển thành chữ thường
        words = query.lower().split()

        # Loại bỏ stopwords và từ quá ngắn
        keywords = [word for word in words if word not in stopwords and len(word) > 2]

        # Sắp xếp theo độ dài từ (ưu tiên từ dài hơn, thường quan trọng hơn)
        keywords.sort(key=len, reverse=True)

        return keywords

    def _decompose_query(self, query: str) -> List[str]:
        """
        Phân rã truy vấn phức tạp thành các truy vấn đơn giản hơn.

        Args:
            query: Truy vấn cần phân rã

        Returns:
            Danh sách các truy vấn con
        """
        if not query or len(query.strip()) < 10:
            return []

        # Bước 1: Kiểm tra độ phức tạp của truy vấn
        complexity_score = self._calculate_query_complexity(query)

        # Nếu truy vấn đơn giản, không cần phân rã
        if complexity_score < 0.5:
            return []

        # Bước 2: Phân tích cấu trúc câu
        sub_queries = []

        # Kiểm tra truy vấn nhiều phần
        # 2.1. Phân tách dựa trên liên từ và dấu câu
        conjunctions = ["and", "or", "but", "as well as", "along with", "including", "plus",
                        "và", "cùng với", "hoặc", "nhưng", "bao gồm", "kèm theo", "cộng với"]

        # Chuẩn bị truy vấn để phân tích
        query_lower = query.lower()

        # Thử phân tách bằng liên từ
        for conj in conjunctions:
            if f" {conj} " in query_lower:
                parts = re.split(f" {conj} ", query, flags=re.IGNORECASE)
                if len(parts) > 1:
                    for part in parts:
                        part = part.strip()
                        if len(part) > 5:  # Bỏ qua phần quá ngắn
                            sub_queries.append(part)
                    break  # Dừng sau khi tìm thấy liên từ đầu tiên

        # 2.2. Phân tách dựa trên dấu phẩy nếu không tìm thấy liên từ
        if not sub_queries and "," in query:
            parts = query.split(",")
            if len(parts) > 1:
                for part in parts:
                    part = part.strip()
                    if len(part) > 5:  # Bỏ qua phần quá ngắn
                        sub_queries.append(part)

        # Bước 3: Phân tích ngữ nghĩa câu hỏi
        if not sub_queries:
            # 3.1. Xác định loại câu hỏi
            question_types = []

            # Câu hỏi so sánh
            comparison_indicators = ["compare", "difference between", "versus", "vs", "similarities", "differences",
                                   "so sánh", "sự khác nhau giữa", "điểm giống nhau", "điểm khác nhau"]

            for indicator in comparison_indicators:
                if indicator in query_lower:
                    question_types.append("comparison")
                    break

            # Câu hỏi đa khía cạnh
            multi_facet_indicators = ["aspects of", "features of", "characteristics of", "attributes of",
                                    "khía cạnh của", "đặc điểm của", "thuộc tính của"]

            for indicator in multi_facet_indicators:
                if indicator in query_lower:
                    question_types.append("multi_facet")
                    break

            # Câu hỏi nguyên nhân-kết quả
            cause_effect_indicators = ["causes of", "effects of", "results in", "leads to", "impacts of",
                                     "nguyên nhân của", "tác động của", "kết quả của", "dẫn đến", "gây ra"]

            for indicator in cause_effect_indicators:
                if indicator in query_lower:
                    question_types.append("cause_effect")
                    break

            # 3.2. Tạo các sub-queries dựa trên loại câu hỏi
            if "comparison" in question_types:
                # Ví dụ: "So sánh A và B" -> "A là gì", "B là gì", "A và B khác nhau thế nào"
                entities = self._extract_entities(query)
                if len(entities) >= 2:
                    for entity in entities[:2]:  # Chỉ lấy 2 thực thể đầu tiên
                        sub_queries.append(f"{entity} là gì")
                    sub_queries.append(f"{entities[0]} và {entities[1]} khác nhau thế nào")

            elif "multi_facet" in question_types:
                # Ví dụ: "Giới thiệu về lịch sử, văn hóa và ẩm thực của Việt Nam"
                topic = self._extract_main_topic(query)
                facets = self._extract_facets(query)

                if topic and facets:
                    for facet in facets:
                        sub_queries.append(f"{facet} của {topic}")

            elif "cause_effect" in question_types:
                # Ví dụ: "Nguyên nhân và hậu quả của biến đổi khí hậu"
                topic = self._extract_main_topic(query)
                if topic:
                    sub_queries.append(f"Nguyên nhân của {topic}")
                    sub_queries.append(f"Hậu quả của {topic}")

        # Bước 4: Tạo truy vấn bổ sung cho câu hỏi phức tạp
        if not sub_queries and len(query.split()) >= 8:
            # Trích xuất từ khóa chính và tạo truy vấn đơn giản hơn
            keywords = self._extract_keywords(query)
            if len(keywords) >= 2:
                sub_queries.append(" ".join(keywords[:3]))

        # Bước 5: Loại bỏ truy vấn trùng lặp và quá ngắn
        unique_queries = []
        seen = set()

        for sq in sub_queries:
            sq_normalized = sq.lower().strip()
            if sq_normalized not in seen and len(sq_normalized) > 5:
                seen.add(sq_normalized)
                unique_queries.append(sq)

        # Giới hạn số lượng sub-queries
        max_sub_queries = 3
        return unique_queries[:max_sub_queries]

    def _evaluate_factual_accuracy(self, content: str, query: str = None, sources: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Đánh giá độ chính xác của thông tin dựa trên nội dung và nguồn.

        Args:
            content: Nội dung cần đánh giá
            query: Truy vấn gốc (tùy chọn)
            sources: Danh sách các nguồn tham khảo (tùy chọn)

        Returns:
            Dict chứa kết quả đánh giá
        """
        result = {
            "score": 0.0,
            "confidence": 0.0,
            "explanation": "",
            "issues": [],
            "factual_statements": [],
            "uncertain_statements": [],
            "contradictions": []
        }

        # Kiểm tra nếu content trống
        if not content or len(content.strip()) < 10:
            result["explanation"] = "Nội dung quá ngắn để đánh giá"
            return result

        # Trích xuất các phát biểu thực tế từ nội dung
        statements = self._extract_factual_statements(content)

        # Kiểm tra các phát biểu với nguồn tham khảo nếu có
        if sources and len(sources) > 0:
            # Kết hợp nội dung từ các nguồn
            sources_content = ""
            for source in sources:
                if "content" in source and isinstance(source["content"], dict) and "text" in source["content"]:
                    sources_content += source["content"]["text"] + "\n\n"
                elif "content" in source and isinstance(source["content"], str):
                    sources_content += source["content"] + "\n\n"
                elif "snippet" in source:
                    sources_content += source["snippet"] + "\n\n"

            # Phân tích mức độ nhất quán giữa phát biểu và nguồn
            result["factual_statements"] = []
            result["uncertain_statements"] = []
            result["contradictions"] = []

            for statement in statements:
                # Kiểm tra xem phát biểu có trong nguồn không
                support_level = self._check_statement_support(statement, sources_content)

                if support_level > 0.7:  # Phát biểu được hỗ trợ mạnh mẽ
                    result["factual_statements"].append(statement)
                elif 0.3 <= support_level <= 0.7:  # Phát biểu có một số hỗ trợ
                    result["uncertain_statements"].append(statement)
                else:  # Phát biểu không được hỗ trợ hoặc mâu thuẫn
                    # Kiểm tra thêm xem có mâu thuẫn không
                    if self._check_contradiction(statement, sources_content):
                        result["contradictions"].append(statement)
                    else:
                        result["uncertain_statements"].append(statement)

            # Tính điểm dựa trên tỷ lệ phát biểu chính xác
            total_statements = len(statements)
            if total_statements > 0:
                factual_ratio = len(result["factual_statements"]) / total_statements
                uncertain_penalty = len(result["uncertain_statements"]) / total_statements * 0.5
                contradiction_penalty = len(result["contradictions"]) / total_statements * 2

                # Công thức tính điểm: factual_ratio - contradiction_penalty - uncertain_penalty
                raw_score = factual_ratio - contradiction_penalty - uncertain_penalty
                result["score"] = max(0.0, min(1.0, raw_score))

                # Độ tin cậy dựa trên số lượng statements
                result["confidence"] = min(0.95, 0.5 + 0.05 * total_statements)
            else:
                result["score"] = 0.5  # Điểm trung bình nếu không có statement
                result["confidence"] = 0.3  # Độ tin cậy thấp

            # Thêm giải thích
            if result["score"] >= 0.8:
                result["explanation"] = "Nội dung có độ chính xác cao và được hỗ trợ tốt bởi các nguồn"
            elif result["score"] >= 0.6:
                result["explanation"] = "Nội dung phần lớn chính xác với một số phát biểu chưa xác minh"
            elif result["score"] >= 0.4:
                result["explanation"] = "Nội dung có sự kết hợp giữa các phát biểu chính xác và chưa xác minh"
            else:
                result["explanation"] = "Nội dung có vấn đề nghiêm trọng về tính chính xác hoặc mâu thuẫn"

        # Nếu không có nguồn, sử dụng phương pháp phân tích ngôn ngữ
        else:
            # Phân tích tính mạch lạc của nội dung
            coherence_score = 0.7  # Giá trị mặc định trong trường hợp không có phương thức đánh giá

            # Kiểm tra các từ chỉ sự không chắc chắn
            uncertainty_words = ["maybe", "perhaps", "possibly", "might", "could", "potentially",
                              "có thể", "có lẽ", "dường như", "hình như", "nghe nói", "theo đồn"]

            uncertainty_count = sum(1 for word in uncertainty_words if word in content.lower())
            uncertainty_score = min(1.0, uncertainty_count / 10)  # Giới hạn ở 1.0

            # Kiểm tra các từ ngữ cực đoan
            extreme_words = ["always", "never", "all", "none", "definitely", "absolutely", "certainly",
                          "luôn luôn", "không bao giờ", "tất cả", "không ai", "chắc chắn", "tuyệt đối"]

            extreme_count = sum(1 for word in extreme_words if word in content.lower())
            extreme_score = min(1.0, extreme_count / 10)  # Giới hạn ở 1.0

            # Tính toán điểm dựa trên các yếu tố phân tích
            result["score"] = 0.5 + (coherence_score * 0.2) - (uncertainty_score * 0.2) - (extreme_score * 0.2)
            result["score"] = max(0.0, min(1.0, result["score"]))

            # Đặt độ tin cậy thấp hơn vì không có nguồn
            result["confidence"] = 0.4

            # Thêm giải thích
            result["explanation"] = "Đánh giá dựa trên phân tích ngôn ngữ mà không có nguồn tham khảo"

            # Phân tách câu để tạo statements
            sentences = re.split(r'[.!?]', content)
            sentences = [s.strip() for s in sentences if s.strip()]

            # Phân loại các câu thành factual hoặc uncertain
            result["factual_statements"] = [s for s in sentences if not any(uw in s.lower() for uw in uncertainty_words)]
            result["uncertain_statements"] = [s for s in sentences if any(uw in s.lower() for uw in uncertainty_words)]

        # Thêm các vấn đề nếu có
        if result["contradictions"]:
            result["issues"].append("Nội dung chứa phát biểu mâu thuẫn với nguồn")

        if len(result["uncertain_statements"]) > len(result["factual_statements"]) and len(statements) > 3:
            result["issues"].append("Nội dung chứa nhiều phát biểu chưa xác minh hơn phát biểu đã xác minh")

        if result["score"] < 0.4:
            result["issues"].append("Độ chính xác tổng thể thấp")

        return result

    def _extract_factual_statements(self, content: str) -> List[str]:
        """
        Trích xuất các phát biểu thực tế từ nội dung.

        Args:
            content: Nội dung cần phân tích

        Returns:
            Danh sách các phát biểu thực tế
        """
        # Phân tách nội dung thành các câu
        sentences = re.split(r'[.!?]', content)
        sentences = [s.strip() for s in sentences if len(s.strip()) > 10]

        # Lọc các câu có khả năng là phát biểu thực tế
        factual_statements = []

        for sentence in sentences:
            # Loại bỏ các câu hỏi
            if sentence.endswith("?") or any(qw in sentence.lower() for qw in ["what", "who", "where", "when", "why", "how"]):
                continue

            # Loại bỏ các phát biểu ý kiến
            opinion_indicators = ["i think", "i believe", "in my opinion", "i feel", "personally",
                               "tôi nghĩ", "tôi tin", "theo ý kiến của tôi", "tôi cảm thấy", "cá nhân tôi"]

            if any(oi in sentence.lower() for oi in opinion_indicators):
                continue

            # Loại bỏ các câu quá ngắn hoặc quá dài
            word_count = len(sentence.split())
            if word_count < 5 or word_count > 50:
                continue

            # Thêm vào danh sách phát biểu thực tế
            factual_statements.append(sentence)

        return factual_statements

    def _check_statement_support(self, statement: str, sources_content: str) -> float:
        """
        Kiểm tra mức độ hỗ trợ của nguồn tham khảo cho một phát biểu.

        Args:
            statement: Phát biểu cần kiểm tra
            sources_content: Nội dung từ các nguồn tham khảo

        Returns:
            Điểm hỗ trợ từ 0.0 đến 1.0
        """
        if not statement or not sources_content:
            return 0.0

        # Trích xuất từ khóa từ phát biểu
        keywords = self._extract_keywords(statement)

        # Đếm số từ khóa xuất hiện trong nguồn
        keyword_count = sum(1 for kw in keywords if kw.lower() in sources_content.lower())

        # Tính tỷ lệ từ khóa được hỗ trợ
        if not keywords:
            return 0.0

        keyword_ratio = keyword_count / len(keywords)

        # Kiểm tra sự xuất hiện của cụm từ
        phrases = self._extract_key_phrases(statement)
        phrase_count = sum(1 for p in phrases if p.lower() in sources_content.lower())
        phrase_ratio = phrase_count / len(phrases) if phrases else 0.0

        # Tính điểm hỗ trợ tổng hợp (trọng số: 60% từ khóa, 40% cụm từ)
        return keyword_ratio * 0.6 + phrase_ratio * 0.4

    def _extract_key_phrases(self, text: str) -> List[str]:
        """
        Trích xuất các cụm từ quan trọng từ văn bản.

        Args:
            text: Văn bản cần phân tích

        Returns:
            Danh sách các cụm từ quan trọng
        """
        # Phân tách thành các từ
        words = text.split()

        # Tạo các cụm từ 2-3 từ
        phrases = []

        # Cụm 2 từ
        for i in range(len(words) - 1):
            phrase = ' '.join(words[i:i+2])
            if len(phrase) > 5:
                phrases.append(phrase)

        # Cụm 3 từ
        for i in range(len(words) - 2):
            phrase = ' '.join(words[i:i+3])
            if len(phrase) > 8:
                phrases.append(phrase)

        return phrases

    def _check_contradiction(self, statement: str, sources_content: str) -> bool:
        """
        Kiểm tra xem phát biểu có mâu thuẫn với nguồn không.

        Args:
            statement: Phát biểu cần kiểm tra
            sources_content: Nội dung từ các nguồn tham khảo

        Returns:
            True nếu có mâu thuẫn, False nếu không
        """
        # Các từ khóa phủ định
        negation_words = ["not", "no", "never", "nobody", "nothing", "neither", "nor",
                       "không", "chẳng", "không phải", "không bao giờ", "chưa từng"]

        # Tách statement thành các từ
        statement_words = statement.lower().split()

        # Kiểm tra xem statement có chứa từ phủ định không
        has_negation = any(neg in statement_words for neg in negation_words)

        # Nếu statement có phủ định, tạo phiên bản không phủ định
        if has_negation:
            # Tạo phiên bản không phủ định (loại bỏ từ phủ định đầu tiên)
            for neg in negation_words:
                if neg in statement_words:
                    non_negated = ' '.join([w for w in statement_words if w != neg])

                    # Kiểm tra xem phiên bản không phủ định có xuất hiện trong nguồn không
                    if non_negated in sources_content.lower():
                        return True
        else:
            # Nếu statement không có phủ định, tạo phiên bản phủ định
            for neg in negation_words[:5]:  # Chỉ sử dụng 5 từ phủ định tiếng Anh
                negated = neg + " " + ' '.join(statement_words)

                # Kiểm tra xem phiên bản phủ định có xuất hiện trong nguồn không
                if negated in sources_content.lower():
                    return True

        # Kiểm tra các mẫu mâu thuẫn cụ thể (ví dụ: A nhưng không phải A)
        contradiction_patterns = [
            r'is not (.*), but is (.*)',
            r'is (.*), not (.*)',
            r'không phải (.*) mà là (.*)',
            r'là (.*), không phải (.*)'
        ]

        for pattern in contradiction_patterns:
            matches = re.search(pattern, sources_content.lower())
            if matches and matches.group(1) in statement.lower() and matches.group(2) in statement.lower():
                return True

        return False

    def _evaluate_relevance(self, content: str, query: str) -> Dict[str, Any]:
        """
        Đánh giá độ liên quan của nội dung với truy vấn.

        Args:
            content: Nội dung cần đánh giá
            query: Truy vấn gốc

        Returns:
            Dict chứa kết quả đánh giá độ liên quan
        """
        result = {
            "relevance_score": 0.0,
            "key_term_coverage": 0.0,
            "topic_alignment": 0.0,
            "semantic_similarity": 0.0,
            "direct_answer": False,
            "explanation": "",
            "relevant_sections": [],
            "missing_aspects": []
        }

        # Kiểm tra nếu content hoặc query trống
        if not content or not query or len(content.strip()) < 10 or len(query.strip()) < 2:
            result["explanation"] = "Không thể đánh giá do nội dung hoặc truy vấn quá ngắn"
            return result

        # Bước 1: Đánh giá độ phủ của từ khóa
        query_keywords = self._extract_keywords(query)

        # Tính tỷ lệ từ khóa xuất hiện trong nội dung
        if query_keywords:
            matched_keywords = sum(1 for kw in query_keywords if kw.lower() in content.lower())
            key_term_coverage = matched_keywords / len(query_keywords)
            result["key_term_coverage"] = key_term_coverage
        else:
            result["key_term_coverage"] = 0.0

        # Bước 2: Đánh giá sự phù hợp về chủ đề
        # Trích xuất chủ đề từ truy vấn
        query_topic = self._extract_main_topic(query)

        # Kiểm tra xem chủ đề có xuất hiện trong nội dung không
        if query_topic:
            topic_frequency = content.lower().count(query_topic.lower())
            topic_alignment = min(1.0, topic_frequency / 3)  # Giới hạn ở 1.0
            result["topic_alignment"] = topic_alignment
        else:
            result["topic_alignment"] = 0.0

        # Bước 3: Đánh giá mức độ trả lời trực tiếp
        # Phân tích loại câu hỏi
        question_types = []
        question_words = ["what", "when", "where", "who", "why", "how", "which",
                        "cái gì", "khi nào", "ở đâu", "ai", "tại sao", "như thế nào", "nào"]

        query_lower = query.lower()

        # Xác định loại câu hỏi
        for qw in question_words:
            if query_lower.startswith(qw) or f" {qw} " in query_lower:
                if qw in ["what", "cái gì"]:
                    question_types.append("definition")
                elif qw in ["when", "khi nào"]:
                    question_types.append("time")
                elif qw in ["where", "ở đâu"]:
                    question_types.append("location")
                elif qw in ["who", "ai"]:
                    question_types.append("person")
                elif qw in ["why", "tại sao"]:
                    question_types.append("reason")
                elif qw in ["how", "như thế nào"]:
                    question_types.append("method")
                elif qw in ["which", "nào"]:
                    question_types.append("choice")
                break

        # Các mẫu câu trả lời phù hợp với từng loại câu hỏi
        answer_patterns = {
            "definition": [r'(?:is|are|refers to|means|là|được định nghĩa|có nghĩa) (?:a|an|the|một|các|những)?',
                        r'(?:can be defined|được hiểu|được xem) as'],
            "time": [r'(?:in|on|at|during|vào|trong) (?:the year|năm)? \d+',
                   r'(?:yesterday|today|tomorrow|hôm qua|hôm nay|ngày mai)',
                   r'(?:morning|afternoon|evening|sáng|chiều|tối|đêm)'],
            "location": [r'(?:in|on|at|near|vào|tại|gần) (?:the|một|các)?',
                       r'located (?:in|on|at|near|vào|tại|gần)'],
            "person": [r'(?:he|she|they|người|ông|bà|anh|chị|họ) (?:is|are|was|were|là|đã|sẽ|đang)',
                     r'born (?:in|on|at|vào|tại) (?:the year|năm)? \d+'],
            "reason": [r'(?:because|since|as|due to|bởi vì|vì|do|tại)',
                     r'(?:the reason|lý do|nguyên nhân) (?:is|was|là)'],
            "method": [r'(?:by|through|using|với|bằng cách|thông qua|sử dụng)',
                     r'(?:first|second|third|next|then|đầu tiên|thứ hai|thứ ba|tiếp theo|sau đó)'],
            "choice": [r'(?:better|best|worse|worst|preferred|tốt hơn|tốt nhất|kém hơn|kém nhất|ưu tiên)',
                     r'(?:chosen|selected|recommended|được chọn|được lựa chọn|được khuyến nghị)']
        }

        # Kiểm tra xem nội dung có chứa mẫu câu trả lời phù hợp không
        has_direct_answer = False
        for qt in question_types:
            if qt in answer_patterns:
                for pattern in answer_patterns[qt]:
                    if re.search(pattern, content.lower()):
                        has_direct_answer = True
                        break
                if has_direct_answer:
                    break

        result["direct_answer"] = has_direct_answer

        # Bước 4: Đánh giá độ tương đồng ngữ nghĩa (phiên bản đơn giản)
        # Trích xuất các khía cạnh từ truy vấn
        query_facets = self._extract_facets(query)

        # Kiểm tra xem nội dung đề cập đến các khía cạnh nào
        if query_facets:
            matched_facets = sum(1 for facet in query_facets if facet.lower() in content.lower())
            semantic_similarity = matched_facets / len(query_facets) if query_facets else 0.0
            result["semantic_similarity"] = semantic_similarity

            # Xác định các khía cạnh còn thiếu
            result["missing_aspects"] = [facet for facet in query_facets if facet.lower() not in content.lower()]
        else:
            result["semantic_similarity"] = result["key_term_coverage"]  # Dùng độ phủ từ khóa nếu không có khía cạnh

        # Bước 5: Tìm các đoạn văn liên quan nhất
        sentences = re.split(r'[.!?]', content)
        sentences = [s.strip() for s in sentences if len(s.strip()) > 10]

        # Tính điểm liên quan cho từng câu
        relevant_sentences = []
        for sentence in sentences:
            sentence_relevance = 0.0

            # Kiểm tra từ khóa
            if query_keywords:
                matched_kw = sum(1 for kw in query_keywords if kw.lower() in sentence.lower())
                sentence_relevance += (matched_kw / len(query_keywords)) * 0.6

            # Kiểm tra chủ đề
            if query_topic and query_topic.lower() in sentence.lower():
                sentence_relevance += 0.4

            # Lưu câu có điểm > 0.3
            if sentence_relevance > 0.3:
                relevant_sentences.append({
                    "text": sentence,
                    "relevance": sentence_relevance
                })

        # Sắp xếp theo độ liên quan giảm dần và lấy 3 câu liên quan nhất
        relevant_sentences.sort(key=lambda x: x["relevance"], reverse=True)
        result["relevant_sections"] = relevant_sentences[:3]

        # Bước 6: Tính điểm liên quan tổng thể
        # Trọng số: 40% độ phủ từ khóa, 25% sự phù hợp chủ đề, 25% độ tương đồng ngữ nghĩa, 10% trả lời trực tiếp
        relevance_score = (
            result["key_term_coverage"] * 0.4 +
            result["topic_alignment"] * 0.25 +
            result["semantic_similarity"] * 0.25 +
            (0.1 if result["direct_answer"] else 0.0)
        )

        result["relevance_score"] = max(0.0, min(1.0, relevance_score))

        # Thêm giải thích
        if result["relevance_score"] >= 0.8:
            result["explanation"] = "Nội dung rất liên quan đến truy vấn, bao gồm hầu hết các từ khóa và khía cạnh quan trọng"
        elif result["relevance_score"] >= 0.6:
            result["explanation"] = "Nội dung khá liên quan đến truy vấn, bao gồm nhiều từ khóa quan trọng"
        elif result["relevance_score"] >= 0.4:
            result["explanation"] = "Nội dung có một số liên quan đến truy vấn, nhưng thiếu một số khía cạnh quan trọng"
        elif result["relevance_score"] >= 0.2:
            result["explanation"] = "Nội dung có ít liên quan đến truy vấn, chỉ đề cập đến một số từ khóa"
        else:
            result["explanation"] = "Nội dung hầu như không liên quan đến truy vấn"

        return result

    def _evaluate_completeness(self, content: str, query: str = None, topic: str = None) -> Dict[str, Any]:
        """
        Đánh giá tính đầy đủ của nội dung.

        Args:
            content: Nội dung cần đánh giá
            query: Truy vấn gốc (tùy chọn)
            topic: Chủ đề (tùy chọn, được sử dụng nếu không có query)

        Returns:
            Dict chứa kết quả đánh giá tính đầy đủ
        """
        result = {
            "completeness_score": 0.0,
            "length_score": 0.0,
            "depth_score": 0.0,
            "breadth_score": 0.0,
            "structure_score": 0.0,
            "missing_elements": [],
            "explanation": "",
            "suggested_improvements": []
        }

        # Kiểm tra nếu content trống
        if not content or len(content.strip()) < 10:
            result["explanation"] = "Nội dung quá ngắn để đánh giá"
            return result

        # Xác định chủ đề từ query nếu chưa có
        main_topic = topic
        if not main_topic and query:
            main_topic = self._extract_main_topic(query)

        # Bước 1: Đánh giá độ dài (length)
        content_length = len(content)
        word_count = len(content.split())

        # Đánh giá độ dài dựa trên số từ
        if word_count >= 500:  # Nội dung dài, có khả năng đầy đủ
            length_score = 1.0
        elif word_count >= 300:
            length_score = 0.8
        elif word_count >= 150:
            length_score = 0.6
        elif word_count >= 80:
            length_score = 0.4
        else:
            length_score = 0.2  # Nội dung quá ngắn, có khả năng thiếu

        result["length_score"] = length_score

        # Nếu quá ngắn, thêm gợi ý cải thiện
        if word_count < 150:
            result["suggested_improvements"].append("Mở rộng nội dung để bao gồm thêm thông tin")
            result["missing_elements"].append("insufficient_length")

        # Bước 2: Đánh giá chiều sâu (depth)
        # Phân tích số lượng dữ liệu cụ thể: số liệu, ngày tháng, tên riêng, trích dẫn

        # Đếm số liệu
        numbers = len(re.findall(r'\b\d+(?:,\d+)*(?:\.\d+)?\b', content))

        # Đếm ngày tháng
        dates = len(re.findall(r'\b\d{1,2}[-/]\d{1,2}[-/]\d{2,4}\b|\b(?:January|February|March|April|May|June|July|August|September|October|November|December|Jan|Feb|Mar|Apr|Jun|Jul|Aug|Sep|Oct|Nov|Dec|Tháng \d{1,2})\s+\d{1,2}(?:st|nd|rd|th)?,?\s+\d{2,4}\b', content))

        # Đếm tên riêng (ước lượng bằng cách đếm từ viết hoa không ở đầu câu)
        capitalized_words = len(re.findall(r'(?<!^)(?<!\. )[A-Z][a-z]+', content))

        # Đếm trích dẫn
        quotes = len(re.findall(r'"([^"]+)"|\(([^)]+)\)', content))

        # Tính điểm chiều sâu
        specific_data_count = numbers + dates + min(capitalized_words, 20) + quotes

        if specific_data_count >= 15:
            depth_score = 1.0
        elif specific_data_count >= 10:
            depth_score = 0.8
        elif specific_data_count >= 5:
            depth_score = 0.6
        elif specific_data_count >= 2:
            depth_score = 0.4
        else:
            depth_score = 0.2

        result["depth_score"] = depth_score

        # Nếu thiếu chi tiết, thêm gợi ý cải thiện
        if specific_data_count < 5:
            result["suggested_improvements"].append("Bổ sung thêm dữ liệu cụ thể: số liệu, ngày tháng, tên riêng, trích dẫn")
            result["missing_elements"].append("insufficient_details")

        # Bước 3: Đánh giá chiều rộng (breadth)
        # Xác định có bao nhiêu khía cạnh khác nhau được đề cập

        # Nếu có query, trích xuất các khía cạnh từ query
        expected_aspects = []
        if query:
            expected_aspects = self._extract_facets(query)

        # Nếu không có khía cạnh từ query, sử dụng các khía cạnh phổ biến dựa trên chủ đề
        if not expected_aspects and main_topic:
            # Ánh xạ chủ đề sang các khía cạnh phổ biến
            common_aspects = {
                "country": ["history", "geography", "economy", "culture", "politics", "demographics"],
                "person": ["early life", "career", "achievements", "personal life", "legacy"],
                "organization": ["history", "structure", "operations", "achievements", "controversy"],
                "event": ["causes", "timeline", "participants", "outcomes", "significance"],
                "product": ["features", "specifications", "history", "usage", "comparison"],
                "concept": ["definition", "history", "applications", "examples", "importance"],
                "process": ["steps", "requirements", "timeline", "advantages", "limitations"],
                "problem": ["causes", "symptoms", "solutions", "prevention", "impact"]
            }

            # Phát hiện loại chủ đề cơ bản
            topic_type = "concept"  # Mặc định
            topic_lower = main_topic.lower()

            # Thử xác định loại chủ đề
            if any(kw in topic_lower for kw in ["country", "nation", "city", "region", "quốc gia", "thành phố", "vùng", "tỉnh"]):
                topic_type = "country"
            elif any(kw in topic_lower for kw in ["person", "người", "ông", "bà", "anh", "chị"]):
                topic_type = "person"
            elif any(kw in topic_lower for kw in ["company", "organization", "association", "công ty", "tổ chức", "hiệp hội"]):
                topic_type = "organization"
            elif any(kw in topic_lower for kw in ["event", "incident", "ceremony", "sự kiện", "biến cố", "lễ"]):
                topic_type = "event"
            elif any(kw in topic_lower for kw in ["product", "device", "tool", "sản phẩm", "thiết bị", "công cụ"]):
                topic_type = "product"
            elif any(kw in topic_lower for kw in ["process", "method", "technique", "quy trình", "phương pháp", "kỹ thuật"]):
                topic_type = "process"
            elif any(kw in topic_lower for kw in ["problem", "issue", "challenge", "vấn đề", "thách thức", "khó khăn"]):
                topic_type = "problem"

            expected_aspects = common_aspects.get(topic_type, ["definition", "examples", "applications"])

        # Đếm số khía cạnh được đề cập trong nội dung
        covered_aspects = []
        missing_aspects = []

        for aspect in expected_aspects:
            if aspect.lower() in content.lower():
                covered_aspects.append(aspect)
            else:
                missing_aspects.append(aspect)

        # Thêm vào kết quả
        result["missing_elements"].extend(missing_aspects)

        # Tính điểm chiều rộng
        if expected_aspects:
            breadth_score = len(covered_aspects) / len(expected_aspects)
        else:
            # Nếu không có expected aspects, đánh giá dựa trên cấu trúc và phân đoạn
            paragraphs = content.split('\n\n')
            if len(paragraphs) >= 5:
                breadth_score = 1.0
            elif len(paragraphs) >= 3:
                breadth_score = 0.7
            else:
                breadth_score = 0.4

        result["breadth_score"] = breadth_score

        # Nếu thiếu khía cạnh, thêm gợi ý cải thiện
        if missing_aspects:
            missing_aspects_str = ", ".join(missing_aspects[:3])
            result["suggested_improvements"].append(f"Bổ sung thêm các khía cạnh: {missing_aspects_str}")

        # Bước 4: Đánh giá cấu trúc (structure)
        # Kiểm tra có bao nhiêu yếu tố cấu trúc: đoạn văn, tiêu đề, danh sách, etc.

        # Đếm số đoạn văn
        paragraphs = len([p for p in content.split('\n\n') if len(p.strip()) > 0])

        # Đếm số tiêu đề (dòng ngắn và độc lập)
        headings = len(re.findall(r'(?:^|\n)([A-Z][A-Za-z0-9\s]{1,50})(?:\n|$)', content))

        # Đếm số danh sách (dòng bắt đầu bằng dấu gạch đầu dòng hoặc số)
        list_items = len(re.findall(r'(?:^|\n)[\-\*\•\d+\)\.][ \t]+\w+', content, re.IGNORECASE))

        # Tính điểm cấu trúc
        structure_elements = paragraphs + headings + list_items

        if structure_elements >= 10:
            structure_score = 1.0
        elif structure_elements >= 7:
            structure_score = 0.8
        elif structure_elements >= 4:
            structure_score = 0.6
        elif structure_elements >= 2:
            structure_score = 0.4
        else:
            structure_score = 0.2

        result["structure_score"] = structure_score

        # Nếu cấu trúc kém, thêm gợi ý cải thiện
        if paragraphs < 3:
            result["suggested_improvements"].append("Cải thiện cấu trúc bài viết: thêm đoạn văn, tiêu đề, danh sách")
            result["missing_elements"].append("poor_structure")

        # Bước 5: Tính điểm đầy đủ tổng thể
        # Trọng số: 25% độ dài, 30% chiều sâu, 30% chiều rộng, 15% cấu trúc
        completeness_score = (
            result["length_score"] * 0.25 +
            result["depth_score"] * 0.30 +
            result["breadth_score"] * 0.30 +
            result["structure_score"] * 0.15
        )

        result["completeness_score"] = max(0.0, min(1.0, completeness_score))

        # Thêm giải thích
        if result["completeness_score"] >= 0.8:
            result["explanation"] = "Nội dung rất đầy đủ, bao gồm đủ chiều sâu và chiều rộng"
        elif result["completeness_score"] >= 0.6:
            result["explanation"] = "Nội dung khá đầy đủ, nhưng có thể cải thiện thêm một số khía cạnh"
        elif result["completeness_score"] >= 0.4:
            result["explanation"] = "Nội dung còn thiếu một số yếu tố quan trọng, cần bổ sung thêm"
        else:
            result["explanation"] = "Nội dung không đầy đủ, thiếu nhiều yếu tố quan trọng"

        return result

    def _evaluate_source_diversity(self, sources: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Đánh giá sự đa dạng của nguồn thông tin.

        Args:
            sources: Danh sách các nguồn thông tin

        Returns:
            Dict chứa kết quả đánh giá sự đa dạng nguồn
        """
        result = {
            "diversity_score": 0.0,
            "domain_diversity_score": 0.0,
            "type_diversity_score": 0.0,
            "perspective_diversity_score": 0.0,
            "freshness_score": 0.0,
            "domain_distribution": {},
            "type_distribution": {},
            "date_distribution": {},
            "perspective_analysis": {},
            "explanation": "",
            "suggestions": []
        }

        # Kiểm tra nếu không có nguồn
        if not sources or len(sources) == 0:
            result["explanation"] = "Không có nguồn thông tin để đánh giá"
            result["suggestions"].append("Thêm nguồn thông tin từ nhiều trang web khác nhau")
            return result

        # Bước 1: Phân tích sự đa dạng về domain
        domains = []
        domain_counts = {}

        for source in sources:
            # Trích xuất domain từ URL
            url = source.get("url", "")
            if url:
                domain = self._extract_domain(url)
                domains.append(domain)

                # Đếm số lần xuất hiện của mỗi domain
                domain_counts[domain] = domain_counts.get(domain, 0) + 1

        # Tính điểm đa dạng domain
        # Nếu tất cả các nguồn đều từ các domain khác nhau, điểm là 1.0
        unique_domains = len(set(domains))

        if len(domains) > 0:
            # Điểm phân bố domain (dựa trên số lượng domain duy nhất)
            domain_diversity_score = unique_domains / len(domains)

            # Tính chỉ số phân tán (diversity index)
            # Sử dụng chỉ số Simpson's Diversity Index: 1 - sum((n/N)^2)
            # n: số lượng của mỗi domain, N: tổng số nguồn
            total_sources = len(domains)
            simpson_index = 1.0 - sum((count / total_sources) ** 2 for count in domain_counts.values())

            # Kết hợp hai điểm số (trọng số: 40% số lượng domain duy nhất, 60% phân bố)
            domain_diversity_score = 0.4 * domain_diversity_score + 0.6 * simpson_index
        else:
            domain_diversity_score = 0.0

        result["domain_diversity_score"] = domain_diversity_score
        result["domain_distribution"] = domain_counts

        # Bước 2: Phân tích sự đa dạng về loại nguồn
        # Phân loại nguồn dựa trên domain
        source_types = {
            "news": ["news", "bbc", "cnn", "reuters", "guardian", "nytimes", "washingtonpost", "foxnews",
                   "vnexpress", "tuoitre", "thanhnien", "dantri", "vietnamnet", "baomoi", "vtv", "vov"],
            "academic": ["edu", "ac.uk", "academia", "researchgate", "scholar", "sciencedirect", "ieee",
                       "springer", "nature", "science", "wiley", "ncbi", "pubmed", "jstor"],
            "government": ["gov", "europa.eu", "un.org", "who.int", "worldbank.org", "imf.org",
                         "gov.vn", "chinhphu.vn"],
            "encyclopedia": ["wikipedia", "britannica", "dictionary", "encyclopedia", "wiki"],
            "blog": ["blog", "medium", "wordpress", "blogger", "tumblr", "substack"],
            "social_media": ["facebook", "twitter", "linkedin", "reddit", "instagram", "tiktok", "youtube"],
            "commercial": ["com", "co", "biz", "shop", "store", "amazon", "ebay"]
        }

        # Đếm số lượng của mỗi loại nguồn
        type_counts = {}

        for domain in domains:
            source_type = "other"  # Mặc định

            # Kiểm tra domain thuộc loại nào
            for type_name, type_keywords in source_types.items():
                if any(kw in domain for kw in type_keywords):
                    source_type = type_name
                    break

            # Đếm số lượng của mỗi loại
            type_counts[source_type] = type_counts.get(source_type, 0) + 1

        # Tính điểm đa dạng loại nguồn
        unique_types = len(type_counts)

        if len(domains) > 0:
            # Điểm đa dạng dựa trên số loại nguồn
            # Lý tưởng nhất là có ít nhất 3 loại nguồn khác nhau
            type_count_score = min(1.0, unique_types / 3)

            # Tính chỉ số phân tán Simpson cho loại nguồn
            total_sources = len(domains)
            simpson_index = 1.0 - sum((count / total_sources) ** 2 for count in type_counts.values())

            # Kết hợp hai điểm số (trọng số: 40% số lượng loại, 60% phân bố)
            type_diversity_score = 0.4 * type_count_score + 0.6 * simpson_index
        else:
            type_diversity_score = 0.0

        result["type_diversity_score"] = type_diversity_score
        result["type_distribution"] = type_counts

        # Bước 3: Phân tích sự đa dạng về thời gian (freshness)
        # Phân tích thời gian xuất bản (nếu có)
        date_counts = {
            "recent": 0,  # < 1 năm
            "moderate": 0,  # 1-3 năm
            "old": 0  # > 3 năm
        }

        # Lấy thời gian hiện tại
        current_time = time.time()
        one_year_ago = current_time - (365 * 24 * 60 * 60)
        three_years_ago = current_time - (3 * 365 * 24 * 60 * 60)

        for source in sources:
            # Thử lấy thời gian từ các trường khác nhau
            pub_date = None

            if "date" in source:
                pub_date = source["date"]
            elif "published_date" in source:
                pub_date = source["published_date"]
            elif "timestamp" in source:
                pub_date = source["timestamp"]

            # Nếu có thời gian, phân loại
            if pub_date:
                try:
                    # Thử chuyển đổi sang timestamp
                    if isinstance(pub_date, str):
                        # Xử lý một số định dạng phổ biến
                        formats = [
                            "%Y-%m-%d", "%Y/%m/%d", "%d-%m-%Y", "%d/%m/%Y",
                            "%Y-%m-%d %H:%M:%S", "%Y/%m/%d %H:%M:%S",
                            "%b %d, %Y", "%B %d, %Y", "%d %b %Y", "%d %B %Y"
                        ]

                        for fmt in formats:
                            try:
                                dt = datetime.strptime(pub_date, fmt)
                                pub_timestamp = dt.timestamp()
                                break
                            except:
                                continue
                        else:
                            # Nếu không khớp với bất kỳ định dạng nào
                            pub_timestamp = None
                    elif isinstance(pub_date, (int, float)):
                        pub_timestamp = pub_date
                    else:
                        pub_timestamp = None

                    # Phân loại thời gian
                    if pub_timestamp > one_year_ago:
                        date_counts["recent"] += 1
                    elif pub_timestamp > three_years_ago:
                        date_counts["moderate"] += 1
                    else:
                        date_counts["old"] += 1
                except:
                    # Nếu có lỗi khi xử lý thời gian, bỏ qua
                    pass

        # Tính điểm đa dạng thời gian và độ mới
        total_dated_sources = sum(date_counts.values())

        if total_dated_sources > 0:
            # Tỷ lệ nguồn mới
            recent_ratio = date_counts["recent"] / total_dated_sources

            # Tính điểm đa dạng thời gian
            # Lý tưởng là có sự kết hợp giữa các nguồn mới và cũ
            has_recent = date_counts["recent"] > 0
            has_moderate = date_counts["moderate"] > 0
            has_old = date_counts["old"] > 0

            time_diversity = 0.0
            if has_recent and has_moderate and has_old:
                time_diversity = 1.0
            elif (has_recent and has_moderate) or (has_recent and has_old) or (has_moderate and has_old):
                time_diversity = 0.7
            elif has_recent or has_moderate or has_old:
                time_diversity = 0.3

            # Ưu tiên nguồn mới hơn, nhưng vẫn đánh giá cao sự đa dạng
            freshness_score = 0.7 * recent_ratio + 0.3 * time_diversity
        else:
            freshness_score = 0.0

        result["freshness_score"] = freshness_score
        result["date_distribution"] = date_counts

        # Bước 4: Phân tích sự đa dạng về quan điểm (perspective)
        # Đây là bản ước lượng đơn giản, phân tích thực tế cần NLP nâng cao

        # Phân loại nguồn theo khuynh hướng
        perspective_indicators = {
            "left": ["guardian", "nytimes", "washingtonpost", "vox", "msnbc", "huffpost", "buzzfeed"],
            "center": ["reuters", "apnews", "bbc", "npr", "pbs", "abc", "cbs"],
            "right": ["foxnews", "breitbart", "dailycaller", "nypost", "washingtonexaminer", "wsj"],
            "scientific": ["nature", "science", "sciencedirect", "springer", "wiley", "ieee", "cell", "nejm"],
            "educational": ["edu", "ac.uk", ".edu.", "wikipedia", "britannica"],
            "government": ["gov", ".gov.", "un.org", "who.int", "europa.eu"]
        }

        # Đếm số lượng của mỗi khuynh hướng
        perspective_counts = {}

        for domain in domains:
            perspective = "unknown"  # Mặc định

            # Kiểm tra domain thuộc khuynh hướng nào
            for persp, indicators in perspective_indicators.items():
                if any(ind in domain for ind in indicators):
                    perspective = persp
                    break

            # Đếm số lượng của mỗi khuynh hướng
            perspective_counts[perspective] = perspective_counts.get(perspective, 0) + 1

        # Tính điểm đa dạng khuynh hướng
        unique_perspectives = len([k for k, v in perspective_counts.items() if k != "unknown" and v > 0])

        # Lý tưởng là có ít nhất 3 khuynh hướng khác nhau
        if len(domains) > 0:
            perspective_count_score = min(1.0, unique_perspectives / 3)

            # Loại bỏ "unknown" khi tính chỉ số phân tán
            known_sources = len(domains) - perspective_counts.get("unknown", 0)

            if known_sources > 0:
                # Tính chỉ số phân tán Simpson cho khuynh hướng (chỉ với khuynh hướng đã biết)
                simpson_index = 1.0 - sum((perspective_counts.get(persp, 0) / known_sources) ** 2
                                      for persp in perspective_counts if persp != "unknown")

                # Kết hợp hai điểm số (trọng số: 40% số lượng khuynh hướng, 60% phân bố)
                perspective_diversity_score = 0.4 * perspective_count_score + 0.6 * simpson_index
            else:
                perspective_diversity_score = 0.0
        else:
            perspective_diversity_score = 0.0

        result["perspective_diversity_score"] = perspective_diversity_score
        result["perspective_analysis"] = perspective_counts

        # Bước 5: Tính điểm đa dạng tổng thể
        # Trọng số: 40% domain, 25% loại nguồn, 20% khuynh hướng, 15% thời gian
        diversity_score = (
            result["domain_diversity_score"] * 0.4 +
            result["type_diversity_score"] * 0.25 +
            result["perspective_diversity_score"] * 0.20 +
            result["freshness_score"] * 0.15
        )

        result["diversity_score"] = max(0.0, min(1.0, diversity_score))

        # Thêm giải thích và gợi ý
        if result["diversity_score"] >= 0.8:
            result["explanation"] = "Nguồn thông tin rất đa dạng, bao gồm nhiều domain, loại nguồn và quan điểm khác nhau"
        elif result["diversity_score"] >= 0.6:
            result["explanation"] = "Nguồn thông tin khá đa dạng, nhưng có thể cải thiện thêm về một số khía cạnh"
        elif result["diversity_score"] >= 0.4:
            result["explanation"] = "Nguồn thông tin có độ đa dạng trung bình, cần thêm nhiều nguồn khác nhau"
        else:
            result["explanation"] = "Nguồn thông tin thiếu đa dạng, quá tập trung vào một số ít nguồn"

        # Thêm các gợi ý cụ thể
        if unique_domains < 3:
            result["suggestions"].append("Thêm nguồn từ các trang web khác nhau")

        if unique_types < 2:
            result["suggestions"].append("Thêm nguồn từ các loại khác nhau (học thuật, tin tức, chính phủ)")

        if unique_perspectives < 2:
            result["suggestions"].append("Thêm nguồn có quan điểm đa dạng hơn")

        if freshness_score < 0.5:
            result["suggestions"].append("Thêm các nguồn cập nhật gần đây")

        return result

    def _evaluate_content_richness(self, content: str, query: str = None) -> Dict[str, Any]:
        """
        Đánh giá sự phong phú của nội dung.

        Args:
            content: Nội dung cần đánh giá
            query: Truy vấn gốc (tùy chọn)

        Returns:
            Dict chứa kết quả đánh giá sự phong phú của nội dung
        """
        result = {
            "richness_score": 0.0,
            "information_density_score": 0.0,
            "vocabulary_richness_score": 0.0,
            "media_enrichment_score": 0.0,
            "structure_complexity_score": 0.0,
            "explanation": "",
            "strengths": [],
            "weaknesses": []
        }

        # Kiểm tra nếu content trống
        if not content or len(content.strip()) < 10:
            result["explanation"] = "Nội dung quá ngắn để đánh giá"
            return result

        # Bước 1: Đánh giá mật độ thông tin (information density)
        # Đếm các yếu tố thông tin: con số, ngày tháng, tên riêng, thuật ngữ chuyên ngành

        # Đếm số lượng
        numbers = len(re.findall(r'\b\d+(?:,\d+)*(?:\.\d+)?\b', content))

        # Đếm ngày tháng
        dates = len(re.findall(r'\b\d{1,2}[-/]\d{1,2}[-/]\d{2,4}\b|\b(?:January|February|March|April|May|June|July|August|September|October|November|December|Jan|Feb|Mar|Apr|Jun|Jul|Aug|Sep|Oct|Nov|Dec|Tháng \d{1,2})\s+\d{1,2}(?:st|nd|rd|th)?,?\s+\d{2,4}\b', content))

        # Đếm tên riêng (ước lượng bằng cách đếm từ viết hoa không ở đầu câu)
        proper_nouns = len(re.findall(r'(?<!^)(?<!\. )[A-Z][a-z]+', content))

        # Đếm trích dẫn
        quotes = len(re.findall(r'"([^"]+)"|\(([^)]+)\)', content))

        # Đếm các liên kết
        links = len(re.findall(r'https?://\S+|www\.\S+', content))

        # Đếm số từ
        word_count = len(content.split())

        # Tính mật độ thông tin
        if word_count > 0:
            info_elements = numbers + dates + min(proper_nouns, 20) + quotes + links
            info_density = info_elements / (word_count / 100)  # Số yếu tố thông tin trên 100 từ

            # Chuẩn hóa điểm mật độ thông tin
            if info_density >= 10:
                information_density_score = 1.0
            elif info_density >= 7:
                information_density_score = 0.8
            elif info_density >= 5:
                information_density_score = 0.6
            elif info_density >= 3:
                information_density_score = 0.4
            else:
                information_density_score = 0.2
        else:
            information_density_score = 0.0

        result["information_density_score"] = information_density_score

        # Thêm điểm mạnh/yếu dựa trên mật độ thông tin
        if information_density_score >= 0.7:
            result["strengths"].append("Nội dung giàu thông tin với nhiều dữ liệu cụ thể")
        elif information_density_score <= 0.3:
            result["weaknesses"].append("Thiếu dữ liệu cụ thể, nội dung có ít yếu tố thông tin")

        # Bước 2: Đánh giá sự phong phú của từ vựng (vocabulary richness)
        words = re.findall(r'\b[a-zA-Z\u00C0-\u1EF9]+\b', content.lower())  # Bao gồm cả từ tiếng Việt

        # Đếm số từ unique
        unique_words = len(set(words))

        # Tính tỷ lệ type-token (TTR)
        if len(words) > 0:
            ttr = unique_words / len(words)

            # Chuẩn hóa TTR (type-token ratio)
            # TTR thường giảm khi độ dài văn bản tăng, nên cần điều chỉnh theo độ dài
            if len(words) <= 100:
                normalized_ttr = ttr
            else:
                # Sử dụng Root TTR để chuẩn hóa theo độ dài
                normalized_ttr = unique_words / math.sqrt(len(words))
                normalized_ttr = min(1.0, normalized_ttr / 10)  # Chuẩn hóa về thang điểm 0-1

            # Đếm từ hiếm (từ dài)
            long_words = sum(1 for word in words if len(word) > 7)
            long_word_ratio = long_words / len(words) if len(words) > 0 else 0

            # Tính điểm phong phú từ vựng tổng hợp
            vocabulary_richness_score = 0.7 * normalized_ttr + 0.3 * min(1.0, long_word_ratio * 5)
        else:
            vocabulary_richness_score = 0.0

        result["vocabulary_richness_score"] = vocabulary_richness_score

        # Thêm điểm mạnh/yếu dựa trên sự phong phú từ vựng
        if vocabulary_richness_score >= 0.7:
            result["strengths"].append("Sử dụng từ vựng phong phú và đa dạng")
        elif vocabulary_richness_score <= 0.3:
            result["weaknesses"].append("Từ vựng đơn điệu, sử dụng nhiều từ lặp lại")

        # Bước 3: Đánh giá sự phong phú về phương tiện (media enrichment)
        # Kiểm tra xem nội dung có chứa hình ảnh, video, biểu đồ, bảng, etc.

        # Đếm số lượng hình ảnh
        images = len(re.findall(r'!\[.*?\]\(.*?\)|<img[^>]+>|\.(jpg|jpeg|png|gif|svg|webp)["\s\)]', content, re.IGNORECASE))

        # Đếm số lượng video
        videos = len(re.findall(r'<video[^>]+>|youtube\.com\/watch|youtu\.be\/|vimeo\.com\/|<iframe[^>]+youtube|<iframe[^>]+vimeo', content, re.IGNORECASE))

        # Đếm bảng và biểu đồ
        tables = len(re.findall(r'<table[^>]*>|(\|[^|]*\|[^|]*\|)|<tr>|<th>', content, re.IGNORECASE))

        # Đếm danh sách
        lists = len(re.findall(r'<[uo]l>|<li>|^[\*\-\+]\s|\d+\.\s', content, re.MULTILINE))

        # Tính điểm phong phú phương tiện
        media_elements = images + videos * 3 + tables * 2 + min(lists, 5)

        if media_elements >= 10:
            media_enrichment_score = 1.0
        elif media_elements >= 6:
            media_enrichment_score = 0.8
        elif media_elements >= 3:
            media_enrichment_score = 0.6
        elif media_elements >= 1:
            media_enrichment_score = 0.4
        else:
            media_enrichment_score = 0.0

        result["media_enrichment_score"] = media_enrichment_score

        # Thêm điểm mạnh/yếu dựa trên sự phong phú phương tiện
        if media_enrichment_score >= 0.6:
            result["strengths"].append("Nội dung phong phú với hình ảnh, biểu đồ hoặc phương tiện đa dạng")
        elif media_enrichment_score == 0:
            result["weaknesses"].append("Thiếu phương tiện minh họa như hình ảnh, biểu đồ hoặc bảng")

        # Bước 4: Đánh giá sự phức tạp cấu trúc (structure complexity)
        # Phân tích cấu trúc văn bản: đoạn văn, tiêu đề, danh sách, etc.

        # Đếm số đoạn văn
        paragraphs = len(content.split('\n\n'))

        # Đếm số tiêu đề
        headings = len(re.findall(r'^#{1,6}\s+[^\n]+$|<h[1-6][^>]*>[^<]+</h[1-6]>', content, re.MULTILINE))

        # Đếm số liên kết
        links = len(re.findall(r'\[([^\]]+)\]\(([^)]+)\)|<a[^>]+href=["\'](.*?)["\']', content))

        # Đếm các cấu trúc phức tạp (bảng, danh sách, code block)
        complex_structures = tables + lists + len(re.findall(r'```[^`]*```|`[^`]+`', content))

        # Tính điểm phức tạp cấu trúc
        structure_elements = paragraphs + headings + links + complex_structures

        if word_count > 0:
            # Chuẩn hóa theo độ dài
            normalized_structure = structure_elements / (word_count / 100)

            if normalized_structure >= 8:
                structure_complexity_score = 1.0
            elif normalized_structure >= 6:
                structure_complexity_score = 0.8
            elif normalized_structure >= 4:
                structure_complexity_score = 0.6
            elif normalized_structure >= 2:
                structure_complexity_score = 0.4
            else:
                structure_complexity_score = 0.2
        else:
            structure_complexity_score = 0.0

        result["structure_complexity_score"] = structure_complexity_score

        # Thêm điểm mạnh/yếu dựa trên độ phức tạp cấu trúc
        if structure_complexity_score >= 0.7:
            result["strengths"].append("Cấu trúc nội dung tốt với nhiều đoạn văn, tiêu đề và tổ chức rõ ràng")
        elif structure_complexity_score <= 0.3:
            result["weaknesses"].append("Cấu trúc đơn giản, thiếu tổ chức và phân cấp rõ ràng")

        # Bước 5: Tính điểm phong phú tổng thể
        # Trọng số: 40% mật độ thông tin, 30% phong phú từ vựng, 15% phong phú phương tiện, 15% phức tạp cấu trúc
        richness_score = (
            result["information_density_score"] * 0.4 +
            result["vocabulary_richness_score"] * 0.3 +
            result["media_enrichment_score"] * 0.15 +
            result["structure_complexity_score"] * 0.15
        )

        result["richness_score"] = max(0.0, min(1.0, richness_score))

        # Thêm giải thích
        if result["richness_score"] >= 0.8:
            result["explanation"] = "Nội dung rất phong phú với mật độ thông tin cao, từ vựng đa dạng và cấu trúc tốt"
        elif result["richness_score"] >= 0.6:
            result["explanation"] = "Nội dung khá phong phú, cân bằng tốt giữa thông tin và cách trình bày"
        elif result["richness_score"] >= 0.4:
            result["explanation"] = "Nội dung có độ phong phú trung bình, cần cải thiện một số khía cạnh"
        else:
            result["explanation"] = "Nội dung thiếu phong phú, cần bổ sung thêm thông tin và cải thiện cách trình bày"

        # Thêm thông tin về truy vấn nếu có
        if query:
            # Đánh giá độ liên quan với truy vấn (đơn giản)
            query_terms = set(query.lower().split())
            content_words = set(content.lower().split())
            query_coverage = len(query_terms.intersection(content_words)) / len(query_terms) if query_terms else 0

            # Bổ sung thông tin về độ phủ truy vấn
            if query_coverage >= 0.7:
                result["strengths"].append("Nội dung bao quát tốt các từ khóa trong truy vấn")
            elif query_coverage <= 0.3:
                result["weaknesses"].append("Nội dung chưa đề cập đầy đủ các từ khóa trong truy vấn")

        return result

    def _evaluate_search_results_quality(self, query: str, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Đánh giá chất lượng tổng thể của các kết quả tìm kiếm.

        Args:
            query: Truy vấn gốc
            results: Danh sách kết quả tìm kiếm

        Returns:
            Dict chứa kết quả đánh giá chất lượng
        """
        result = {
            "overall_quality_score": 0.0,
            "relevance_score": 0.0,
            "diversity_score": 0.0,
            "credibility_score": 0.0,
            "freshness_score": 0.0,
            "completeness_score": 0.0,
            "explanation": "",
            "high_quality_results": [],
            "low_quality_results": [],
            "improvements_needed": []
        }

        # Kiểm tra nếu không có kết quả
        if not results or len(results) == 0:
            result["explanation"] = "Không tìm thấy kết quả nào"
            result["improvements_needed"].append("Thử các từ khóa tìm kiếm khác")
            return result

        # Bước 1: Đánh giá độ liên quan (relevance)
        # Tính điểm liên quan trung bình của các kết quả
        relevance_scores = []

        for i, search_result in enumerate(results):
            # Lấy điểm liên quan từ kết quả (nếu có)
            if "relevance_score" in search_result:
                relevance_scores.append(search_result["relevance_score"])
            elif "relevance" in search_result:
                relevance_scores.append(search_result["relevance"])
            else:
                # Tính điểm liên quan dựa trên sự xuất hiện của từ khóa trong tiêu đề và snippet
                query_terms = set(query.lower().split())
                title = search_result.get("title", "").lower()
                snippet = search_result.get("snippet", "").lower()

        # Bước 2: Đánh giá độ đa dạng (diversity)
        # Sử dụng phương thức đã có
        diversity_result = self._evaluate_source_diversity(results)
        result["diversity_score"] = diversity_result["diversity_score"]

        # Bước 3: Đánh giá độ tin cậy (credibility)
        # Tính điểm tin cậy trung bình
        credibility_scores = []

        for search_result in results:
            # Lấy điểm tin cậy từ kết quả (nếu có)
            if "credibility_score" in search_result:
                credibility_scores.append(search_result["credibility_score"])
            elif "credibility" in search_result and isinstance(search_result["credibility"], dict):
                if "overall_score" in search_result["credibility"]:
                    credibility_scores.append(search_result["credibility"]["overall_score"])
                elif "source" in search_result["credibility"] and "score" in search_result["credibility"]["source"]:
                    credibility_scores.append(search_result["credibility"]["source"]["score"])
            else:
                # Ước tính độ tin cậy dựa trên domain
                url = search_result.get("url", "")
                domain_score = self._get_domain_credibility(self._extract_domain(url))

                if domain_score is not None:
                    credibility_scores.append(domain_score)
                else:
                    credibility_scores.append(0.5)  # Điểm mặc định

        # Tính điểm tin cậy trung bình
        if credibility_scores:
            credibility_score = sum(credibility_scores) / len(credibility_scores)
        else:
            credibility_score = 0.5  # Điểm mặc định

        result["credibility_score"] = credibility_score

        # Bước 4: Đánh giá độ mới (freshness)
        # Đếm số lượng kết quả mới và cũ
        current_time = time.time()
        one_year_ago = current_time - (365 * 24 * 60 * 60)

        recent_results = 0
        dated_results = 0

        for search_result in results:
            # Tìm thời gian trong kết quả
            result_date = None

            if "date" in search_result:
                result_date = search_result["date"]
            elif "published_date" in search_result:
                result_date = search_result["published_date"]
            elif "timestamp" in search_result:
                result_date = search_result["timestamp"]

            # Nếu có thời gian, kiểm tra xem có phải là gần đây
            if result_date:
                try:
                    # Chuyển đổi thành timestamp
                    if isinstance(result_date, str):
                        # Xử lý các định dạng phổ biến
                        formats = ["%Y-%m-%d", "%Y/%m/%d", "%d-%m-%Y", "%d/%m/%Y",
                                 "%Y-%m-%d %H:%M:%S", "%Y/%m/%d %H:%M:%S"]

                        for fmt in formats:
                            try:
                                dt = datetime.strptime(result_date, fmt)
                                result_timestamp = dt.timestamp()
                                break
                            except:
                                continue
                        else:
                            result_timestamp = None
                    elif isinstance(result_date, (int, float)):
                        result_timestamp = result_date
                    else:
                        result_timestamp = None

                    # Phân loại thời gian
                    if result_timestamp > one_year_ago:
                        recent_results += 1
                    else:
                        dated_results += 1
                except:
                    # Nếu có lỗi khi xử lý thời gian, bỏ qua
                    pass

        # Tính điểm độ mới
        total_dated = recent_results + dated_results
        if total_dated > 0:
            freshness_score = recent_results / total_dated
        else:
            # Nếu không có thông tin về thời gian, giả định là 0.5
            freshness_score = 0.5

        result["freshness_score"] = freshness_score

        # Bước 5: Đánh giá tính đầy đủ (completeness)
        # Kiểm tra xem có bao phủ đủ các khía cạnh của truy vấn không

        # Trích xuất các khía cạnh từ truy vấn
        query_facets = self._extract_facets(query)

        # Nếu không có khía cạnh, trích xuất từ khóa
        if not query_facets:
            query_facets = self._extract_keywords(query)

        # Đếm xem có bao nhiêu khía cạnh được đề cập trong kết quả
        facet_coverage = {}

        for facet in query_facets:
            facet_coverage[facet] = 0

        for search_result in results:
            title = search_result.get("title", "").lower()
            snippet = search_result.get("snippet", "").lower()
            content = title + " " + snippet

            for facet in query_facets:
                if facet.lower() in content:
                    facet_coverage[facet] += 1

        # Tính tỷ lệ các khía cạnh được hỗ trợ
        if query_facets:
            # Một khía cạnh cần xuất hiện trong ít nhất 2 kết quả để được coi là đủ
            well_covered_facets = sum(1 for count in facet_coverage.values() if count >= 2)
            completeness_score = well_covered_facets / len(query_facets)

            # Xác định các khía cạnh còn thiếu
            missing_facets = [facet for facet, count in facet_coverage.items() if count < 2]

            if missing_facets:
                if len(missing_facets) == 1:
                    result["improvements_needed"].append(f"Tìm thêm thông tin về khía cạnh: {missing_facets[0]}")
                else:
                    facets_str = ", ".join(missing_facets[:3])
                    result["improvements_needed"].append(f"Tìm thêm thông tin về các khía cạnh: {facets_str}")
        else:
            # Nếu không xác định được khía cạnh, đánh giá dựa trên số lượng kết quả
            completeness_score = min(1.0, len(results) / 5)  # 5+ kết quả được coi là đầy đủ

        result["completeness_score"] = completeness_score

        # Bước 6: Xác định kết quả chất lượng cao và thấp
        high_quality_threshold = 0.7
        low_quality_threshold = 0.3

        for i, search_result in enumerate(results):
            # Tính điểm chất lượng cho từng kết quả
            rel_score = relevance_scores[i] if i < len(relevance_scores) else 0.5
            cred_score = credibility_scores[i] if i < len(credibility_scores) else 0.5

            # Điểm chất lượng (trọng số: 60% độ liên quan, 40% độ tin cậy)
            quality_score = rel_score * 0.6 + cred_score * 0.4

            # Phân loại kết quả
            if quality_score >= high_quality_threshold:
                result["high_quality_results"].append({
                    "index": i,
                    "title": search_result.get("title", ""),
                    "quality_score": quality_score
                })
            elif quality_score <= low_quality_threshold:
                result["low_quality_results"].append({
                    "index": i,
                    "title": search_result.get("title", ""),
                    "quality_score": quality_score
                })

        # Bước 7: Tính điểm chất lượng tổng thể
        # Trọng số: 40% độ liên quan, 20% độ đa dạng, 20% độ tin cậy, 10% độ mới, 10% tính đầy đủ
        overall_quality_score = (
            result["relevance_score"] * 0.4 +
            result["diversity_score"] * 0.2 +
            result["credibility_score"] * 0.2 +
            result["freshness_score"] * 0.1 +
            result["completeness_score"] * 0.1
        )

        result["overall_quality_score"] = max(0.0, min(1.0, overall_quality_score))

        # Thêm giải thích và đề xuất cải thiện
        if result["overall_quality_score"] >= 0.8:
            result["explanation"] = "Kết quả tìm kiếm có chất lượng cao, liên quan và đáng tin cậy"
        elif result["overall_quality_score"] >= 0.6:
            result["explanation"] = "Kết quả tìm kiếm có chất lượng khá, bao gồm nhiều thông tin liên quan"
        elif result["overall_quality_score"] >= 0.4:
            result["explanation"] = "Kết quả tìm kiếm có chất lượng trung bình, cần cải thiện một số khía cạnh"

            # Thêm gợi ý cải thiện cụ thể
            if result["relevance_score"] < 0.5:
                result["improvements_needed"].append("Sử dụng các từ khóa chính xác hơn trong truy vấn")
            if result["diversity_score"] < 0.5:
                result["improvements_needed"].append("Tìm kiếm từ nhiều nguồn khác nhau")
            if result["credibility_score"] < 0.5:
                result["improvements_needed"].append("Tìm thêm các nguồn đáng tin cậy")
            if result["freshness_score"] < 0.5:
                result["improvements_needed"].append("Thêm 'mới nhất' hoặc năm hiện tại vào truy vấn")
        else:
            result["explanation"] = "Kết quả tìm kiếm có chất lượng thấp, cần cải thiện đáng kể"
            result["improvements_needed"].append("Thử từ khóa hoặc cách diễn đạt khác")

        return result

    def _improve_search_results(self, query: str, results: List[Dict[str, Any]], evaluation_result: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Cải thiện chất lượng các kết quả tìm kiếm dựa trên đánh giá.

        Args:
            query: Truy vấn gốc
            results: Danh sách kết quả tìm kiếm gốc
            evaluation_result: Kết quả đánh giá (tùy chọn)

        Returns:
            Dict chứa danh sách kết quả đã cải thiện và thông tin bổ sung
        """
        # Nếu không có đánh giá, thực hiện đánh giá trước
        if not evaluation_result:
            evaluation_result = self._evaluate_search_results_quality(query, results)

        # Tạo kết quả trả về
        improved_results = {
            "results": [],
            "filtered_results": [],
            "added_results": [],
            "query_modifications": [],
            "improvement_actions": [],
            "explanation": ""
        }

        # Kiểm tra nếu không có kết quả hoặc kết quả đã tốt
        if not results or len(results) == 0:
            improved_results["explanation"] = "Không tìm thấy kết quả nào để cải thiện"
            improved_results["query_modifications"] = self._generate_alternative_queries(query)
            return improved_results

        if evaluation_result["overall_quality_score"] >= 0.8:
            improved_results["explanation"] = "Kết quả tìm kiếm đã có chất lượng cao, không cần cải thiện thêm"
            improved_results["results"] = results
            return improved_results

        # Bước 1: Loại bỏ kết quả chất lượng thấp
        low_quality_indices = set(item["index"] for item in evaluation_result.get("low_quality_results", []))

        # Tạo danh sách mới không bao gồm kết quả chất lượng thấp
        filtered_results = []
        for i, result in enumerate(results):
            if i in low_quality_indices:
                improved_results["filtered_results"].append(result)
            else:
                filtered_results.append(result)

        # Thêm hành động cải thiện nếu có lọc kết quả
        if improved_results["filtered_results"]:
            improved_results["improvement_actions"].append(f"Đã loại bỏ {len(improved_results['filtered_results'])} kết quả có chất lượng thấp")

        # Bước 2: Sắp xếp lại kết quả theo chất lượng
        # Tạo danh sách các điểm số chất lượng cho từng kết quả
        result_scores = []

        for i, result in enumerate(filtered_results):
            # Tính điểm chất lượng tổng hợp

            # Độ liên quan
            relevance = 0.0
            if "relevance_score" in result:
                relevance = result["relevance_score"]
            elif "relevance" in result:
                relevance = result["relevance"]
            else:
                # Đánh giá độ liên quan dựa trên sự xuất hiện của từ khóa trong tiêu đề và snippet
                query_terms = set(query.lower().split())
                title = result.get("title", "").lower()
                snippet = result.get("snippet", "").lower()

                if query_terms:
                    title_match = sum(1 for term in query_terms if term in title) / len(query_terms)
                    snippet_match = sum(1 for term in query_terms if term in snippet) / len(query_terms)
                    relevance = title_match * 0.7 + snippet_match * 0.3
                else:
                    relevance = 0.5

            # Độ tin cậy
            credibility = 0.0
            if "credibility_score" in result:
                credibility = result["credibility_score"]
            else:
                # Ước tính độ tin cậy dựa trên domain
                url = result.get("url", "")
                domain_score = self._get_domain_credibility(self._extract_domain(url))
                credibility = domain_score if domain_score is not None else 0.5

            # Tính điểm tổng hợp (trọng số: 70% độ liên quan, 30% độ tin cậy)
            quality_score = relevance * 0.7 + credibility * 0.3

            # Thêm vào danh sách điểm số
            result_scores.append((i, quality_score))

        # Sắp xếp kết quả theo điểm chất lượng giảm dần
        result_scores.sort(key=lambda x: x[1], reverse=True)

        # Tạo danh sách kết quả đã sắp xếp
        sorted_results = [filtered_results[i] for i, _ in result_scores]

        # Thêm hành động cải thiện nếu có sắp xếp lại
        if len(sorted_results) > 1 and sorted_results != filtered_results:
            improved_results["improvement_actions"].append("Đã sắp xếp lại kết quả theo chất lượng")

        # Bước 3: Bổ sung thêm thông tin đánh giá cho từng kết quả
        for i, result in enumerate(sorted_results):
            # Thêm thông tin đánh giá nếu chưa có
            if "credibility_score" not in result:
                url = result.get("url", "")
                domain = self._extract_domain(url)
                domain_score = self._get_domain_credibility(domain)

                if domain_score is not None:
                    result["credibility_score"] = domain_score
                    result["credibility_source"] = "domain_reputation"
                else:
                    result["credibility_score"] = 0.5
                    result["credibility_source"] = "default"

            # Thêm thông tin về độ liên quan nếu chưa có
            if "relevance_score" not in result:
                query_terms = set(query.lower().split())
                title = result.get("title", "").lower()
                snippet = result.get("snippet", "").lower()

                if query_terms:
                    title_match = sum(1 for term in query_terms if term in title) / len(query_terms)
                    snippet_match = sum(1 for term in query_terms if term in snippet) / len(query_terms)
                    result["relevance_score"] = title_match * 0.7 + snippet_match * 0.3
                    result["relevance_source"] = "keyword_matching"
                else:
                    result["relevance_score"] = 0.5
                    result["relevance_source"] = "default"

        # Bước 4: Tạo các truy vấn thay thế cho các khía cạnh còn thiếu
        missing_facets = []
        facet_coverage = evaluation_result.get("facet_coverage", {})

        # Nếu không có thông tin facet_coverage, trích xuất từ gợi ý cải thiện
        if not facet_coverage:
            for improvement in evaluation_result.get("improvements_needed", []):
                if "khía cạnh" in improvement:
                    # Trích xuất các khía cạnh từ gợi ý cải thiện
                    match = re.search(r"khía cạnh:?\s*(.*)", improvement)
                    if match:
                        facets = match.group(1).split(", ")
                        missing_facets.extend(facets)

        # Nếu vẫn không có, trích xuất từ truy vấn
        if not missing_facets:
            query_facets = self._extract_facets(query)

            # Đếm xem mỗi khía cạnh xuất hiện bao nhiêu lần trong kết quả
            facet_counts = {facet: 0 for facet in query_facets}

            for result in sorted_results:
                title = result.get("title", "").lower()
                snippet = result.get("snippet", "").lower()
                content = title + " " + snippet

                for facet in query_facets:
                    if facet.lower() in content:
                        facet_counts[facet] += 1

            # Xác định các khía cạnh còn thiếu
            missing_facets = [facet for facet, count in facet_counts.items() if count < 2]

        # Tạo các truy vấn thay thế cho các khía cạnh còn thiếu
        alternative_queries = []

        for facet in missing_facets:
            # Tạo truy vấn tập trung vào khía cạnh còn thiếu
            alt_query = f"{query} {facet}"
            alternative_queries.append({
                "query": alt_query,
                "focus": facet,
                "reason": f"Tìm thêm thông tin về khía cạnh: {facet}"
            })

        # Thêm các truy vấn chính xác hơn nếu cần
        if evaluation_result["relevance_score"] < 0.5:
            enhanced_queries = self._generate_alternative_queries(query)
            for eq in enhanced_queries:
                if eq not in [aq["query"] for aq in alternative_queries]:
                    alternative_queries.append({
                        "query": eq,
                        "focus": "relevance",
                        "reason": "Cải thiện độ liên quan bằng cách sử dụng từ khóa chính xác hơn"
                    })

        # Nếu độ mới thấp, thêm truy vấn với giới hạn thời gian
        if evaluation_result["freshness_score"] < 0.5:
            current_year = datetime.now().year
            time_limited_query = f"{query} {current_year}"
            alternative_queries.append({
                "query": time_limited_query,
                "focus": "freshness",
                "reason": "Tìm kết quả mới hơn bằng cách thêm năm hiện tại"
            })

        # Nếu độ tin cậy thấp, thêm truy vấn với nguồn đáng tin cậy
        if evaluation_result["credibility_score"] < 0.5:
            credible_query = f"{query} site:.edu OR site:.gov"
            alternative_queries.append({
                "query": credible_query,
                "focus": "credibility",
                "reason": "Tìm từ các nguồn học thuật và chính phủ đáng tin cậy"
            })

        # Cập nhật danh sách truy vấn thay thế
        improved_results["query_modifications"] = alternative_queries

        # Bước 5: Hoàn thiện kết quả
        improved_results["results"] = sorted_results

        # Thêm giải thích
        if low_quality_indices:
            filtered_explanation = f"Đã loại bỏ {len(low_quality_indices)} kết quả chất lượng thấp và sắp xếp lại {len(sorted_results)} kết quả còn lại."
        else:
            filtered_explanation = f"Đã sắp xếp lại {len(sorted_results)} kết quả theo chất lượng."

        suggestion_explanation = ""
        if alternative_queries:
            suggestion_explanation = f" Đề xuất {len(alternative_queries)} truy vấn thay thế để tìm thêm thông tin."

        improved_results["explanation"] = filtered_explanation + suggestion_explanation

        return improved_results

    def _generate_alternative_queries(self, query: str) -> List[str]:
        """
        Tạo các truy vấn thay thế để cải thiện kết quả tìm kiếm.

        Args:
            query: Truy vấn gốc

        Returns:
            Danh sách các truy vấn thay thế
        """
        alternative_queries = []

        # Loại bỏ các stopword
        stopwords = ["và", "hoặc", "nhưng", "vì", "bởi", "nếu", "khi", "là", "có", "được",
                    "tại", "trong", "ngoài", "trên", "dưới", "the", "and", "or", "but", "if",
                    "when", "is", "are", "at", "in", "on", "of", "to", "from"]

        # Trích xuất từ khóa
        query_words = query.split()
        keywords = [word for word in query_words if word.lower() not in stopwords]

        # Nếu có quá ít từ khóa (chỉ 1-2 từ), không thay đổi nhiều
        if len(keywords) <= 2:
            # Thêm các từ khóa mở rộng
            alternative_queries.append(f"{query} là gì")
            alternative_queries.append(f"{query} cách")
            alternative_queries.append(f"{query} ví dụ")
            alternative_queries.append(f"hướng dẫn {query}")

            # Thêm phiên bản tiếng Anh nếu truy vấn là tiếng Việt
            if self._is_vietnamese_text(query):
                no_tone_query = self._remove_vietnamese_tones(query)
                alternative_queries.append(no_tone_query)

            return alternative_queries

        # Tạo các biến thể của truy vấn
        # 1. Đổi thứ tự các từ khóa
        if len(keywords) >= 3:
            shuffled_keywords = keywords.copy()
            for i in range(min(3, len(keywords))):
                random.shuffle(shuffled_keywords)
                alternative_query = " ".join(shuffled_keywords)
                if alternative_query != query and alternative_query not in alternative_queries:
                    alternative_queries.append(alternative_query)

        # 2. Loại bỏ một số từ khóa ít quan trọng
        if len(keywords) >= 4:
            # Sắp xếp từ khóa theo độ dài (ưu tiên từ dài hơn - thường quan trọng hơn)
            sorted_keywords = sorted(keywords, key=len, reverse=True)
            important_keywords = sorted_keywords[:int(len(sorted_keywords) * 0.7)]
            alternative_query = " ".join(important_keywords)
            if alternative_query != query and alternative_query not in alternative_queries:
                alternative_queries.append(alternative_query)

        # 3. Thêm từ khóa chỉ định rõ hơn
        clarifying_terms = ["cụ thể", "chi tiết", "specific", "detailed"]
        for term in clarifying_terms:
            alternative_query = f"{query} {term}"
            if alternative_query not in alternative_queries:
                alternative_queries.append(alternative_query)

        # 4. Thêm từ khóa giới hạn cho tìm kiếm học thuật
        if any(word in query.lower() for word in ["nghiên cứu", "khoa học", "học thuật", "research", "science", "academic"]):
            academic_terms = ["paper", "journal", "scholarly", "nghiên cứu", "tạp chí", "khoa học"]
            for term in academic_terms:
                if term not in query.lower():
                    alternative_query = f"{query} {term}"
                    if alternative_query not in alternative_queries:
                        alternative_queries.append(alternative_query)

        # 5. Chuyển đổi dạng câu hỏi/câu lệnh
        # Kiểm tra xem truy vấn có phải là câu hỏi không
        if any(query.lower().startswith(qw) for qw in ["what", "how", "why", "when", "where", "who", "which",
                                                     "cái gì", "như thế nào", "tại sao", "khi nào", "ở đâu", "ai", "nào"]):
            # Chuyển từ câu hỏi sang từ khóa
            if query.endswith("?"):
                query_no_question = query[:-1]
            else:
                query_no_question = query

            # Loại bỏ từ để hỏi
            for qw in ["what", "how", "why", "when", "where", "who", "which",
                      "cái gì", "như thế nào", "tại sao", "khi nào", "ở đâu", "ai", "nào"]:
                if query_no_question.lower().startswith(qw):
                    query_no_question = query_no_question[len(qw):].strip()
                    break

            # Loại bỏ các từ như "is", "are", "do", "does", "là", "có phải"
            for aux in ["is", "are", "do", "does", "was", "were", "will", "should", "can", "could",
                       "là", "có phải", "có", "sẽ", "nên", "có thể"]:
                if query_no_question.lower().startswith(aux + " "):
                    query_no_question = query_no_question[len(aux):].strip()
                    break

            # Thêm vào danh sách truy vấn thay thế
            if query_no_question != query and query_no_question not in alternative_queries:
                alternative_queries.append(query_no_question)

        else:
            # Chuyển từ từ khóa sang câu hỏi
            question_forms = []

            # Trích xuất chủ đề chính
            main_topic = self._extract_main_topic(query)

            if main_topic:
                question_forms.append(f"What is {main_topic}?")
                question_forms.append(f"{main_topic} là gì?")
                question_forms.append(f"How does {main_topic} work?")
                question_forms.append(f"{main_topic} hoạt động như thế nào?")

            # Thêm vào danh sách truy vấn thay thế
            for qf in question_forms:
                if qf not in alternative_queries:
                    alternative_queries.append(qf)

        # Loại bỏ các truy vấn trùng lặp hoặc quá giống với truy vấn gốc
        filtered_queries = []
        for aq in alternative_queries:
            # Kiểm tra xem truy vấn thay thế có quá giống với truy vấn gốc không
            if aq.lower() != query.lower() and self._query_similarity(query, aq) < 0.9:
                filtered_queries.append(aq)

        # Giới hạn số lượng truy vấn thay thế
        return filtered_queries[:5]

    def _query_similarity(self, query1: str, query2: str) -> float:
        """
        Tính độ tương đồng giữa hai truy vấn.

        Args:
            query1: Truy vấn thứ nhất
            query2: Truy vấn thứ hai

        Returns:
            Điểm tương đồng từ 0.0 đến 1.0
        """
        # Chuyển thành lowercase
        q1 = query1.lower()
        q2 = query2.lower()

        # Tách từ
        words1 = set(q1.split())
        words2 = set(q2.split())

        # Tính độ tương đồng Jaccard
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))

        if union == 0:
            return 0.0

        return intersection / union

    def _extract_pdf_info(self, file_path: str) -> Dict[str, Any]:
        """
        Trích xuất thông tin từ file PDF.

        Args:
            file_path: Đường dẫn đến file PDF

        Returns:
            Dict chứa nội dung và metadata của PDF
        """
        # Kiểm tra xem FileProcessor có khả dụng không
        if not FileProcessor:
            # Fallback nếu không có FileProcessor
            return {
                "content": f"Không thể trích xuất nội dung PDF từ {file_path}: FileProcessor không khả dụng",
                "metadata": {
                    "error": "FileProcessor not available",
                    "file_path": file_path
                }
            }

        # Sử dụng FileProcessor hiện có
        try:
            # Kiểm tra xem agent có _file_processor không
            if hasattr(self, '_file_processor'):
                result = self._file_processor.extract_content(file_path, 'pdf')
            else:
                # Tạo FileProcessor mới nếu cần
                file_processor = FileProcessor()
                result = file_processor.extract_content(file_path, 'pdf')

            # Phân tích thêm nội dung PDF nếu cần
            if result.get('success', False):
                # Thêm thông tin bổ sung về PDF
                pdf_text = result.get('content', '')

                # Tính số từ và số câu
                words = pdf_text.split()
                sentences = re.split(r'[.!?]', pdf_text)
                sentences = [s.strip() for s in sentences if s.strip()]

                if 'metadata' not in result:
                    result['metadata'] = {}

                result['metadata']['word_count'] = len(words)
                result['metadata']['sentence_count'] = len(sentences)

                # Tìm các từ khóa phổ biến
                if words:
                    # Loại bỏ stopwords (từ không quan trọng)
                    stopwords = {"the", "a", "an", "and", "or", "but", "if", "of", "in", "on",
                                "at", "to", "for", "with", "by", "about", "như", "là", "và",
                                "hoặc", "nhưng", "nếu", "của", "trong", "trên", "tại", "cho"}

                    # Đếm từ
                    word_counter = Counter(word.lower() for word in words if word.lower() not in stopwords and len(word) > 2)

                    # Lấy 10 từ phổ biến nhất
                    result['metadata']['common_keywords'] = [word for word, _ in word_counter.most_common(10)]

                # Kiểm tra PDF có được bảo vệ không
                if 'encrypted' not in result['metadata']:
                    result['metadata']['encrypted'] = False  # Mặc định

                    # Kiểm tra các dấu hiệu bảo vệ
                    if "This document is password protected" in pdf_text:
                        result['metadata']['encrypted'] = True

            return result
        except Exception as e:
            # Xử lý lỗi
            return {
                "success": False,
                "content": f"Lỗi khi trích xuất nội dung PDF: {str(e)}",
                "metadata": {
                    "error": str(e),
                    "file_path": file_path
                }
            }

    def _crawl_with_async(self, urls: List[str], timeout: int = 30, max_workers: int = 10) -> List[Dict[str, Any]]:
        """
        Crawl nhiều URL cùng lúc sử dụng asyncio.

        Args:
            urls: Danh sách URL cần crawl
            timeout: Thời gian chờ tối đa (giây)
            max_workers: Số lượng worker tối đa

        Returns:
            Danh sách kết quả crawl
        """
        results = []

        if not urls:
            return results

        # Giới hạn số lượng workers
        workers = min(max_workers, len(urls))

        # Tạo các tác vụ crawl
        with ThreadPoolExecutor(max_workers=workers) as executor:
            # Gửi các yêu cầu song song
            future_to_url = {executor.submit(self._fetch_url, url, timeout): url for url in urls}

            # Xử lý kết quả
            for future in as_completed(future_to_url):
                url = future_to_url[future]
                try:
                    data = future.result()
                    if data:
                        results.append(data)
                except Exception as e:
                    logger.error("Lỗi khi crawl URL %s: %s", url, str(e))
                    results.append({
                        "url": url,
                        "success": False,
                        "error": str(e),
                        "status_code": None,
                        "content": None,
                        "headers": {},
                        "timestamp": datetime.now().isoformat()
                    })

        return results

    def _fetch_url(self, url: str, timeout: int = None) -> Dict[str, Any]:
        """
        Fetch URL sử dụng requests.

        Args:
            url: URL cần fetch
            timeout: Thời gian timeout

        Returns:
            Dict chứa kết quả fetch
        """
        result = {
                        "url": url,
            "content": {},
            "html": None,
                        "success": False,
            "error": None,
                        "timestamp": datetime.now().isoformat()
                    }

        # Sử dụng PlaywrightHandler nếu có
        if hasattr(self, '_playwright_handler') and self._playwright_handler:
            try:
                page_data = self._playwright_handler.extract_content(url, timeout=timeout or self.timeout)
                result.update(page_data)
                return result
            except Exception as e:
                result["error"] = f"Lỗi khi sử dụng PlaywrightHandler: {str(e)}"
                # Fallback to direct Playwright usage

        # Fallback nếu không có PlaywrightHandler
        if not PLAYWRIGHT_AVAILABLE:
            result["error"] = "Playwright không khả dụng"
            return result

        try:
            with sync_playwright() as p:
                browser = p.chromium.launch(headless=True)
                page = browser.new_page(user_agent=user_agent or self._get_user_agent())

                try:
                    page.set_default_timeout(timeout or self.timeout * 1000)  # Convert to ms
                    page.goto(url)

                    # Xử lý infinite scroll nếu cần
                    for _ in range(3):  # Scroll 3 lần
                        page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                        page.wait_for_timeout(1000)  # Chờ 1 giây

                    # Chờ AJAX requests hoàn thành
                    page.wait_for_timeout(2000)  # Chờ 2 giây

                    # Lấy nội dung
                    result["html"] = page.content()
                    result["content"] = {
                        "title": page.title(),
                        "text": page.evaluate("document.body.innerText"),
                        "url": page.url
                    }

                    result["success"] = True

                    # Trích xuất links
                    try:
                        links = page.evaluate("""() => {
                            return Array.from(document.querySelectorAll('a[href]')).map(a => {
                                return {
                                    url: a.href,
                                    text: a.innerText.trim(),
                                    rel: a.rel
                                }
                            });
                        }""")
                        result["links"] = links
                    except Exception as e:
                        self.logger.warning("Không thể trích xuất links: %s", str(e))

                    try:
                        # Thêm xử lý khác nếu cần
                        pass
                    except Exception as e:
                        result["error"] = f"Lỗi Playwright: {str(e)}"
                    finally:
                        browser.close()
                except Exception as e:
                    result["error"] = f"Lỗi khởi tạo Playwright: {str(e)}"

            return result
        except Exception as e:
            result["error"] = f"Lỗi khởi tạo Playwright: {str(e)}"

        return result

    def _apply_rate_limiting(self, domain: str = None):
        """
        Áp dụng rate limiting cho các request.

        Args:
            domain: Tên miền đang được truy cập
        """
        if not self.enable_rate_limiting:
            return

        # Delay cơ bản
        base_delay = self.rate_limit_delay

        # Kiểm tra nếu có domain-specific delay
        if domain and hasattr(self, '_domain_delays') and domain in self._domain_delays:
            base_delay = self._domain_delays.get(domain, base_delay)

        # Apply delay
        time.sleep(base_delay)

    def _backoff(self, attempt: int, base_delay: float = 1.0, max_delay: float = 60.0) -> float:
        """
        Tính toán thời gian backoff cho retry.

        Args:
            attempt: Số lần thử
            base_delay: Thời gian delay cơ bản
            max_delay: Thời gian delay tối đa

        Returns:
            Thời gian delay (giây)
        """
        # Exponential backoff: base_delay * 2^(attempt - 1)
        delay = base_delay * (2 ** (attempt - 1))

        # Thêm jitter ngẫu nhiên (±20%)
        jitter = random.uniform(-0.2, 0.2)
        delay = delay * (1 + jitter)

        # Giới hạn ở max_delay
        return min(delay, max_delay)

    def _retry_with_backoff(self, func: Callable, args: tuple = (), kwargs: dict = None,
                          max_retries: int = None, base_delay: float = 1.0,
                          max_delay: float = 60.0, retry_on: tuple = (Exception,)) -> Any:
        """
        Thực thi một hàm với retry và backoff.

        Args:
            func: Hàm cần thực thi
            args: Tham số vị trí
            kwargs: Tham số từ khóa
            max_retries: Số lần retry tối đa
            base_delay: Thời gian delay cơ bản
            max_delay: Thời gian delay tối đa
            retry_on: Các exception cần retry

        Returns:
            Kết quả của hàm
        """
        if kwargs is None:
            kwargs = {}

        if max_retries is None:
            max_retries = self.max_retries

        attempt = 0
        last_exception = None

        while attempt <= max_retries:
            attempt += 1
            try:
                return func(*args, **kwargs)
            except retry_on as e:
                last_exception = e

                if attempt > max_retries:
                    break

                delay = self._backoff(attempt, base_delay, max_delay)
                if self.verbose:
                    self.logger.info("Retry attempt %s/%s after %.2fs delay (Error: %s)", attempt, max_retries, delay, str(e))

                time.sleep(delay)

        # Nếu đến đây, tất cả retries đã thất bại
        if self.verbose:
            self.logger.error("All %s retry attempts failed: %s", max_retries, str(last_exception))

        raise last_exception

    def _get_proxy(self) -> Optional[Dict[str, str]]:
        """
        Lấy proxy để sử dụng (với xoay vòng nếu được bật).

        Returns:
            Dict chứa thông tin proxy hoặc None nếu không có proxy
        """
        if not self.use_proxy_rotation or not self.proxies or len(self.proxies) == 0:
            return None

        # Chọn proxy ngẫu nhiên
        proxy = random.choice(self.proxies)

        # Định dạng proxy
        if isinstance(proxy, str):
            # Định dạng proxy đơn giản "ip:port"
            if proxy.count(':') == 1:
                host, port = proxy.split(':')
                return {
                    "http": f"http://{host}:{port}",
                    "https": f"http://{host}:{port}"
                }
            # Định dạng proxy đầy đủ "protocol://ip:port"
            elif '://' in proxy:
                protocol = proxy.split('://')[0]
                return {
                    "http": proxy if protocol == 'http' else f"http://{proxy.split('://')[1]}",
                    "https": proxy if protocol == 'https' else f"https://{proxy.split('://')[1]}"
                }
        elif isinstance(proxy, dict):
            # Định dạng proxy đã là dict
            return proxy

        # Fallback
        return None

    def _setup_proxy_session(self) -> requests.Session:
        """
        Thiết lập session với proxy.

        Returns:
            Session với proxy đã được thiết lập
        """
        session = requests.Session()

        if self.use_proxy_rotation and self.proxies:
            proxy = self._get_proxy()
            if proxy:
                session.proxies.update(proxy)
                if self.verbose:
                    self.logger.info("Sử dụng proxy: %s", proxy)

        # Thiết lập User-Agent
        session.headers.update({
            "User-Agent": self._get_user_agent()
        })

        return session

    def _rotate_proxy(self, session: requests.Session) -> requests.Session:
        """
        Xoay vòng proxy cho session.

        Args:
            session: Session cần xoay vòng proxy

        Returns:
            Session với proxy mới
        """
        if not self.use_proxy_rotation or not self.proxies:
            return session

        proxy = self._get_proxy()
        if proxy:
            session.proxies.clear()
            session.proxies.update(proxy)
            if self.verbose:
                self.logger.info("Đã xoay vòng proxy: %s", proxy)

        return session

    def adaptive_scraping(self, url: str, content_type: str = None, depth: int = 1) -> Dict[str, Any]:
        """
        Thực hiện scraping thích ứng dựa trên loại nội dung.

        Args:
            url: URL cần scrape
            content_type: Loại nội dung (nếu biết trước)
            depth: Độ sâu scraping (mặc định: 1)

        Returns:
            Dict chứa kết quả scraping
        """
        if hasattr(self, '_adaptive_crawler_integration') and self._adaptive_crawler_integration:
            try:
                return self._adaptive_crawler_integration.adaptive_scrape(url, content_type, depth)
            except Exception as e:
                self.logger.error("Lỗi khi sử dụng AdaptiveCrawlerIntegration: %s", str(e))
                # Fallback to internal implementation

        # Sử dụng các phương thức có sẵn để thực hiện adaptive scraping
        try:
            # Xác định loại nội dung nếu chưa biết
            if not content_type:
                try:
                    head_response = requests.head(url, timeout=self.timeout)
                    content_type = head_response.headers.get('Content-Type', '').lower()
                except Exception as e:
                    self.logger.warning("Không thể xác định loại nội dung: %s", str(e))
                    content_type = "text/html"  # Giả định là HTML

            # Xử lý theo loại nội dung
            if "text/html" in content_type:
                # Sử dụng _crawl_with_playwright hoặc _fetch_url
                if PLAYWRIGHT_AVAILABLE:
                    return self._crawl_with_playwright(url)
                else:
                    return self._fetch_url(url)
            elif "application/pdf" in content_type:
                # Xử lý PDF
                return {"url": url, "content_type": content_type, "error": "PDF processing not implemented"}
            else:
                # Xử lý các loại nội dung khác
                return self._fetch_url(url)

        except Exception as e:
            self.logger.error("Lỗi trong adaptive_scraping: %s", str(e))
            return {
                "url": url,
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def extract_content_for_results(self, results: List[Dict[str, Any]], max_results: int = 5) -> List[Dict[str, Any]]:
        """
        Trích xuất nội dung cho danh sách kết quả tìm kiếm.

        Args:
            results: Danh sách kết quả tìm kiếm
            max_results: Số lượng kết quả tối đa cần trích xuất nội dung

        Returns:
            Danh sách kết quả đã được trích xuất nội dung
        """
        if not results:
            return []

        # Giới hạn số lượng kết quả để trích xuất
        target_results = results[:min(max_results, len(results))]
        enriched_results = []

        for result in target_results:
            if not result or "url" not in result:
                enriched_results.append(result)
                continue

            # Clone kết quả để không ảnh hưởng đến dữ liệu gốc
            enriched_result = result.copy()
            url = result["url"]

            # Kiểm tra nếu đã có nội dung
            if "content" in result and isinstance(result["content"], dict) and "text" in result["content"]:
                enriched_results.append(result)
                continue

            # Sử dụng adaptive_scraping để trích xuất nội dung
            try:
                scraped_data = self.adaptive_scraping(url)
                if scraped_data and scraped_data.get("success", False):
                    enriched_result["content"] = scraped_data.get("content", {})
                    enriched_result["links"] = scraped_data.get("links", [])
                    enriched_result["extracted"] = True
                else:
                    enriched_result["extraction_error"] = scraped_data.get("error", "Unknown error")
            except Exception as e:
                enriched_result["extraction_error"] = f"Error: {str(e)}"

            enriched_results.append(enriched_result)

        return enriched_results