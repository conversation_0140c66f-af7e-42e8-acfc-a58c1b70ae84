"""
Module cung cấp các công cụ xử lý và trích xuất nội dung từ nhiều định dạng file khác nhau.

FileProcessor cung cấp một giao diện thống nhất để xử lý các file PDF, DOCX, XLSX, TXT,
HTML và các định dạng phổ biến khác, hỗ trợ trích xuất văn bản, metadata và phân tích cấu trúc.
"""

import os
import re
import io
import time
import tempfile
import logging
import mimetypes
import hashlib
from typing import Any, Dict, List, Optional, Tuple, Union, BinaryIO, Set
from pathlib import Path

# Kiểm tra các thư viện phụ thuộc
try:
    import PyPDF2
    PYPDF_AVAILABLE = True
except ImportError:
    PYPDF_AVAILABLE = False

try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False

try:
    from docx import Document
    PYTHON_DOCX_AVAILABLE = True
except ImportError:
    PYTHON_DOCX_AVAILABLE = False

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

try:
    import openpyxl
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False

try:
    from bs4 import BeautifulSoup
    BS4_AVAILABLE = True
except ImportError:
    BS4_AVAILABLE = False

try:
    import pytesseract
    import PIL.Image
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False

try:
    import tabula
    TABULA_AVAILABLE = True
except ImportError:
    TABULA_AVAILABLE = False

try:
    import magic
    MAGIC_AVAILABLE = True
except ImportError:
    MAGIC_AVAILABLE = False

# Các ngoại lệ tùy chỉnh
class FileProcessorError(Exception):
    """Lỗi cơ bản của FileProcessor."""
    pass

class UnsupportedFileTypeError(FileProcessorError):
    """Lỗi khi loại file không được hỗ trợ."""
    pass

class FileTooLargeError(FileProcessorError):
    """Lỗi khi file quá lớn."""
    pass

class CorruptedFileError(FileProcessorError):
    """Lỗi khi file bị hỏng."""
    pass

class TimeoutError(FileProcessorError):
    """Lỗi khi xử lý file mất quá nhiều thời gian."""
    pass

# Thiết lập logger
logger = logging.getLogger(__name__)

# Map các MIME type phổ biến
MIME_TYPE_MAP = {
    'application/pdf': 'pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
    'application/msword': 'doc',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
    'application/vnd.ms-excel': 'xls',
    'text/plain': 'txt',
    'text/html': 'html',
    'text/csv': 'csv',
    'image/jpeg': 'jpg',
    'image/png': 'png',
    'image/gif': 'gif',
    'image/webp': 'webp',
    'application/zip': 'zip',
    'application/x-rar-compressed': 'rar',
    'application/gzip': 'gz',
    'application/x-tar': 'tar',
    'audio/mpeg': 'mp3',
    'video/mp4': 'mp4'
}

# Map các extension phổ biến
EXTENSION_MAP = {
    '.pdf': 'pdf',
    '.docx': 'docx',
    '.doc': 'doc',
    '.xlsx': 'xlsx',
    '.xls': 'xls',
    '.txt': 'txt',
    '.html': 'html',
    '.htm': 'html',
    '.csv': 'csv',
    '.jpg': 'jpg',
    '.jpeg': 'jpg',
    '.png': 'png',
    '.gif': 'gif',
    '.webp': 'webp',
    '.zip': 'zip',
    '.rar': 'rar',
    '.gz': 'gz',
    '.tar': 'tar',
    '.mp3': 'mp3',
    '.mp4': 'mp4',
    '.rtf': 'rtf',
    '.odt': 'odt',
    '.ods': 'ods',
    '.md': 'md',
    '.json': 'json',
    '.xml': 'xml'
}


class FileProcessor:
    """
    Cung cấp một giao diện thống nhất để xử lý và trích xuất nội dung từ nhiều định dạng file khác nhau.
    
    Hỗ trợ các định dạng phổ biến như PDF, DOCX, XLSX, TXT, HTML, và các định dạng khác.
    Cung cấp các phương thức để trích xuất văn bản, metadata, hình ảnh, bảng và cấu trúc từ file.
    """
    
    def __init__(self,
                 file_path: Optional[str] = None,
                 file_content: Optional[bytes] = None,
                 file_type: Optional[str] = None,
                 encoding: str = 'utf-8',
                 chunk_size: int = 4096,
                 extract_metadata: bool = True,
                 extract_images: bool = False,
                 ocr_enabled: bool = False,
                 language: str = 'eng',
                 temp_dir: Optional[str] = None,
                 max_file_size: int = 100 * 1024 * 1024,  # 100MB
                 timeout: int = 60,
                 verbose: bool = False,
                 logger: Optional[logging.Logger] = None,
                 **kwargs):
        """
        Khởi tạo FileProcessor.
        
        Args:
            file_path: Đường dẫn đến file cần xử lý
            file_content: Nội dung binary của file (thay thế cho file_path)
            file_type: Loại file (tự động phát hiện nếu không cung cấp)
            encoding: Encoding mặc định cho file văn bản
            chunk_size: Kích thước chunk khi đọc file lớn
            extract_metadata: Tự động trích xuất metadata
            extract_images: Tự động trích xuất hình ảnh
            ocr_enabled: Sử dụng OCR cho file PDF/hình ảnh
            language: Ngôn ngữ cho OCR (theo định dạng của Tesseract)
            temp_dir: Thư mục tạm (tự động tạo nếu không cung cấp)
            max_file_size: Kích thước file tối đa (byte)
            timeout: Thời gian tối đa cho việc xử lý file (giây)
            verbose: In thông tin chi tiết
            logger: Logger tùy chỉnh
            **kwargs: Các tham số bổ sung
        """
        # Thiết lập logger
        self.logger = logger or logging.getLogger(__name__)
        self.verbose = verbose
        
        # Thiết lập các tham số cơ bản
        self.encoding = encoding
        self.chunk_size = chunk_size
        self.extract_metadata = extract_metadata
        self.extract_images = extract_images
        self.ocr_enabled = ocr_enabled
        self.language = language
        self.max_file_size = max_file_size
        self.timeout = timeout
        
        # Các biến trạng thái
        self._file_path = None
        self._file_content = None
        self._file_type = None
        self._file_size = None
        self._temp_dir = temp_dir
        self._temp_files = []
        self._metadata = {}
        self._text_content = None
        self._html_content = None
        self._images = []
        self._tables = []
        self._loaded = False
        self._start_time = time.time()
        
        # Tạo thư mục tạm nếu cần
        if not self._temp_dir:
            self._temp_dir = tempfile.mkdtemp(prefix="fileprocessor_")
            
        # Kiểm tra và tải file
        if file_path:
            self.load_file(file_path=file_path, file_type=file_type)
        elif file_content:
            self.load_file(file_content=file_content, file_type=file_type)
    
    def __enter__(self):
        """Hỗ trợ context manager protocol."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Dọn dẹp tài nguyên khi thoát context."""
        self.cleanup()
    
    def load_file(self, file_path: Optional[str] = None, file_content: Optional[bytes] = None, file_type: Optional[str] = None) -> bool:
        """
        Tải file từ đường dẫn hoặc nội dung binary.
        
        Args:
            file_path: Đường dẫn đến file
            file_content: Nội dung binary của file
            file_type: Loại file (tự động phát hiện nếu không cung cấp)
            
        Returns:
            bool: True nếu tải thành công, False nếu thất bại
            
        Raises:
            FileNotFoundError: Nếu file không tồn tại
            PermissionError: Nếu không có quyền đọc file
            FileTooLargeError: Nếu file quá lớn
        """
        # Kiểm tra xem có cung cấp đường dẫn hoặc nội dung file không
        if not file_path and not file_content:
            self.logger.error("Không có đường dẫn file hoặc nội dung file được cung cấp")
            return False
            
        # Kiểm tra timeout
        if time.time() - self._start_time > self.timeout:
            raise TimeoutError(f"Quá thời gian xử lý: {self.timeout}s")
            
        try:
            # Xử lý file từ đường dẫn
            if file_path:
                file_path = os.path.abspath(file_path)
                
                # Kiểm tra file tồn tại
                if not os.path.isfile(file_path):
                    raise FileNotFoundError(f"File không tồn tại: {file_path}")
                    
                # Kiểm tra quyền đọc
                if not os.access(file_path, os.R_OK):
                    raise PermissionError(f"Không có quyền đọc file: {file_path}")
                    
                # Kiểm tra kích thước file
                file_size = os.path.getsize(file_path)
                if file_size > self.max_file_size:
                    raise FileTooLargeError(f"File quá lớn: {file_size} bytes (tối đa {self.max_file_size} bytes)")
                    
                self._file_path = file_path
                self._file_size = file_size
                
                # Đọc nội dung file nếu cần
                if not file_content:
                    with open(file_path, 'rb') as f:
                        file_content = f.read()
            
            # Lưu nội dung file
            if file_content:
                self._file_content = file_content
                if not self._file_size:
                    self._file_size = len(file_content)
            
            # Nhận dạng loại file
            if file_type:
                self._file_type = file_type.lower()
            else:
                self._file_type = self.identify_file_type()
                
            # Kiểm tra xem loại file có được hỗ trợ không
            if not self.is_supported_file_type():
                self.logger.warning(f"Loại file không được hỗ trợ: {self._file_type}")
                return False
                
            self._loaded = True
            
            # Tự động trích xuất metadata nếu cần
            if self.extract_metadata:
                self._metadata = self.extract_metadata()
                
            if self.verbose:
                self.logger.info(f"Đã tải file: {self._file_path or 'từ nội dung'}, loại: {self._file_type}, kích thước: {self._file_size} bytes")
                
            return True
            
        except (FileNotFoundError, PermissionError, FileTooLargeError) as e:
            self.logger.error(f"Lỗi khi tải file: {str(e)}")
            raise
        except Exception as e:
            self.logger.error(f"Lỗi không xác định khi tải file: {str(e)}")
            return False
    
    def identify_file_type(self) -> str:
        """
        Nhận dạng loại file dựa trên nội dung và/hoặc extension.
        
        Returns:
            str: Loại file (pdf, docx, txt, etc.)
        """
        file_type = None
        
        # Sử dụng python-magic nếu có sẵn
        if MAGIC_AVAILABLE and self._file_content:
            mime = magic.Magic(mime=True)
            mime_type = mime.from_buffer(self._file_content)
            file_type = MIME_TYPE_MAP.get(mime_type)
            
            if self.verbose and file_type:
                self.logger.info(f"Nhận dạng loại file từ MIME type: {mime_type} -> {file_type}")
        
        # Sử dụng mimetypes dựa trên extension
        if not file_type and self._file_path:
            ext = os.path.splitext(self._file_path)[1].lower()
            file_type = EXTENSION_MAP.get(ext)
            
            if self.verbose and file_type:
                self.logger.info(f"Nhận dạng loại file từ extension: {ext} -> {file_type}")
        
        # Kiểm tra các dấu hiệu đặc trưng trong nội dung file
        if not file_type and self._file_content:
            # Kiểm tra PDF header
            if self._file_content.startswith(b'%PDF'):
                file_type = 'pdf'
            # Kiểm tra các định dạng Office
            elif self._file_content.startswith(b'PK\x03\x04'):
                # Có thể là DOCX, XLSX, PPTX, ZIP
                # Cần kiểm tra thêm
                file_type = 'zip'  # Mặc định là zip
            # Kiểm tra HTML
            elif re.search(b'<!DOCTYPE html|<html', self._file_content[:1000], re.IGNORECASE):
                file_type = 'html'
            # Kiểm tra văn bản thuần
            elif self.is_text_file(self._file_content):
                file_type = 'txt'
                
            if self.verbose and file_type:
                self.logger.info(f"Nhận dạng loại file từ nội dung -> {file_type}")
        
        # Mặc định là văn bản thuần nếu không nhận dạng được
        if not file_type:
            file_type = 'txt'
            if self.verbose:
                self.logger.warning("Không thể nhận dạng loại file, sử dụng mặc định: txt")
        
        return file_type
    
    def is_text_file(self, content: bytes, sample_size: int = 1000) -> bool:
        """
        Kiểm tra xem nội dung có phải là văn bản thuần không.
        
        Args:
            content: Nội dung cần kiểm tra
            sample_size: Kích thước mẫu để kiểm tra
            
        Returns:
            bool: True nếu là văn bản thuần, False nếu không phải
        """
        # Lấy một mẫu của nội dung
        sample = content[:min(sample_size, len(content))]
        
        # Kiểm tra xem có ký tự null không
        if b'\x00' in sample:
            return False
            
        # Kiểm tra tỷ lệ ký tự có thể in được
        printable_chars = 0
        for byte in sample:
            if 32 <= byte <= 126 or byte in (9, 10, 13):  # TAB, LF, CR
                printable_chars += 1
                
        ratio = printable_chars / len(sample)
        
        # Nếu tỷ lệ ký tự có thể in được cao, có thể là văn bản thuần
        return ratio > 0.7
    
    def is_supported_file_type(self) -> bool:
        """
        Kiểm tra xem loại file có được hỗ trợ không.
        
        Returns:
            bool: True nếu được hỗ trợ, False nếu không được hỗ trợ
        """
        if not self._file_type:
            return False
            
        # Danh sách các loại file được hỗ trợ
        supported_types = {
            'pdf': PYPDF_AVAILABLE or PYMUPDF_AVAILABLE,
            'docx': PYTHON_DOCX_AVAILABLE,
            'xlsx': PANDAS_AVAILABLE or OPENPYXL_AVAILABLE,
            'xls': PANDAS_AVAILABLE,
            'txt': True,
            'html': BS4_AVAILABLE,
            'csv': True,
            'jpg': OCR_AVAILABLE,
            'png': OCR_AVAILABLE,
            'gif': OCR_AVAILABLE,
            'webp': OCR_AVAILABLE
        }
        
        # Kiểm tra xem loại file có trong danh sách không
        if self._file_type not in supported_types:
            return False
            
        # Kiểm tra xem có thư viện phụ thuộc không
        return supported_types[self._file_type]
    
    def extract_text(self) -> str:
        """
        Trích xuất văn bản từ file.
        
        Returns:
            str: Văn bản đã trích xuất
            
        Raises:
            UnsupportedFileTypeError: Nếu loại file không được hỗ trợ
            CorruptedFileError: Nếu file bị hỏng
        """
        if not self._loaded:
            self.logger.error("File chưa được tải")
            return ""
            
        # Kiểm tra timeout
        if time.time() - self._start_time > self.timeout:
            raise TimeoutError(f"Quá thời gian xử lý: {self.timeout}s")
            
        # Kiểm tra xem đã trích xuất chưa
        if self._text_content is not None:
            return self._text_content
            
        try:
            # Xử lý theo loại file
            if self._file_type == 'pdf':
                text = self._extract_text_from_pdf()
            elif self._file_type == 'docx':
                text = self._extract_text_from_docx()
            elif self._file_type in ('xlsx', 'xls'):
                text = self._extract_text_from_excel()
            elif self._file_type == 'html':
                text = self._extract_text_from_html()
            elif self._file_type == 'csv':
                text = self._extract_text_from_csv()
            elif self._file_type in ('jpg', 'png', 'gif', 'webp'):
                text = self._extract_text_from_image()
            elif self._file_type == 'txt':
                text = self._extract_text_from_txt()
            else:
                raise UnsupportedFileTypeError(f"Không hỗ trợ trích xuất văn bản từ loại file: {self._file_type}")
                
            # Lưu kết quả
            self._text_content = text
            
            if self.verbose:
                self.logger.info(f"Đã trích xuất {len(text)} ký tự từ file {self._file_type}")
                
            return text
            
        except UnsupportedFileTypeError:
            raise
        except Exception as e:
            self.logger.error(f"Lỗi khi trích xuất văn bản: {str(e)}")
            raise CorruptedFileError(f"Không thể trích xuất văn bản từ file bị hỏng: {str(e)}")
    
    def cleanup(self) -> None:
        """
        Dọn dẹp tài nguyên tạm thời.
        """
        # Xóa các file tạm
        for temp_file in self._temp_files:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                    if self.verbose:
                        self.logger.info(f"Đã xóa file tạm: {temp_file}")
            except Exception as e:
                self.logger.warning(f"Không thể xóa file tạm {temp_file}: {str(e)}")
                
        # Xóa thư mục tạm nếu đã tạo
        if self._temp_dir and os.path.exists(self._temp_dir) and self._temp_dir.startswith(tempfile.gettempdir()):
            try:
                # Chỉ xóa thư mục nếu nó trống
                os.rmdir(self._temp_dir)
                if self.verbose:
                    self.logger.info(f"Đã xóa thư mục tạm: {self._temp_dir}")
            except Exception as e:
                self.logger.warning(f"Không thể xóa thư mục tạm {self._temp_dir}: {str(e)}")
    
    # Các phương thức xử lý riêng cho từng loại file sẽ được triển khai trong phần tiếp theo
    
    def _extract_text_from_pdf(self) -> str:
        """Trích xuất văn bản từ file PDF."""
        text = ""

        # Thử với PyMuPDF trước (tốt hơn)
        if PYMUPDF_AVAILABLE:
            try:
                # Tạo file tạm để PyMuPDF đọc
                temp_file = os.path.join(self._temp_dir, f"temp_pdf_{int(time.time())}.pdf")
                with open(temp_file, 'wb') as f:
                    f.write(self._file_content)
                self._temp_files.append(temp_file)

                # Mở PDF với PyMuPDF
                doc = fitz.open(temp_file)

                for page_num in range(len(doc)):
                    page = doc.load_page(page_num)
                    text += page.get_text()

                    # Trích xuất hình ảnh nếu cần
                    if self.extract_images:
                        image_list = page.get_images()
                        for img_index, img in enumerate(image_list):
                            xref = img[0]
                            pix = fitz.Pixmap(doc, xref)
                            if pix.n - pix.alpha < 4:  # GRAY or RGB
                                img_data = pix.tobytes("png")
                                self._images.append({
                                    'page': page_num + 1,
                                    'index': img_index,
                                    'data': img_data,
                                    'format': 'png'
                                })
                            pix = None

                doc.close()

            except Exception as e:
                self.logger.warning(f"Lỗi khi sử dụng PyMuPDF: {str(e)}")
                text = ""

        # Fallback sang PyPDF2 nếu PyMuPDF thất bại
        if not text and PYPDF_AVAILABLE:
            try:
                pdf_reader = PyPDF2.PdfReader(io.BytesIO(self._file_content))

                for page_num in range(len(pdf_reader.pages)):
                    page = pdf_reader.pages[page_num]
                    text += page.extract_text()

            except Exception as e:
                self.logger.warning(f"Lỗi khi sử dụng PyPDF2: {str(e)}")
                text = ""

        # Nếu không có text và OCR được bật
        if not text.strip() and self.ocr_enabled and OCR_AVAILABLE:
            try:
                text = self._extract_text_with_ocr_from_pdf()
            except Exception as e:
                self.logger.warning(f"Lỗi khi sử dụng OCR: {str(e)}")

        return text.strip()
    
    def _extract_text_with_ocr_from_pdf(self) -> str:
        """Trích xuất văn bản từ PDF bằng OCR."""
        if not OCR_AVAILABLE:
            return ""

        try:
            # Tạo file tạm để PyMuPDF đọc
            temp_file = os.path.join(self._temp_dir, f"temp_pdf_ocr_{int(time.time())}.pdf")
            with open(temp_file, 'wb') as f:
                f.write(self._file_content)
            self._temp_files.append(temp_file)

            text = ""
            if PYMUPDF_AVAILABLE:
                doc = fitz.open(temp_file)
                for page_num in range(len(doc)):
                    page = doc.load_page(page_num)
                    # Chuyển page thành hình ảnh
                    pix = page.get_pixmap()
                    img_data = pix.tobytes("png")

                    # Sử dụng OCR
                    image = PIL.Image.open(io.BytesIO(img_data))
                    page_text = pytesseract.image_to_string(image, lang=self.language)
                    text += page_text + "\n"

                doc.close()

            return text.strip()

        except Exception as e:
            self.logger.warning(f"Lỗi khi sử dụng OCR cho PDF: {str(e)}")
            return ""

    def _extract_text_from_docx(self) -> str:
        """Trích xuất văn bản từ file DOCX."""
        if not PYTHON_DOCX_AVAILABLE:
            raise UnsupportedFileTypeError("Thư viện python-docx không có sẵn")

        try:
            # Tạo file tạm để python-docx đọc
            temp_file = os.path.join(self._temp_dir, f"temp_docx_{int(time.time())}.docx")
            with open(temp_file, 'wb') as f:
                f.write(self._file_content)
            self._temp_files.append(temp_file)

            # Mở document với python-docx
            doc = Document(temp_file)

            text = ""

            # Trích xuất văn bản từ các paragraph
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"

            # Trích xuất văn bản từ các table
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        text += cell.text + "\t"
                    text += "\n"

            # Trích xuất hình ảnh nếu cần
            if self.extract_images:
                try:
                    for rel in doc.part.rels.values():
                        if "image" in rel.target_ref:
                            img_data = rel.target_part.blob
                            self._images.append({
                                'data': img_data,
                                'format': rel.target_ref.split('.')[-1],
                                'source': 'docx_embedded'
                            })
                except Exception as e:
                    self.logger.warning(f"Lỗi khi trích xuất hình ảnh từ DOCX: {str(e)}")

            return text.strip()

        except Exception as e:
            self.logger.error(f"Lỗi khi trích xuất văn bản từ DOCX: {str(e)}")
            raise CorruptedFileError(f"Không thể trích xuất văn bản từ file DOCX: {str(e)}")
    
    def _extract_text_from_excel(self) -> str:
        """Trích xuất văn bản từ file Excel."""
        text = ""

        # Thử với pandas trước
        if PANDAS_AVAILABLE:
            try:
                # Tạo file tạm
                temp_file = os.path.join(self._temp_dir, f"temp_excel_{int(time.time())}.xlsx")
                with open(temp_file, 'wb') as f:
                    f.write(self._file_content)
                self._temp_files.append(temp_file)

                # Đọc tất cả các sheet
                excel_file = pd.ExcelFile(temp_file)

                for sheet_name in excel_file.sheet_names:
                    df = pd.read_excel(temp_file, sheet_name=sheet_name)

                    # Chuyển DataFrame thành text
                    text += f"=== Sheet: {sheet_name} ===\n"
                    text += df.to_string(index=False) + "\n\n"

                    # Lưu table data nếu cần
                    if hasattr(self, '_tables'):
                        self._tables.append({
                            'sheet_name': sheet_name,
                            'data': df.to_dict('records'),
                            'columns': df.columns.tolist()
                        })

            except Exception as e:
                self.logger.warning(f"Lỗi khi sử dụng pandas: {str(e)}")
                text = ""

        # Fallback sang openpyxl
        if not text and OPENPYXL_AVAILABLE and self._file_type == 'xlsx':
            try:
                # Tạo file tạm
                temp_file = os.path.join(self._temp_dir, f"temp_excel_openpyxl_{int(time.time())}.xlsx")
                with open(temp_file, 'wb') as f:
                    f.write(self._file_content)
                self._temp_files.append(temp_file)

                from openpyxl import load_workbook
                wb = load_workbook(temp_file)

                for sheet_name in wb.sheetnames:
                    ws = wb[sheet_name]
                    text += f"=== Sheet: {sheet_name} ===\n"

                    for row in ws.iter_rows(values_only=True):
                        row_text = "\t".join([str(cell) if cell is not None else "" for cell in row])
                        text += row_text + "\n"
                    text += "\n"

            except Exception as e:
                self.logger.warning(f"Lỗi khi sử dụng openpyxl: {str(e)}")
                text = ""

        return text.strip()

    def _extract_text_from_html(self) -> str:
        """Trích xuất văn bản từ file HTML."""
        if not BS4_AVAILABLE:
            # Fallback đơn giản bằng regex
            try:
                html_content = self._file_content.decode(self.encoding)
                # Loại bỏ script và style tags
                html_content = re.sub(r'<script[^>]*>.*?</script>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
                html_content = re.sub(r'<style[^>]*>.*?</style>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
                # Loại bỏ HTML tags
                text = re.sub(r'<[^>]+>', '', html_content)
                # Dọn dẹp whitespace
                text = re.sub(r'\s+', ' ', text).strip()
                return text
            except Exception as e:
                self.logger.warning(f"Lỗi khi xử lý HTML bằng regex: {str(e)}")
                return ""

        try:
            html_content = self._file_content.decode(self.encoding)
            soup = BeautifulSoup(html_content, 'html.parser')

            # Loại bỏ script và style elements
            for script in soup(["script", "style"]):
                script.decompose()

            # Trích xuất text
            text = soup.get_text()

            # Dọn dẹp whitespace
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)

            # Lưu HTML content nếu cần
            self._html_content = html_content

            return text

        except Exception as e:
            self.logger.error(f"Lỗi khi trích xuất văn bản từ HTML: {str(e)}")
            raise CorruptedFileError(f"Không thể trích xuất văn bản từ file HTML: {str(e)}")

    def _extract_text_from_csv(self) -> str:
        """Trích xuất văn bản từ file CSV."""
        try:
            # Thử decode với encoding đã chỉ định
            try:
                csv_content = self._file_content.decode(self.encoding)
            except UnicodeDecodeError:
                # Thử với các encoding phổ biến
                for enc in ['utf-8', 'latin1', 'cp1252']:
                    try:
                        csv_content = self._file_content.decode(enc)
                        break
                    except UnicodeDecodeError:
                        pass
                else:
                    csv_content = self._file_content.decode('latin1', errors='replace')

            # Sử dụng pandas nếu có
            if PANDAS_AVAILABLE:
                try:
                    df = pd.read_csv(io.StringIO(csv_content))
                    text = df.to_string(index=False)

                    # Lưu table data
                    if hasattr(self, '_tables'):
                        self._tables.append({
                            'data': df.to_dict('records'),
                            'columns': df.columns.tolist()
                        })

                    return text
                except Exception as e:
                    self.logger.warning(f"Lỗi khi sử dụng pandas cho CSV: {str(e)}")

            # Fallback đơn giản
            return csv_content

        except Exception as e:
            self.logger.error(f"Lỗi khi trích xuất văn bản từ CSV: {str(e)}")
            raise CorruptedFileError(f"Không thể trích xuất văn bản từ file CSV: {str(e)}")

    def _extract_text_from_image(self) -> str:
        """Trích xuất văn bản từ file hình ảnh sử dụng OCR."""
        if not OCR_AVAILABLE:
            raise UnsupportedFileTypeError("Thư viện OCR (pytesseract) không có sẵn")

        try:
            # Mở hình ảnh từ bytes
            image = PIL.Image.open(io.BytesIO(self._file_content))

            # Sử dụng OCR để trích xuất text
            text = pytesseract.image_to_string(image, lang=self.language)

            return text.strip()

        except Exception as e:
            self.logger.error(f"Lỗi khi trích xuất văn bản từ hình ảnh: {str(e)}")
            raise CorruptedFileError(f"Không thể trích xuất văn bản từ file hình ảnh: {str(e)}")
    
    def _extract_text_from_txt(self) -> str:
        """Trích xuất văn bản từ file TXT."""
        if not self._file_content:
            return ""
            
        try:
            # Thử với encoding đã chỉ định
            text = self._file_content.decode(self.encoding)
        except UnicodeDecodeError:
            # Thử với các encoding phổ biến khác
            for enc in ['utf-8', 'latin1', 'cp1252', 'utf-16']:
                try:
                    text = self._file_content.decode(enc)
                    if self.verbose:
                        self.logger.info(f"Đã decode thành công với encoding: {enc}")
                    break
                except UnicodeDecodeError:
                    pass
            else:
                # Nếu tất cả đều thất bại, sử dụng 'latin1' với xử lý lỗi
                text = self._file_content.decode('latin1', errors='replace')
                self.logger.warning("Không thể xác định encoding chính xác, sử dụng latin1 với xử lý lỗi")
                
        return text

    def extract_metadata(self) -> Dict[str, Any]:
        """
        Trích xuất metadata từ file.

        Returns:
            Dict chứa metadata của file
        """
        if not self._loaded:
            self.logger.error("File chưa được tải")
            return {}

        metadata = {
            'file_type': self._file_type,
            'file_size': self._file_size,
            'file_path': self._file_path,
            'processing_time': time.time() - self._start_time
        }

        try:
            # Metadata cơ bản cho tất cả file
            if self._file_content:
                metadata['md5_hash'] = hashlib.md5(self._file_content).hexdigest()
                metadata['sha256_hash'] = hashlib.sha256(self._file_content).hexdigest()

            # Metadata riêng cho từng loại file
            if self._file_type == 'pdf' and PYMUPDF_AVAILABLE:
                metadata.update(self._extract_pdf_metadata())
            elif self._file_type == 'docx' and PYTHON_DOCX_AVAILABLE:
                metadata.update(self._extract_docx_metadata())
            elif self._file_type in ('xlsx', 'xls') and PANDAS_AVAILABLE:
                metadata.update(self._extract_excel_metadata())
            elif self._file_type == 'html':
                metadata.update(self._extract_html_metadata())
            elif self._file_type in ('jpg', 'png', 'gif', 'webp'):
                metadata.update(self._extract_image_metadata())

            self._metadata = metadata
            return metadata

        except Exception as e:
            self.logger.warning(f"Lỗi khi trích xuất metadata: {str(e)}")
            return metadata

    def _extract_pdf_metadata(self) -> Dict[str, Any]:
        """Trích xuất metadata từ PDF."""
        metadata = {}
        try:
            temp_file = os.path.join(self._temp_dir, f"temp_pdf_meta_{int(time.time())}.pdf")
            with open(temp_file, 'wb') as f:
                f.write(self._file_content)
            self._temp_files.append(temp_file)

            doc = fitz.open(temp_file)
            pdf_metadata = doc.metadata

            metadata.update({
                'pages': len(doc),
                'title': pdf_metadata.get('title', ''),
                'author': pdf_metadata.get('author', ''),
                'subject': pdf_metadata.get('subject', ''),
                'creator': pdf_metadata.get('creator', ''),
                'producer': pdf_metadata.get('producer', ''),
                'creation_date': pdf_metadata.get('creationDate', ''),
                'modification_date': pdf_metadata.get('modDate', ''),
                'encrypted': doc.needs_pass,
                'pdf_version': doc.pdf_version()
            })

            doc.close()

        except Exception as e:
            self.logger.warning(f"Lỗi khi trích xuất metadata PDF: {str(e)}")

        return metadata

    def _extract_docx_metadata(self) -> Dict[str, Any]:
        """Trích xuất metadata từ DOCX."""
        metadata = {}
        try:
            temp_file = os.path.join(self._temp_dir, f"temp_docx_meta_{int(time.time())}.docx")
            with open(temp_file, 'wb') as f:
                f.write(self._file_content)
            self._temp_files.append(temp_file)

            doc = Document(temp_file)
            core_props = doc.core_properties

            metadata.update({
                'title': core_props.title or '',
                'author': core_props.author or '',
                'subject': core_props.subject or '',
                'keywords': core_props.keywords or '',
                'comments': core_props.comments or '',
                'category': core_props.category or '',
                'created': str(core_props.created) if core_props.created else '',
                'modified': str(core_props.modified) if core_props.modified else '',
                'last_modified_by': core_props.last_modified_by or '',
                'revision': core_props.revision,
                'paragraphs_count': len(doc.paragraphs),
                'tables_count': len(doc.tables)
            })

        except Exception as e:
            self.logger.warning(f"Lỗi khi trích xuất metadata DOCX: {str(e)}")

        return metadata

    def _extract_excel_metadata(self) -> Dict[str, Any]:
        """Trích xuất metadata từ Excel."""
        metadata = {}
        try:
            temp_file = os.path.join(self._temp_dir, f"temp_excel_meta_{int(time.time())}.xlsx")
            with open(temp_file, 'wb') as f:
                f.write(self._file_content)
            self._temp_files.append(temp_file)

            excel_file = pd.ExcelFile(temp_file)

            metadata.update({
                'sheets_count': len(excel_file.sheet_names),
                'sheet_names': excel_file.sheet_names
            })

            # Thông tin chi tiết cho mỗi sheet
            sheets_info = []
            for sheet_name in excel_file.sheet_names:
                df = pd.read_excel(temp_file, sheet_name=sheet_name)
                sheets_info.append({
                    'name': sheet_name,
                    'rows': len(df),
                    'columns': len(df.columns),
                    'column_names': df.columns.tolist()
                })

            metadata['sheets_info'] = sheets_info

        except Exception as e:
            self.logger.warning(f"Lỗi khi trích xuất metadata Excel: {str(e)}")

        return metadata

    def _extract_html_metadata(self) -> Dict[str, Any]:
        """Trích xuất metadata từ HTML."""
        metadata = {}
        try:
            html_content = self._file_content.decode(self.encoding)

            if BS4_AVAILABLE:
                soup = BeautifulSoup(html_content, 'html.parser')

                # Title
                title_tag = soup.find('title')
                if title_tag:
                    metadata['title'] = title_tag.get_text().strip()

                # Meta tags
                meta_tags = soup.find_all('meta')
                for meta in meta_tags:
                    name = meta.get('name') or meta.get('property')
                    content = meta.get('content')
                    if name and content:
                        metadata[f'meta_{name}'] = content

                # Links count
                metadata['links_count'] = len(soup.find_all('a'))
                metadata['images_count'] = len(soup.find_all('img'))

            else:
                # Fallback với regex
                title_match = re.search(r'<title[^>]*>(.*?)</title>', html_content, re.IGNORECASE | re.DOTALL)
                if title_match:
                    metadata['title'] = title_match.group(1).strip()

        except Exception as e:
            self.logger.warning(f"Lỗi khi trích xuất metadata HTML: {str(e)}")

        return metadata

    def _extract_image_metadata(self) -> Dict[str, Any]:
        """Trích xuất metadata từ hình ảnh."""
        metadata = {}
        try:
            image = PIL.Image.open(io.BytesIO(self._file_content))

            metadata.update({
                'width': image.width,
                'height': image.height,
                'mode': image.mode,
                'format': image.format,
                'has_transparency': image.mode in ('RGBA', 'LA') or 'transparency' in image.info
            })

            # EXIF data nếu có
            if hasattr(image, '_getexif') and image._getexif():
                exif_data = image._getexif()
                if exif_data:
                    metadata['exif'] = {str(k): str(v) for k, v in exif_data.items()}

        except Exception as e:
            self.logger.warning(f"Lỗi khi trích xuất metadata hình ảnh: {str(e)}")

        return metadata


def process_file(file_path: str, **kwargs) -> Dict[str, Any]:
    """
    Hàm tiện ích để xử lý trọn vẹn một file.
    
    Args:
        file_path: Đường dẫn đến file cần xử lý
        **kwargs: Các tham số tùy chọn cho FileProcessor
        
    Returns:
        Dict chứa nội dung đã trích xuất, metadata và các thông tin khác
    """
    result = {
        'success': False,
        'file_path': file_path,
        'file_type': None,
        'file_size': None,
        'text': None,
        'metadata': None,
        'error': None
    }
    
    try:
        with FileProcessor(file_path=file_path, **kwargs) as processor:
            result['file_type'] = processor._file_type
            result['file_size'] = processor._file_size
            result['text'] = processor.extract_text()
            result['metadata'] = processor._metadata
            result['success'] = True
    except Exception as e:
        result['error'] = str(e)
        
    return result 