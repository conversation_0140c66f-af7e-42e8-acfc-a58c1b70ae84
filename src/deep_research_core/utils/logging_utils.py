#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Logging Utils - <PERSON><PERSON> thống logging nâng cao cho Deep Research Core

Cung cấp:
- Structured logging với JSON
- Log rotation
- Performance logging
- Error tracking
- Context logging
- Multiple handlers
"""

import os
import sys
import json
import time
import logging
import logging.handlers
import threading
import traceback
from typing import Dict, Any, Optional, Union, List
from datetime import datetime
from pathlib import Path
import functools

logger = logging.getLogger(__name__)


class JSONFormatter(logging.Formatter):
    """JSON formatter cho structured logging"""
    
    def __init__(self, include_extra: bool = True):
        super().__init__()
        self.include_extra = include_extra
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record thành JSON"""
        log_data = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'thread': record.thread,
            'thread_name': record.threadName,
        }
        
        # Thêm exception info nếu có
        if record.exc_info:
            log_data['exception'] = {
                'type': record.exc_info[0].__name__,
                'message': str(record.exc_info[1]),
                'traceback': traceback.format_exception(*record.exc_info)
            }
        
        # Thêm extra fields nếu có
        if self.include_extra:
            for key, value in record.__dict__.items():
                if key not in log_data and not key.startswith('_'):
                    try:
                        # Chỉ thêm serializable values
                        json.dumps(value)
                        log_data[key] = value
                    except (TypeError, ValueError):
                        log_data[key] = str(value)
        
        return json.dumps(log_data, ensure_ascii=False)


class PerformanceLogger:
    """Logger cho performance metrics"""
    
    def __init__(self, logger_name: str = "performance"):
        self.logger = logging.getLogger(logger_name)
        self._timers = {}
        self._lock = threading.Lock()
    
    def start_timer(self, name: str):
        """Bắt đầu timer"""
        with self._lock:
            self._timers[name] = time.time()
    
    def end_timer(self, name: str, extra_data: Optional[Dict[str, Any]] = None):
        """Kết thúc timer và log performance"""
        with self._lock:
            if name not in self._timers:
                self.logger.warning(f"Timer '{name}' not found")
                return
            
            duration = time.time() - self._timers.pop(name)
            
            log_data = {
                'timer_name': name,
                'duration_seconds': duration,
                'duration_ms': duration * 1000,
            }
            
            if extra_data:
                log_data.update(extra_data)
            
            self.logger.info(f"Performance: {name}", extra=log_data)
    
    def log_metric(self, metric_name: str, value: Union[int, float], 
                   unit: str = "", extra_data: Optional[Dict[str, Any]] = None):
        """Log một metric"""
        log_data = {
            'metric_name': metric_name,
            'metric_value': value,
            'metric_unit': unit,
        }
        
        if extra_data:
            log_data.update(extra_data)
        
        self.logger.info(f"Metric: {metric_name}={value}{unit}", extra=log_data)


class ContextLogger:
    """Logger với context information"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self._context = threading.local()
    
    def set_context(self, **kwargs):
        """Set context cho thread hiện tại"""
        if not hasattr(self._context, 'data'):
            self._context.data = {}
        self._context.data.update(kwargs)
    
    def clear_context(self):
        """Clear context"""
        if hasattr(self._context, 'data'):
            self._context.data.clear()
    
    def get_context(self) -> Dict[str, Any]:
        """Lấy context hiện tại"""
        if hasattr(self._context, 'data'):
            return self._context.data.copy()
        return {}
    
    def _log_with_context(self, level: int, msg: str, *args, **kwargs):
        """Log với context"""
        extra = kwargs.get('extra', {})
        extra.update(self.get_context())
        kwargs['extra'] = extra
        
        self.logger.log(level, msg, *args, **kwargs)
    
    def debug(self, msg: str, *args, **kwargs):
        self._log_with_context(logging.DEBUG, msg, *args, **kwargs)
    
    def info(self, msg: str, *args, **kwargs):
        self._log_with_context(logging.INFO, msg, *args, **kwargs)
    
    def warning(self, msg: str, *args, **kwargs):
        self._log_with_context(logging.WARNING, msg, *args, **kwargs)
    
    def error(self, msg: str, *args, **kwargs):
        self._log_with_context(logging.ERROR, msg, *args, **kwargs)
    
    def critical(self, msg: str, *args, **kwargs):
        self._log_with_context(logging.CRITICAL, msg, *args, **kwargs)


class ErrorTracker:
    """Tracker cho errors và exceptions"""
    
    def __init__(self, logger_name: str = "errors"):
        self.logger = logging.getLogger(logger_name)
        self.error_counts = {}
        self._lock = threading.Lock()
    
    def track_error(self, error: Exception, context: Optional[Dict[str, Any]] = None):
        """Track một error"""
        error_type = type(error).__name__
        error_msg = str(error)
        
        with self._lock:
            key = f"{error_type}:{error_msg}"
            self.error_counts[key] = self.error_counts.get(key, 0) + 1
        
        log_data = {
            'error_type': error_type,
            'error_message': error_msg,
            'error_count': self.error_counts[key],
            'traceback': traceback.format_exc(),
        }
        
        if context:
            log_data['context'] = context
        
        self.logger.error(f"Error tracked: {error_type}", extra=log_data)
    
    def get_error_stats(self) -> Dict[str, int]:
        """Lấy thống kê errors"""
        with self._lock:
            return self.error_counts.copy()


class LoggingManager:
    """Manager cho toàn bộ logging system"""
    
    def __init__(self, 
                 log_dir: str = "logs",
                 log_level: str = "INFO",
                 max_file_size: int = 10 * 1024 * 1024,  # 10MB
                 backup_count: int = 5,
                 use_json_format: bool = True,
                 console_output: bool = True):
        """
        Args:
            log_dir: Thư mục chứa log files
            log_level: Log level mặc định
            max_file_size: Kích thước tối đa của log file
            backup_count: Số file backup
            use_json_format: Có sử dụng JSON format không
            console_output: Có output ra console không
        """
        self.log_dir = Path(log_dir)
        self.log_level = getattr(logging, log_level.upper())
        self.max_file_size = max_file_size
        self.backup_count = backup_count
        self.use_json_format = use_json_format
        self.console_output = console_output
        
        # Tạo thư mục logs
        self.log_dir.mkdir(exist_ok=True)
        
        # Setup logging
        self._setup_logging()
        
        # Performance logger
        self.performance = PerformanceLogger()
        
        # Error tracker
        self.error_tracker = ErrorTracker()
    
    def _setup_logging(self):
        """Setup logging configuration"""
        # Root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(self.log_level)
        
        # Clear existing handlers
        root_logger.handlers.clear()
        
        # Formatter
        if self.use_json_format:
            formatter = JSONFormatter()
        else:
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
        
        # File handler với rotation
        file_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / "app.log",
            maxBytes=self.max_file_size,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
        
        # Error file handler
        error_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / "error.log",
            maxBytes=self.max_file_size,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        root_logger.addHandler(error_handler)
        
        # Console handler
        if self.console_output:
            console_handler = logging.StreamHandler(sys.stdout)
            if self.use_json_format:
                # Sử dụng format đơn giản cho console
                console_formatter = logging.Formatter(
                    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                )
                console_handler.setFormatter(console_formatter)
            else:
                console_handler.setFormatter(formatter)
            root_logger.addHandler(console_handler)
    
    def get_logger(self, name: str) -> ContextLogger:
        """Lấy logger với context support"""
        logger = logging.getLogger(name)
        return ContextLogger(logger)
    
    def log_performance(self, name: str, duration: float, **kwargs):
        """Log performance metric"""
        self.performance.log_metric(name, duration, "seconds", kwargs)
    
    def track_error(self, error: Exception, **context):
        """Track error"""
        self.error_tracker.track_error(error, context)


def performance_timer(logger_name: str = "performance"):
    """Decorator để đo performance của function"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            perf_logger = PerformanceLogger(logger_name)
            timer_name = f"{func.__module__}.{func.__name__}"
            
            perf_logger.start_timer(timer_name)
            try:
                result = func(*args, **kwargs)
                perf_logger.end_timer(timer_name, {
                    'function': func.__name__,
                    'module': func.__module__,
                    'success': True
                })
                return result
            except Exception as e:
                perf_logger.end_timer(timer_name, {
                    'function': func.__name__,
                    'module': func.__module__,
                    'success': False,
                    'error': str(e)
                })
                raise
        return wrapper
    return decorator


def log_errors(logger_name: str = "errors"):
    """Decorator để tự động log errors"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_tracker = ErrorTracker(logger_name)
                error_tracker.track_error(e, {
                    'function': func.__name__,
                    'module': func.__module__,
                    'args': str(args)[:200],  # Limit length
                    'kwargs': str(kwargs)[:200]
                })
                raise
        return wrapper
    return decorator


# Singleton instance
_logging_manager = None

def get_logging_manager(**kwargs) -> LoggingManager:
    """Lấy logging manager singleton"""
    global _logging_manager
    if _logging_manager is None:
        _logging_manager = LoggingManager(**kwargs)
    return _logging_manager

def get_logger(name: str) -> ContextLogger:
    """Shortcut để lấy logger"""
    return get_logging_manager().get_logger(name)
