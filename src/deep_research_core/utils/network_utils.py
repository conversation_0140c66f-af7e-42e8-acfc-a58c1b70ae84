#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Network Utils - Tiện ích mạng cho Deep Research Core

Cung cấp:
- HTTP Client với retry và rate limiting
- User Agent rotation
- Proxy support
- Download manager với resume
- Network monitoring
"""

import os
import time
import random
import hashlib
import threading
from typing import Dict, List, Optional, Union, Any, Callable
from urllib.parse import urlparse, urljoin
import logging

# Core imports
try:
    import requests
    from requests.adapters import HTTPAdapter
    from requests.packages.urllib3.util.retry import Retry
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False

try:
    import aiohttp
    import asyncio
    AIOHTTP_AVAILABLE = True
except ImportError:
    AIOHTTP_AVAILABLE = False

logger = logging.getLogger(__name__)


class RateLimiter:
    """Rate limiter với token bucket algorithm"""
    
    def __init__(self, max_requests: int = 10, time_window: int = 60):
        """
        Args:
            max_requests: <PERSON><PERSON> request tối đa trong time_window
            time_window: <PERSON><PERSON><PERSON> sổ thời gian (giây)
        """
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = []
        self._lock = threading.Lock()
    
    def can_proceed(self) -> bool:
        """Kiểm tra có thể thực hiện request không"""
        with self._lock:
            now = time.time()
            # Loại bỏ requests cũ
            self.requests = [req_time for req_time in self.requests 
                           if now - req_time < self.time_window]
            
            return len(self.requests) < self.max_requests
    
    def add_request(self):
        """Thêm request vào tracker"""
        with self._lock:
            self.requests.append(time.time())
    
    def wait_time(self) -> float:
        """Tính thời gian cần chờ để có thể request tiếp"""
        with self._lock:
            if len(self.requests) < self.max_requests:
                return 0.0
            
            oldest_request = min(self.requests)
            return max(0.0, self.time_window - (time.time() - oldest_request))


class UserAgentRotator:
    """Xoay User-Agent để tránh bị block"""
    
    def __init__(self):
        self.user_agents = [
            # Chrome
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            
            # Firefox
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (X11; Linux x86_64; rv:121.0) Gecko/20100101 Firefox/121.0',
            
            # Safari
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15',
            'Mozilla/5.0 (iPhone; CPU iPhone OS 17_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Mobile/15E148 Safari/604.1',
            
            # Edge
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
        ]
        self._lock = threading.Lock()
        self._last_used = {}
    
    def get_user_agent(self, domain: Optional[str] = None) -> str:
        """
        Lấy User-Agent, có thể sticky theo domain
        
        Args:
            domain: Domain để sticky User-Agent
        """
        with self._lock:
            if domain and domain in self._last_used:
                return self._last_used[domain]
            
            user_agent = random.choice(self.user_agents)
            
            if domain:
                self._last_used[domain] = user_agent
            
            return user_agent


class ProxyManager:
    """Quản lý proxy rotation"""
    
    def __init__(self, proxies: Optional[List[str]] = None):
        """
        Args:
            proxies: Danh sách proxy URLs
        """
        self.proxies = proxies or []
        self.failed_proxies = set()
        self._lock = threading.Lock()
        self._current_index = 0
    
    def get_proxy(self) -> Optional[Dict[str, str]]:
        """Lấy proxy tiếp theo"""
        if not self.proxies:
            return None
        
        with self._lock:
            available_proxies = [p for p in self.proxies if p not in self.failed_proxies]
            
            if not available_proxies:
                # Reset failed proxies nếu tất cả đều fail
                self.failed_proxies.clear()
                available_proxies = self.proxies
            
            if not available_proxies:
                return None
            
            proxy_url = available_proxies[self._current_index % len(available_proxies)]
            self._current_index += 1
            
            return {
                'http': proxy_url,
                'https': proxy_url
            }
    
    def mark_failed(self, proxy_url: str):
        """Đánh dấu proxy bị lỗi"""
        with self._lock:
            self.failed_proxies.add(proxy_url)


class HTTPClient:
    """HTTP Client với retry, rate limiting và proxy support"""
    
    def __init__(self, 
                 max_retries: int = 3,
                 backoff_factor: float = 0.3,
                 rate_limit: int = 10,
                 rate_window: int = 60,
                 timeout: int = 30,
                 proxies: Optional[List[str]] = None,
                 use_user_agent_rotation: bool = True):
        """
        Args:
            max_retries: Số lần retry tối đa
            backoff_factor: Hệ số backoff cho retry
            rate_limit: Số request tối đa trong rate_window
            rate_window: Cửa sổ thời gian rate limit (giây)
            timeout: Timeout cho request (giây)
            proxies: Danh sách proxy URLs
            use_user_agent_rotation: Có xoay User-Agent không
        """
        if not REQUESTS_AVAILABLE:
            raise ImportError("requests library is required for HTTPClient")
        
        self.max_retries = max_retries
        self.timeout = timeout
        
        # Rate limiter
        self.rate_limiter = RateLimiter(rate_limit, rate_window)
        
        # User agent rotation
        self.ua_rotator = UserAgentRotator() if use_user_agent_rotation else None
        
        # Proxy manager
        self.proxy_manager = ProxyManager(proxies) if proxies else None
        
        # Session với retry strategy
        self.session = requests.Session()
        
        retry_strategy = Retry(
            total=max_retries,
            backoff_factor=backoff_factor,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS", "POST"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # Stats
        self.stats = {
            'requests_made': 0,
            'requests_failed': 0,
            'bytes_downloaded': 0,
            'rate_limited': 0
        }
    
    def _prepare_request(self, url: str, **kwargs) -> Dict[str, Any]:
        """Chuẩn bị request với headers, proxy, etc."""
        # User-Agent
        if self.ua_rotator:
            domain = urlparse(url).netloc
            kwargs.setdefault('headers', {})
            kwargs['headers']['User-Agent'] = self.ua_rotator.get_user_agent(domain)
        
        # Proxy
        if self.proxy_manager and 'proxies' not in kwargs:
            proxy = self.proxy_manager.get_proxy()
            if proxy:
                kwargs['proxies'] = proxy
        
        # Timeout
        kwargs.setdefault('timeout', self.timeout)
        
        return kwargs
    
    def _handle_rate_limit(self):
        """Xử lý rate limiting"""
        if not self.rate_limiter.can_proceed():
            wait_time = self.rate_limiter.wait_time()
            if wait_time > 0:
                logger.info(f"Rate limited, waiting {wait_time:.2f} seconds")
                time.sleep(wait_time)
                self.stats['rate_limited'] += 1
        
        self.rate_limiter.add_request()
    
    def get(self, url: str, **kwargs) -> requests.Response:
        """GET request với rate limiting và retry"""
        self._handle_rate_limit()
        
        kwargs = self._prepare_request(url, **kwargs)
        
        try:
            response = self.session.get(url, **kwargs)
            self.stats['requests_made'] += 1
            
            if hasattr(response, 'content'):
                self.stats['bytes_downloaded'] += len(response.content)
            
            return response
            
        except Exception as e:
            self.stats['requests_failed'] += 1
            
            # Đánh dấu proxy failed nếu có
            if self.proxy_manager and 'proxies' in kwargs:
                proxy_url = kwargs['proxies'].get('http')
                if proxy_url:
                    self.proxy_manager.mark_failed(proxy_url)
            
            raise e
    
    def post(self, url: str, **kwargs) -> requests.Response:
        """POST request với rate limiting và retry"""
        self._handle_rate_limit()
        
        kwargs = self._prepare_request(url, **kwargs)
        
        try:
            response = self.session.post(url, **kwargs)
            self.stats['requests_made'] += 1
            
            if hasattr(response, 'content'):
                self.stats['bytes_downloaded'] += len(response.content)
            
            return response
            
        except Exception as e:
            self.stats['requests_failed'] += 1
            raise e
    
    def download_file(self, url: str, filepath: str, 
                     chunk_size: int = 8192, 
                     resume: bool = True,
                     progress_callback: Optional[Callable] = None) -> bool:
        """
        Download file với resume support
        
        Args:
            url: URL để download
            filepath: Đường dẫn lưu file
            chunk_size: Kích thước chunk
            resume: Có resume download không
            progress_callback: Callback để báo tiến độ
        """
        try:
            # Kiểm tra file đã tồn tại
            resume_header = {}
            if resume and os.path.exists(filepath):
                resume_header['Range'] = f'bytes={os.path.getsize(filepath)}-'
            
            response = self.get(url, headers=resume_header, stream=True)
            response.raise_for_status()
            
            # Tổng kích thước
            total_size = int(response.headers.get('content-length', 0))
            if resume and os.path.exists(filepath):
                total_size += os.path.getsize(filepath)
            
            # Download
            mode = 'ab' if resume and os.path.exists(filepath) else 'wb'
            downloaded = os.path.getsize(filepath) if resume and os.path.exists(filepath) else 0
            
            with open(filepath, mode) as f:
                for chunk in response.iter_content(chunk_size=chunk_size):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        
                        if progress_callback:
                            progress_callback(downloaded, total_size)
            
            return True
            
        except Exception as e:
            logger.error(f"Download failed: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """Lấy thống kê"""
        return self.stats.copy()
    
    def close(self):
        """Đóng session"""
        self.session.close()


# Singleton instance
_http_client = None

def get_http_client(**kwargs) -> HTTPClient:
    """Lấy HTTP client singleton"""
    global _http_client
    if _http_client is None:
        _http_client = HTTPClient(**kwargs)
    return _http_client
