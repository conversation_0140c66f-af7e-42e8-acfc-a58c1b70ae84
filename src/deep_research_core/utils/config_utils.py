#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Config Utils - <PERSON>u<PERSON>n lý cấu hình cho Deep Research Core

Cung cấp:
- Config loader từ JSON/YAML/ENV
- Environment detection
- Config validation
- Dynamic config với hot reload
- Config merging và override
"""

import os
import json
import logging
from typing import Dict, Any, Optional, Union, List
from pathlib import Path
import threading
import time

# Optional imports
try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False

try:
    import jsonschema
    JSONSCHEMA_AVAILABLE = True
except ImportError:
    JSONSCHEMA_AVAILABLE = False

logger = logging.getLogger(__name__)


class ConfigError(Exception):
    """Exception cho config errors"""
    pass


class ConfigValidator:
    """Validator cho config schema"""
    
    def __init__(self, schema: Optional[Dict[str, Any]] = None):
        """
        Args:
            schema: JSON schema để validate config
        """
        self.schema = schema
        self.validator = None
        
        if schema and JSONSCHEMA_AVAILABLE:
            self.validator = jsonschema.Draft7Validator(schema)
    
    def validate(self, config: Dict[str, Any]) -> List[str]:
        """
        Validate config theo schema
        
        Args:
            config: Config cần validate
            
        Returns:
            List các lỗi validation
        """
        errors = []
        
        if not self.validator:
            return errors
        
        try:
            for error in self.validator.iter_errors(config):
                errors.append(f"{'.'.join(str(p) for p in error.path)}: {error.message}")
        except Exception as e:
            errors.append(f"Validation error: {str(e)}")
        
        return errors
    
    def is_valid(self, config: Dict[str, Any]) -> bool:
        """Kiểm tra config có hợp lệ không"""
        return len(self.validate(config)) == 0


class EnvironmentDetector:
    """Phát hiện môi trường runtime"""
    
    @staticmethod
    def get_environment() -> str:
        """
        Phát hiện môi trường hiện tại
        
        Returns:
            Environment name (development, testing, staging, production)
        """
        # Kiểm tra biến môi trường
        env = os.getenv('ENVIRONMENT', '').lower()
        if env in ['development', 'dev']:
            return 'development'
        elif env in ['testing', 'test']:
            return 'testing'
        elif env in ['staging', 'stage']:
            return 'staging'
        elif env in ['production', 'prod']:
            return 'production'
        
        # Kiểm tra các indicator khác
        if os.getenv('DEBUG', '').lower() in ['true', '1', 'yes']:
            return 'development'
        
        if os.getenv('CI', '').lower() in ['true', '1', 'yes']:
            return 'testing'
        
        # Mặc định
        return 'development'
    
    @staticmethod
    def is_development() -> bool:
        """Kiểm tra có phải môi trường development không"""
        return EnvironmentDetector.get_environment() == 'development'
    
    @staticmethod
    def is_production() -> bool:
        """Kiểm tra có phải môi trường production không"""
        return EnvironmentDetector.get_environment() == 'production'
    
    @staticmethod
    def is_testing() -> bool:
        """Kiểm tra có phải môi trường testing không"""
        return EnvironmentDetector.get_environment() == 'testing'


class ConfigLoader:
    """Loader cho config files"""
    
    def __init__(self, 
                 config_dir: str = "config",
                 environment: Optional[str] = None,
                 schema: Optional[Dict[str, Any]] = None,
                 auto_reload: bool = False,
                 reload_interval: int = 60):
        """
        Args:
            config_dir: Thư mục chứa config files
            environment: Environment name (auto-detect nếu None)
            schema: JSON schema để validate
            auto_reload: Có tự động reload config không
            reload_interval: Khoảng thời gian reload (giây)
        """
        self.config_dir = Path(config_dir)
        self.environment = environment or EnvironmentDetector.get_environment()
        self.validator = ConfigValidator(schema)
        self.auto_reload = auto_reload
        self.reload_interval = reload_interval
        
        self._config = {}
        self._file_mtimes = {}
        self._lock = threading.RLock()
        self._last_reload = 0
        
        # Load initial config
        self.reload()
        
        # Start auto-reload thread nếu cần
        if auto_reload:
            self._start_auto_reload()
    
    def _load_json_file(self, filepath: Path) -> Dict[str, Any]:
        """Load JSON config file"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading JSON config {filepath}: {e}")
            return {}
    
    def _load_yaml_file(self, filepath: Path) -> Dict[str, Any]:
        """Load YAML config file"""
        if not YAML_AVAILABLE:
            logger.warning(f"YAML not available, skipping {filepath}")
            return {}
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f) or {}
        except Exception as e:
            logger.error(f"Error loading YAML config {filepath}: {e}")
            return {}
    
    def _load_env_vars(self, prefix: str = "APP_") -> Dict[str, Any]:
        """Load config từ environment variables"""
        config = {}
        
        for key, value in os.environ.items():
            if key.startswith(prefix):
                # Convert APP_DATABASE_HOST -> database.host
                config_key = key[len(prefix):].lower().replace('_', '.')
                
                # Try to parse as JSON first
                try:
                    config[config_key] = json.loads(value)
                except (json.JSONDecodeError, ValueError):
                    # Fallback to string
                    config[config_key] = value
        
        return config
    
    def _merge_configs(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """Merge hai config objects"""
        result = base.copy()
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def _set_nested_value(self, config: Dict[str, Any], key: str, value: Any):
        """Set giá trị nested bằng dot notation"""
        keys = key.split('.')
        current = config
        
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]
        
        current[keys[-1]] = value
    
    def _get_config_files(self) -> List[Path]:
        """Lấy danh sách config files theo thứ tự ưu tiên"""
        files = []
        
        if not self.config_dir.exists():
            return files
        
        # Base config
        for ext in ['.json', '.yaml', '.yml']:
            base_file = self.config_dir / f"config{ext}"
            if base_file.exists():
                files.append(base_file)
        
        # Environment-specific config
        for ext in ['.json', '.yaml', '.yml']:
            env_file = self.config_dir / f"config.{self.environment}{ext}"
            if env_file.exists():
                files.append(env_file)
        
        # Local config (highest priority)
        for ext in ['.json', '.yaml', '.yml']:
            local_file = self.config_dir / f"config.local{ext}"
            if local_file.exists():
                files.append(local_file)
        
        return files
    
    def _check_file_changes(self) -> bool:
        """Kiểm tra có file nào thay đổi không"""
        config_files = self._get_config_files()
        
        for filepath in config_files:
            try:
                mtime = filepath.stat().st_mtime
                if filepath not in self._file_mtimes or self._file_mtimes[filepath] != mtime:
                    return True
            except OSError:
                continue
        
        return False
    
    def reload(self) -> bool:
        """Reload config từ files"""
        with self._lock:
            try:
                config = {}
                config_files = self._get_config_files()
                
                # Load và merge config files
                for filepath in config_files:
                    if filepath.suffix.lower() == '.json':
                        file_config = self._load_json_file(filepath)
                    elif filepath.suffix.lower() in ['.yaml', '.yml']:
                        file_config = self._load_yaml_file(filepath)
                    else:
                        continue
                    
                    config = self._merge_configs(config, file_config)
                    
                    # Update file mtime
                    try:
                        self._file_mtimes[filepath] = filepath.stat().st_mtime
                    except OSError:
                        pass
                
                # Load environment variables
                env_config = self._load_env_vars()
                for key, value in env_config.items():
                    self._set_nested_value(config, key, value)
                
                # Validate config
                if self.validator:
                    errors = self.validator.validate(config)
                    if errors:
                        logger.error(f"Config validation errors: {errors}")
                        raise ConfigError(f"Config validation failed: {errors}")
                
                self._config = config
                self._last_reload = time.time()
                
                logger.info(f"Config reloaded successfully for environment: {self.environment}")
                return True
                
            except Exception as e:
                logger.error(f"Error reloading config: {e}")
                return False
    
    def _start_auto_reload(self):
        """Bắt đầu auto-reload thread"""
        def reload_worker():
            while self.auto_reload:
                try:
                    if time.time() - self._last_reload > self.reload_interval:
                        if self._check_file_changes():
                            self.reload()
                    
                    time.sleep(min(10, self.reload_interval // 6))  # Check every 10s or 1/6 of interval
                except Exception as e:
                    logger.error(f"Auto-reload error: {e}")
                    time.sleep(30)  # Wait longer on error
        
        thread = threading.Thread(target=reload_worker, daemon=True)
        thread.start()
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Lấy config value bằng dot notation
        
        Args:
            key: Config key (e.g., 'database.host')
            default: Giá trị mặc định
        """
        with self._lock:
            keys = key.split('.')
            current = self._config
            
            try:
                for k in keys:
                    current = current[k]
                return current
            except (KeyError, TypeError):
                return default
    
    def set(self, key: str, value: Any):
        """
        Set config value bằng dot notation
        
        Args:
            key: Config key (e.g., 'database.host')
            value: Giá trị mới
        """
        with self._lock:
            self._set_nested_value(self._config, key, value)
    
    def get_all(self) -> Dict[str, Any]:
        """Lấy toàn bộ config"""
        with self._lock:
            return self._config.copy()
    
    def get_environment(self) -> str:
        """Lấy environment hiện tại"""
        return self.environment


# Singleton instance
_config_loader = None

def get_config_loader(**kwargs) -> ConfigLoader:
    """Lấy config loader singleton"""
    global _config_loader
    if _config_loader is None:
        _config_loader = ConfigLoader(**kwargs)
    return _config_loader

def get_config(key: str, default: Any = None) -> Any:
    """Shortcut để lấy config value"""
    return get_config_loader().get(key, default)

def set_config(key: str, value: Any):
    """Shortcut để set config value"""
    get_config_loader().set(key, value)
