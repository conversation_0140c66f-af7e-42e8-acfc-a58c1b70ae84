"""
Mo<PERSON><PERSON> thiết lập và tích hợp tìm kiếm tiếng Việt nâng cao.

Module này giúp tạo các đối tượng WebSearchAgentLocal với đầy đủ tính năng:
- Tì<PERSON> kiếm tiếng Việt
- <PERSON><PERSON><PERSON><PERSON> phục dấu tiếng Việt
- Xế<PERSON> hạng kết quả thông minh
- <PERSON><PERSON>h giá độ tin cậy nguồn thông tin
"""

import logging
import os
import sys
from typing import Optional, Dict, Any

# Thêm thư mục gốc vào sys.path để có thể import các module
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

try:
    # Sử dụng dynamic import để tránh circular dependency
    import importlib
    websearch_module = importlib.import_module('deep_research_core.websearch_agent_local')
    WebSearchAgentLocal = getattr(websearch_module, 'WebSearchAgentLocal')
    WEBSEARCH_AGENT_AVAILABLE = True
except (ImportError, AttributeError):
    WebSearchAgentLocal = None
    WEBSEARCH_AGENT_AVAILABLE = False

try:
    from src.deep_research_core.utils.vietnamese_search_integration import integrate_vietnamese_search
    VIETNAMESE_SEARCH_INTEGRATION_AVAILABLE = True
except ImportError:
    VIETNAMESE_SEARCH_INTEGRATION_AVAILABLE = False

try:
    from src.deep_research_core.utils.vietnamese_ranking import VietnameseResultRanker, customize_ranking
    VIETNAMESE_RANKING_AVAILABLE = True
except ImportError:
    VIETNAMESE_RANKING_AVAILABLE = False

try:
    from src.deep_research_core.credibility.vietnamese_credibility_integration import integrate_credibility_evaluation
    CREDIBILITY_INTEGRATION_AVAILABLE = True
except ImportError:
    CREDIBILITY_INTEGRATION_AVAILABLE = False

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_enhanced_agent(config: Optional[Dict[str, Any]] = None) -> Any:
    """
    Tạo WebSearchAgentLocal với tất cả các tính năng tìm kiếm tiếng Việt nâng cao.
    
    Args:
        config: Cấu hình cho agent
        
    Returns:
        WebSearchAgentLocal: Agent tìm kiếm với tính năng tìm kiếm tiếng Việt nâng cao
    """
    if not WEBSEARCH_AGENT_AVAILABLE:
        logger.error("WebSearchAgentLocal không khả dụng - không thể tạo agent")
        return None
    
    # Khởi tạo cấu hình mặc định nếu không có
    if config is None:
        config = {}
    
    # Đảm bảo cấu hình có các thành phần cần thiết
    if "vietnamese_search" not in config:
        config["vietnamese_search"] = {
            "enable_vietnamese_search": True,
            "vietnamese_search_sources": [
                "coccoc", "wikitiengviet", "baomoi", "google_vietnam", "vnexpress", "zingnews"
            ],
            "optimize_vietnamese_query": True,
            "use_diacritics_handler": True,
            "use_vietnamese_ranking": True,
            "use_advanced_language_detector": True,
            "ranking_config": {
                "content_weight": 0.6,
                "title_weight": 0.3,
                "url_weight": 0.1,
                "recency_boost": True,
                "combine_sources": True
            },
            "diacritics_handler_config": {
                "cache_size": 1000
            }
        }
    
    # Thêm cấu hình đánh giá độ tin cậy nếu chưa có
    if "credibility" not in config:
        config["credibility"] = {
            "enable_credibility_evaluation": True,
            "auto_filter_by_credibility": True,
            "min_credibility_level": "low",
            "filter_before_ranking": False,
            "credibility_config": {
                "enable_domain_heuristics": True,
                "enable_content_analysis": True
            }
        }
    
    # Tạo agent cơ bản
    agent = WebSearchAgentLocal(config)
    
    # Tích hợp tìm kiếm tiếng Việt nếu có
    if VIETNAMESE_SEARCH_INTEGRATION_AVAILABLE:
        Agent = integrate_vietnamese_search(WebSearchAgentLocal)
        agent = Agent(config)
        logger.info("Đã tích hợp tìm kiếm tiếng Việt vào agent")
    else:
        logger.warning("Tích hợp tìm kiếm tiếng Việt không khả dụng")
    
    # Tích hợp đánh giá độ tin cậy nếu có
    if CREDIBILITY_INTEGRATION_AVAILABLE:
        Agent = integrate_credibility_evaluation(agent.__class__)
        # Lưu lại cấu hình hiện tại
        current_config = config
        # Tạo agent mới với cùng cấu hình
        agent = Agent(current_config)
        logger.info("Đã tích hợp đánh giá độ tin cậy vào agent")
    else:
        logger.warning("Tích hợp đánh giá độ tin cậy không khả dụng")
    
    logger.info("Đã tạo WebSearchAgentLocal với tất cả tính năng tìm kiếm tiếng Việt nâng cao")
    
    return agent

def get_enhanced_config(base_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Tạo cấu hình đầy đủ cho agent tìm kiếm tiếng Việt nâng cao.
    
    Args:
        base_config: Cấu hình cơ sở cần mở rộng
        
    Returns:
        Dict: Cấu hình đầy đủ
    """
    # Khởi tạo cấu hình cơ sở nếu không có
    config = base_config.copy() if base_config else {}
    
    # Cấu hình tìm kiếm tiếng Việt
    if "vietnamese_search" not in config:
        config["vietnamese_search"] = {}
    
    vietnamese_config = config["vietnamese_search"]
    
    # Thiết lập các giá trị mặc định nếu chưa có
    vietnamese_config.setdefault("enable_vietnamese_search", True)
    vietnamese_config.setdefault("vietnamese_search_sources", 
        ["coccoc", "wikitiengviet", "baomoi", "google_vietnam", "vnexpress", "zingnews"])
    vietnamese_config.setdefault("optimize_vietnamese_query", True)
    vietnamese_config.setdefault("use_diacritics_handler", True)
    vietnamese_config.setdefault("use_vietnamese_ranking", True)
    vietnamese_config.setdefault("use_advanced_language_detector", True)
    
    # Cấu hình xếp hạng
    if "ranking_config" not in vietnamese_config:
        vietnamese_config["ranking_config"] = {}
    
    ranking_config = vietnamese_config["ranking_config"]
    ranking_config.setdefault("content_weight", 0.6)
    ranking_config.setdefault("title_weight", 0.3)
    ranking_config.setdefault("url_weight", 0.1)
    ranking_config.setdefault("recency_boost", True)
    ranking_config.setdefault("combine_sources", True)
    
    # Cấu hình bộ xử lý dấu
    if "diacritics_handler_config" not in vietnamese_config:
        vietnamese_config["diacritics_handler_config"] = {}
    
    diacritics_config = vietnamese_config["diacritics_handler_config"]
    diacritics_config.setdefault("cache_size", 1000)
    
    # Cấu hình hệ thống cache
    if "cache_config" not in vietnamese_config:
        vietnamese_config["cache_config"] = {}
    
    cache_config = vietnamese_config["cache_config"]
    cache_config.setdefault("cache_dir", ".vietnamese_search_cache")
    cache_config.setdefault("memory_cache_size", 1000)
    cache_config.setdefault("disk_cache_size", 10000)
    
    # Cấu hình đánh giá độ tin cậy
    if "credibility" not in config:
        config["credibility"] = {}
    
    credibility_config = config["credibility"]
    credibility_config.setdefault("enable_credibility_evaluation", True)
    credibility_config.setdefault("auto_filter_by_credibility", True)
    credibility_config.setdefault("min_credibility_level", "low")
    credibility_config.setdefault("filter_before_ranking", False)
    
    if "credibility_config" not in credibility_config:
        credibility_config["credibility_config"] = {}
    
    credibility_core_config = credibility_config["credibility_config"]
    credibility_core_config.setdefault("enable_domain_heuristics", True)
    credibility_core_config.setdefault("enable_content_analysis", True)
    
    return config

def main():
    """
    Hàm chính để chạy script.
    """
    # Tích hợp tìm kiếm tiếng Việt vào WebSearchAgentLocal
    EnhancedWebSearchAgentLocal = create_enhanced_agent()
    
    # Ví dụ sử dụng
    config = get_enhanced_config()
    
    # Truyền config như một tham số thông thường
    agent = EnhancedWebSearchAgentLocal(config) if EnhancedWebSearchAgentLocal else None
    
    if agent:
        logger.info("Vietnamese search integration setup completed successfully")
    else:
        logger.error("Failed to set up Vietnamese search integration")

if __name__ == "__main__":
    main() 