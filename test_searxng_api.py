#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for SearXNG API.
"""

import requests
import json
import time
import sys
import json
import time
import random
import logging
import threading
import subprocess
import urllib.parse
import requests
from typing import Dict, Any, List, Optional, Union, Callable, Set, Type
from datetime import datetime, timedelta
from functools import wraps
import concurrent.futures
import socket
import psutil
import os

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Public SearXNG instances for fallback
PUBLIC_SEARXNG_INSTANCES = [
    "https://search.disroot.org",
    "https://searx.tiekoetter.com", 
    "https://search.privacyguides.net",
    "https://search.mdosch.de",
    "https://search.ononoki.org",
    "https://searx.tuxcloud.net",
    "https://searx.be",
    "https://searx.work",
    "https://search.unlocked.link",
    "https://search.sapti.me",
    "https://search.bus-hit.me",
    "https://searx.fmac.xyz",
    "https://search.leptons.xyz",
    "https://search.rhscz.eu",
    "https://search.neet.works",
    "https://search.cronobox.one",
    "https://searx.thegpm.org",
    "https://searx.dresden.network",
    "https://searx.xyz",
]

# Multiple user agents for rotation
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 11.5; rv:90.0) Gecko/20100101 Firefox/90.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 11_5_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15",
]

# Custom exceptions
class SearXNGError(Exception):
    """Base exception for SearXNG errors."""
    pass

class SearXNGConnectionError(SearXNGError):
    """Exception for connection errors."""
    pass

class SearXNGTimeoutError(SearXNGError):
    """Exception for timeout errors."""
    pass

class SearXNGCaptchaError(SearXNGError):
    """Exception for CAPTCHA detection."""
    pass

class SearXNGRateLimitError(SearXNGError):
    """Exception for rate limiting."""
    pass

# Simple file-based caching
_cache = {}
_cache_timestamps = {}

def _get_cache_key(query: str, num_results: int, language: str, categories: Optional[List[str]], 
                   time_range: Optional[str], safesearch: int) -> str:
    """Generate cache key for search parameters."""
    cats = ",".join(sorted(categories)) if categories else ""
    return f"{query}:{num_results}:{language}:{cats}:{time_range}:{safesearch}"

def _is_cache_valid(key: str, ttl: int = 300) -> bool:
    """Check if cache entry is still valid."""
    if key not in _cache_timestamps:
        return False
    return time.time() - _cache_timestamps[key] < ttl

def _get_cached_result(key: str) -> Optional[Dict[str, Any]]:
    """Get cached result if valid."""
    if _is_cache_valid(key):
        return _cache.get(key)
    return None

def _set_cached_result(key: str, result: Dict[str, Any]) -> None:
    """Cache the result."""
    _cache[key] = result
    _cache_timestamps[key] = time.time()

def retry_with_exponential_backoff(
    max_retries: int = 3,
    initial_backoff: float = 1.0,
    max_backoff: float = 60.0,
    backoff_factor: float = 2.0,
    jitter: bool = True,
    retry_exceptions: Optional[Set[Type[Exception]]] = None
):
    """Decorator for retry with exponential backoff."""
    if retry_exceptions is None:
        retry_exceptions = {
            requests.RequestException,
            SearXNGConnectionError,
            SearXNGTimeoutError,
            ConnectionError,
            TimeoutError
        }
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            retries = 0
            backoff = initial_backoff
            last_exception = None
            
            while retries <= max_retries:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    
                    # Check if this exception should trigger a retry
                    if not any(isinstance(e, exc_type) for exc_type in retry_exceptions):
                        raise
                    
                    if retries == max_retries:
                        raise
                    
                    # Calculate backoff time
                    if jitter:
                        sleep_time = backoff + random.uniform(0, backoff * 0.1)
                    else:
                        sleep_time = backoff
                    
                    logger.warning(f"Retry {retries + 1}/{max_retries + 1} after {sleep_time:.1f}s due to {type(e).__name__}: {e}")
                    time.sleep(sleep_time)
                    
                    # Increase backoff for next retry
                    backoff = min(backoff * backoff_factor, max_backoff)
                    retries += 1
            
            # If we get here, all retries failed
            raise last_exception
        return wrapper
    return decorator

def get_random_user_agent() -> str:
    """Get a random user agent from the pool."""
    return random.choice(USER_AGENTS)

def check_searxng_health(instance_url: str, timeout: int = 5) -> bool:
    """Check if a SearXNG instance is healthy."""
    try:
        # Try health endpoint first
        response = requests.get(f"{instance_url}/healthz", timeout=timeout)
        if response.status_code == 200:
            return True
    except:
        pass
    
    try:
        # Fallback to main page
        response = requests.get(instance_url, timeout=timeout)
        return response.status_code == 200
    except:
        return False

def restart_local_searxng(docker_container_name: str = "searxng") -> bool:
    """Attempt to restart local SearXNG Docker container."""
    try:
        # Check if container exists
        result = subprocess.run(
            ["docker", "ps", "-a", "--filter", f"name={docker_container_name}", "--format", "{{.Names}}"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if docker_container_name in result.stdout:
            # Restart the container
            subprocess.run(["docker", "restart", docker_container_name], timeout=30)
            
            # Wait a bit for it to start
            time.sleep(5)
            
            # Check if it's now healthy
            return check_searxng_health("http://localhost:8080")
        else:
            logger.warning(f"Docker container '{docker_container_name}' not found")
            return False
    except Exception as e:
        logger.error(f"Failed to restart SearXNG container: {e}")
        return False

def get_available_instances(primary_instance: str = "http://localhost:8080", 
                          check_health: bool = True) -> List[str]:
    """Get list of available SearXNG instances, prioritizing local/primary."""
    instances = [primary_instance] + PUBLIC_SEARXNG_INSTANCES
    
    if not check_health:
        return instances
    
    available_instances = []
    
    # Quick health check with threading for speed
    def check_instance(instance):
        if check_searxng_health(instance, timeout=3):
            return instance
        return None
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        future_to_instance = {executor.submit(check_instance, instance): instance 
                            for instance in instances}
        
        for future in concurrent.futures.as_completed(future_to_instance, timeout=15):
            result = future.result()
            if result:
                available_instances.append(result)
    
    return available_instances if available_instances else instances

def search_searxng(
    query: str,
    num_results: int = 10,
    searxng_instance: str = "http://localhost:8080",
    language: str = "en",
    categories: Optional[List[str]] = None,
    time_range: Optional[str] = None,
    safesearch: int = 0,
    use_cache: bool = True,
    cache_ttl: int = 300,
    enable_fallback: bool = True,
    max_fallback_attempts: int = 3,
    auto_restart_local: bool = True,
    rate_limit_delay: float = 1.0,
    verify_ssl: bool = True,
    format_output: bool = True
):
    """
    Enhanced SearXNG search with advanced features:
    - Fallback to multiple public instances
    - Exponential backoff retry logic  
    - Comprehensive error handling with CAPTCHA detection
    - File-based caching with TTL
    - Health checking and auto-restart
    - User agent rotation and rate limiting
    - SSL verification options
    - Optimization for content length and formatting
    
    Args:
        query: The search query
        num_results: Number of results to return (default: 10)
        searxng_instance: Primary SearXNG instance URL (default: "http://localhost:8080")
        language: Language code for search results (default: "en")
        categories: List of categories to search in (e.g., ["general", "news"])
        time_range: Time range for search results (day, week, month, year)
        safesearch: Safe search level (0=off, 1=moderate, 2=strict, default: 0)
        use_cache: Enable caching of results (default: True)
        cache_ttl: Cache time-to-live in seconds (default: 300)
        enable_fallback: Enable fallback to public instances (default: True)
        max_fallback_attempts: Maximum fallback attempts (default: 3)
        auto_restart_local: Attempt to restart local SearXNG if it fails (default: True)
        rate_limit_delay: Delay between requests in seconds (default: 1.0)
        verify_ssl: Verify SSL certificates (default: True)
        format_output: Format output to standardized structure (default: True)
        
    Returns:
        Dictionary containing search results and metadata
    """
    # Check cache first
    cache_key = None
    if use_cache:
        cache_key = _get_cache_key(query, num_results, language, categories, time_range, safesearch)
        cached_result = _get_cached_result(cache_key)
        if cached_result:
            print(f"🎯 Cache hit for query: {query}")
            logger.info(f"Cache hit for query: {query}")
            return cached_result
    
    # Rate limiting
    if rate_limit_delay > 0:
        time.sleep(rate_limit_delay)
    
    print(f"Searching with SearXNG instance: {searxng_instance}")
    print(f"Query: {query}")
    print(f"Language: {language}")
    print(f"Number of results: {num_results}")
    
    # Get available instances for fallback
    available_instances = [searxng_instance]
    if enable_fallback:
        available_instances = get_available_instances(searxng_instance, check_health=True)
        if not available_instances:
            available_instances = [searxng_instance] + PUBLIC_SEARXNG_INSTANCES
    
    last_error = None
    attempts = 0
    
    # Try each available instance
    for instance in available_instances[:max_fallback_attempts + 1]:
        attempts += 1
        
        try:
            result = _perform_search_request(
                query=query,
                num_results=num_results,
                searxng_instance=instance,
                language=language,
                categories=categories,
                time_range=time_range,
                safesearch=safesearch,
                verify_ssl=verify_ssl,
                format_output=format_output
            )
            
            if result["success"]:
                # Cache successful result
                if use_cache and cache_key:
                    _set_cached_result(cache_key, result)
                    print(f"💾 Result cached for future use")
                
                print(f"✅ Search successful with instance: {instance}")
                return result
            else:
                last_error = result.get("error", "Unknown error")
                logger.warning(f"Instance {instance} failed: {last_error}")
                
        except Exception as e:
            last_error = str(e)
            logger.warning(f"Instance {instance} failed with exception: {e}")
            
            # Special handling for local instance
            if instance == searxng_instance and "localhost" in instance and auto_restart_local:
                logger.info("Attempting to restart local SearXNG...")
                if restart_local_searxng():
                    logger.info("Local SearXNG restarted successfully, retrying...")
                    time.sleep(2)  # Give it time to fully start
                    try:
                        result = _perform_search_request(
                            query=query,
                            num_results=num_results,
                            searxng_instance=instance,
                            language=language,
                            categories=categories,
                            time_range=time_range,
                            safesearch=safesearch,
                            verify_ssl=verify_ssl,
                            format_output=format_output
                        )
                        
                        if result["success"]:
                            if use_cache:
                                _set_cached_result(cache_key, result)
                            return result
                    except Exception as retry_e:
                        logger.error(f"Retry after restart failed: {retry_e}")
        
        # Add delay between fallback attempts
        if attempts < len(available_instances):
            time.sleep(min(2.0 * attempts, 10.0))
    
    # All instances failed
    logger.error(f"All {attempts} instances failed. Last error: {last_error}")
    return {
        "success": False,
        "search_method": "api",
        "engine": "searxng",
        "query": query,
        "error": f"All {attempts} instances failed. Last error: {last_error}",
        "results": [],
        "attempts": attempts
    }

@retry_with_exponential_backoff(max_retries=2, initial_backoff=1.0, backoff_factor=2.0)
def _perform_search_request(
    query: str,
    num_results: int,
    searxng_instance: str,
    language: str,
    categories: Optional[List[str]],
    time_range: Optional[str],
    safesearch: int,
    verify_ssl: bool,
    format_output: bool
) -> Dict[str, Any]:
    """Perform the actual search request with retry logic."""
    
    # Ensure the URL has the correct format
    if not searxng_instance.startswith(('http://', 'https://')):
        searxng_instance = 'https://' + searxng_instance
    searxng_instance = searxng_instance.rstrip('/')

    # Set up the headers with random user agent
    headers = {
        "User-Agent": get_random_user_agent(),
        "Accept": "application/json",
        "Accept-Language": f"{language},en-US;q=0.9,en;q=0.8",
        "Referer": "https://www.google.com/",
        "DNT": "1",
        "Connection": "keep-alive",
        "Upgrade-Insecure-Requests": "1"
    }

    # Build the query parameters
    params = {
        "q": query,
        "format": "json",
        "language": language,
        "results": num_results,
        "safesearch": safesearch
    }

    if categories:
        params["categories"] = ",".join(categories) if isinstance(categories, list) else categories

    if time_range:
        params["time_range"] = time_range

    try:
        # Make the request
        print(f"Making request to {searxng_instance}/search with params: {params}")
        response = requests.get(
            f"{searxng_instance}/search",
            params=params,
            headers=headers,
            timeout=30,
            verify=verify_ssl
        )
        
        print(f"Response status code: {response.status_code}")
        
        # Handle rate limiting
        if response.status_code == 429:
            retry_after = response.headers.get('Retry-After', '60')
            raise SearXNGRateLimitError(f"Rate limited. Retry after {retry_after} seconds")
        
        # Check if the request was successful
        if response.status_code == 200:
            return _process_response(response, query, searxng_instance, format_output)
        else:
            error_msg = f"HTTP error: {response.status_code}"
            if response.status_code in [502, 503, 504]:
                raise SearXNGConnectionError(error_msg)
            elif response.status_code == 408:
                raise SearXNGTimeoutError(error_msg)
            else:
                return {
                    "success": False,
                    "search_method": "api",
                    "engine": "searxng",
                    "query": query,
                    "error": error_msg,
                    "results": []
                }
                
    except requests.exceptions.Timeout:
        raise SearXNGTimeoutError("Request timed out")
    except requests.exceptions.ConnectionError as e:
        raise SearXNGConnectionError(f"Connection error: {str(e)}")
    except requests.RequestException as e:
        raise SearXNGConnectionError(f"Request error: {str(e)}")

def _process_response(response: requests.Response, query: str, instance: str, format_output: bool) -> Dict[str, Any]:
    """Process the SearXNG response and handle various edge cases."""
    
    try:
        # Check content type
        content_type = response.headers.get('Content-Type', '')
        
        # Handle HTML response (potential CAPTCHA)
        if 'text/html' in content_type:
            logger.warning(f"SearXNG returned HTML instead of JSON from {instance}")
            
            # Check for CAPTCHA indicators
            html_content = response.text.lower()
            captcha_indicators = ['captcha', 'robot', 'verification', 'prove you are human']
            
            if any(indicator in html_content for indicator in captcha_indicators):
                raise SearXNGCaptchaError("CAPTCHA detected in SearXNG response")
            
            # Try to parse as JSON anyway (some instances return wrong content-type)
            try:
                data = response.json()
            except json.JSONDecodeError:
                return {
                    "success": False,
                    "search_method": "api",
                    "engine": "searxng",
                    "query": query,
                    "error": "Received HTML instead of JSON (possible CAPTCHA)",
                    "results": []
                }
        else:
            # Normal JSON response
            data = response.json()
        
        # Check for empty results
        results = data.get("results", [])
        if not results:
            logger.warning(f"SearXNG returned empty results for query: {query}")
            return {
                "success": False,
                "search_method": "api",
                "engine": "searxng",
                "query": query,
                "error": "No results found",
                "results": []
            }
        
        # Format results if requested
        if format_output:
            formatted_results = []
            for result in results:
                # Optimize content length for LLM processing
                content = result.get("content", "")
                if len(content) > 500:
                    content = content[:497] + "..."
                
                formatted_result = {
                    "title": result.get("title", "").strip(),
                    "url": result.get("url", "").strip(),
                    "content": content.strip(),
                    "engine": "searxng",
                    "source": instance,
                    "published_date": result.get("publishedDate"),
                    "img_src": result.get("img_src"),
                    "thumbnail": result.get("thumbnail")
                }
                # Remove None values
                formatted_result = {k: v for k, v in formatted_result.items() if v is not None}
                formatted_results.append(formatted_result)
            
            print(f"Response contains {len(formatted_results)} results")
            
            # Print first result if available
            if formatted_results:
                first_result = formatted_results[0]
                print("\nFirst result:")
                print(f"Title: {first_result.get('title', 'N/A')}")
                print(f"URL: {first_result.get('url', 'N/A')}")
                print(f"Content: {first_result.get('content', 'N/A')[:100]}...")
            
            return {
                "success": True,
                "search_method": "api",
                "engine": "searxng",
                "query": query,
                "results": formatted_results,
                "count": len(formatted_results),
                "instance_used": instance,
                "response_time": response.elapsed.total_seconds()
            }
        else:
            # Return raw data
            return {
                "success": True,
                "search_method": "api", 
                "engine": "searxng",
                "query": query,
                "raw_data": data,
                "count": len(results),
                "instance_used": instance,
                "response_time": response.elapsed.total_seconds()
            }
            
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse JSON response: {e}")
        return {
            "success": False,
            "search_method": "api",
            "engine": "searxng", 
            "query": query,
            "error": f"Failed to parse JSON response: {str(e)}",
            "results": []
        }

def search_with_multiple_instances(
    query: str,
    num_results: int = 10,
    language: str = "en",
    categories: Optional[List[str]] = None,
    time_range: Optional[str] = None,
    safesearch: int = 0,
    max_instances: int = 3
) -> Dict[str, Any]:
    """
    Search using multiple SearXNG instances in parallel and return the best result.
    
    Args:
        query: Search query
        num_results: Number of results to return
        language: Language code
        categories: List of categories
        time_range: Time range filter
        safesearch: Safe search level
        max_instances: Maximum number of instances to try in parallel
        
    Returns:
        Dictionary with search results
    """
    available_instances = get_available_instances(check_health=True)[:max_instances]
    
    if not available_instances:
        return search_searxng(query, num_results, language=language, categories=categories,
                             time_range=time_range, safesearch=safesearch)
    
    results = []
    
    def search_instance(instance):
        try:
            return search_searxng(
                query=query,
                num_results=num_results,
                searxng_instance=instance,
                language=language,
                categories=categories,
                time_range=time_range,
                safesearch=safesearch,
                enable_fallback=False,
                use_cache=False
            )
        except Exception as e:
            logger.error(f"Search failed for instance {instance}: {e}")
            return {"success": False, "error": str(e), "results": []}
    
    # Search in parallel
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_instances) as executor:
        future_to_instance = {
            executor.submit(search_instance, instance): instance 
            for instance in available_instances
        }
        
        for future in concurrent.futures.as_completed(future_to_instance, timeout=30):
            try:
                result = future.result()
                if result.get("success") and result.get("results"):
                    results.append(result)
            except Exception as e:
                logger.error(f"Parallel search failed: {e}")
    
    if not results:
        return {
            "success": False,
            "error": "All parallel searches failed",
            "results": []
        }
    
    # Return the result with the most results
    best_result = max(results, key=lambda x: len(x.get("results", [])))
    best_result["search_method"] = "parallel"
    best_result["instances_tried"] = len(available_instances)
    best_result["successful_instances"] = len(results)
    
    return best_result

def get_searxng_stats() -> Dict[str, Any]:
    """Get statistics about SearXNG instances and cache."""
    available_instances = get_available_instances(check_health=True)
    
    return {
        "cache_size": len(_cache),
        "available_instances": len(available_instances),
        "total_known_instances": len(PUBLIC_SEARXNG_INSTANCES) + 1,  # +1 for localhost
        "healthy_instances": available_instances,
        "cache_entries": list(_cache.keys())[:10]  # Show first 10 cache keys
    }

def clear_cache():
    """Clear the search cache."""
    global _cache, _cache_timestamps
    _cache.clear()
    _cache_timestamps.clear()
    logger.info("Search cache cleared")

def batch_search(
    queries: List[str],
    num_results: int = 10,
    language: str = "en",
    delay_between_searches: float = 2.0,
    **kwargs
) -> List[Dict[str, Any]]:
    """
    Perform batch searches with rate limiting.
    
    Args:
        queries: List of search queries
        num_results: Number of results per query
        language: Language code
        delay_between_searches: Delay between searches in seconds
        **kwargs: Additional arguments for search_searxng
        
    Returns:
        List of search results
    """
    results = []
    
    for i, query in enumerate(queries):
        logger.info(f"Batch search {i+1}/{len(queries)}: {query}")
        
        result = search_searxng(
            query=query,
            num_results=num_results,
            language=language,
            **kwargs
        )
        
        results.append(result)
        
        # Add delay between searches except for the last one
        if i < len(queries) - 1:
            time.sleep(delay_between_searches)
    
    return results

def main():
    """
    Enhanced main function demonstrating all SearXNG features.
    """
    import argparse
    
    parser = argparse.ArgumentParser(description="Enhanced SearXNG Search Tool")
    parser.add_argument("query", nargs="?", default="Python programming", 
                       help="Search query (default: 'Python programming')")
    parser.add_argument("--num-results", "-n", type=int, default=5,
                       help="Number of results to return (default: 5)")
    parser.add_argument("--language", "-l", default="en",
                       help="Language code (default: en)")
    parser.add_argument("--instance", "-i", default="http://localhost:8080",
                       help="SearXNG instance URL (default: http://localhost:8080)")
    parser.add_argument("--categories", "-c", nargs="*",
                       help="Search categories (e.g., general news)")
    parser.add_argument("--time-range", "-t",
                       help="Time range (day, week, month, year)")
    parser.add_argument("--safesearch", "-s", type=int, choices=[0, 1, 2], default=0,
                       help="Safe search level: 0=off, 1=moderate, 2=strict (default: 0)")
    parser.add_argument("--no-cache", action="store_true",
                       help="Disable caching")
    parser.add_argument("--no-fallback", action="store_true",
                       help="Disable fallback to public instances")
    parser.add_argument("--parallel", "-p", action="store_true",
                       help="Use parallel search with multiple instances")
    parser.add_argument("--batch", "-b", nargs="*",
                       help="Perform batch search with multiple queries")
    parser.add_argument("--stats", action="store_true",
                       help="Show SearXNG statistics")
    parser.add_argument("--clear-cache", action="store_true",
                       help="Clear search cache")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="Enable verbose logging")
    
    args = parser.parse_args()
    
    # Configure logging
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.setLevel(logging.DEBUG)
    
    print("🔍 Enhanced SearXNG Search Tool")
    print("=" * 50)
    
    # Handle special commands
    if args.clear_cache:
        clear_cache()
        print("✅ Cache cleared")
        return
    
    if args.stats:
        stats = get_searxng_stats()
        print("📊 SearXNG Statistics:")
        print(f"   Cache size: {stats['cache_size']} entries")
        print(f"   Available instances: {stats['available_instances']}/{stats['total_known_instances']}")
        print(f"   Healthy instances: {len(stats['healthy_instances'])}")
        if stats['healthy_instances']:
            print("   🟢 Healthy instances:")
            for instance in stats['healthy_instances'][:5]:  # Show first 5
                print(f"      - {instance}")
        return
    
    # Batch search mode
    if args.batch:
        print(f"🔄 Batch search mode: {len(args.batch)} queries")
        results = batch_search(
            queries=args.batch,
            num_results=args.num_results,
            language=args.language,
            categories=args.categories,
            time_range=args.time_range,
            safesearch=args.safesearch,
            use_cache=not args.no_cache,
            enable_fallback=not args.no_fallback
        )
        
        print(f"\n📋 Batch Results Summary:")
        for i, result in enumerate(results):
            status = "✅" if result.get("success") else "❌"
            count = result.get("count", 0)
            query = args.batch[i]
            print(f"   {status} Query {i+1}: '{query}' - {count} results")
        return
    
    # Parallel search mode
    if args.parallel:
        print(f"⚡ Parallel search mode for: '{args.query}'")
        result = search_with_multiple_instances(
            query=args.query,
            num_results=args.num_results,
            language=args.language,
            categories=args.categories,
            time_range=args.time_range,
            safesearch=args.safesearch
        )
    else:
        # Standard search mode
        print(f"🔍 Searching for: '{args.query}'")
        result = search_searxng(
            query=args.query,
            num_results=args.num_results,
            searxng_instance=args.instance,
            language=args.language,
            categories=args.categories,
            time_range=args.time_range,
            safesearch=args.safesearch,
            use_cache=not args.no_cache,
            enable_fallback=not args.no_fallback
        )
    
    # Display results
    print("\n" + "=" * 50)
    print("📊 Search Results Summary:")
    print("=" * 50)
    
    success = result.get('success', False)
    status_icon = "✅" if success else "❌"
    print(f"{status_icon} Success: {success}")
    print(f"🔢 Number of results: {result.get('count', 0)}")
    
    if result.get('error'):
        print(f"❌ Error: {result.get('error')}")
    
    if result.get('instance_used'):
        print(f"🌐 Instance used: {result.get('instance_used')}")
    
    if result.get('response_time'):
        print(f"⏱️ Response time: {result.get('response_time'):.2f}s")
    
    if result.get('search_method') == 'parallel':
        print(f"⚡ Parallel search: {result.get('successful_instances', 0)}/{result.get('instances_tried', 0)} instances succeeded")
    
    if result.get('attempts'):
        print(f"🔄 Fallback attempts: {result.get('attempts')}")
    
    # Display actual results
    if success and result.get('results'):
        print(f"\n📝 Top {min(3, len(result['results']))} Results:")
        print("-" * 50)
        
        for i, res in enumerate(result['results'][:3], 1):
            print(f"\n{i}. {res.get('title', 'No Title')}")
            print(f"   🔗 {res.get('url', 'No URL')}")
            content = res.get('content', 'No content available')
            if len(content) > 150:
                content = content[:147] + "..."
            print(f"   📄 {content}")
    
    print("\n" + "=" * 50)
    print("🎉 Search completed!")

if __name__ == "__main__":
    main()
