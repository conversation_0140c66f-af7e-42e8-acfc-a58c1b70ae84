#!/usr/bin/env python3
"""
Quick check cho SearXNG local.
"""

import requests

def quick_check():
    """Quick check SearXNG local."""
    print("🔍 Quick SearXNG Local Check")
    print("=" * 30)
    
    local_urls = [
        "http://localhost:8080",
        "http://localhost:4000",
        "http://localhost:8888"
    ]
    
    for url in local_urls:
        try:
            response = requests.get(url, timeout=2)
            if response.status_code == 200:
                print(f"✅ {url}: Working")
                return True
            else:
                print(f"❌ {url}: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ {url}: {str(e)[:50]}")
    
    print("\n⚠️  No local SearXNG found")
    print("📝 To setup SearXNG local:")
    print("   docker run -d -p 8080:8080 searxng/searxng")
    
    return False

if __name__ == "__main__":
    quick_check()
