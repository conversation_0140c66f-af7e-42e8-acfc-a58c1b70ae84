#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test cho FileProcessor đã được hoàn thiện
"""

import sys
import os
import tempfile
import logging

# Thêm đường dẫn để import
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_file_processor():
    """Test các tính năng của FileProcessor"""
    print("🚀 TESTING FILEPROCESSOR")
    print("=" * 50)
    
    try:
        from src.deep_research_core.utils.shared.file_processor import FileProcessor, process_file
        print("✅ Import FileProcessor thành công!")
        
        # Test 1: Tạo file text đơn giản
        print("\n🔍 Test 1: Xử lý file TXT")
        test_text = "Đây là một file text tiếng Việt.\nDòng thứ hai với nội dung khác."
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
            f.write(test_text)
            txt_file = f.name
        
        try:
            with FileProcessor(file_path=txt_file, verbose=True) as processor:
                text = processor.extract_text()
                metadata = processor.extract_metadata()
                
                print(f"  ✓ Text extracted: {len(text)} characters")
                print(f"  ✓ File type: {metadata.get('file_type')}")
                print(f"  ✓ File size: {metadata.get('file_size')} bytes")
                print(f"  ✓ MD5 hash: {metadata.get('md5_hash', 'N/A')[:16]}...")
                
        finally:
            os.unlink(txt_file)
        
        # Test 2: Tạo file HTML đơn giản
        print("\n🔍 Test 2: Xử lý file HTML")
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Test HTML File</title>
            <meta name="description" content="This is a test HTML file">
        </head>
        <body>
            <h1>Tiêu đề chính</h1>
            <p>Đây là một đoạn văn bản tiếng Việt.</p>
            <a href="https://example.com">Link test</a>
            <img src="test.jpg" alt="Test image">
        </body>
        </html>
        """
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
            f.write(html_content)
            html_file = f.name
        
        try:
            with FileProcessor(file_path=html_file, verbose=True) as processor:
                text = processor.extract_text()
                metadata = processor.extract_metadata()
                
                print(f"  ✓ Text extracted: {len(text)} characters")
                print(f"  ✓ Title: {metadata.get('title', 'N/A')}")
                print(f"  ✓ Links count: {metadata.get('links_count', 0)}")
                print(f"  ✓ Images count: {metadata.get('images_count', 0)}")
                
        finally:
            os.unlink(html_file)
        
        # Test 3: Tạo file CSV đơn giản
        print("\n🔍 Test 3: Xử lý file CSV")
        csv_content = """Name,Age,City
John Doe,30,New York
Jane Smith,25,Los Angeles
Nguyễn Văn A,35,Hà Nội"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
            f.write(csv_content)
            csv_file = f.name
        
        try:
            with FileProcessor(file_path=csv_file, verbose=True) as processor:
                text = processor.extract_text()
                metadata = processor.extract_metadata()
                
                print(f"  ✓ Text extracted: {len(text)} characters")
                print(f"  ✓ File type: {metadata.get('file_type')}")
                print(f"  ✓ Contains Vietnamese: {'Nguyễn' in text}")
                
        finally:
            os.unlink(csv_file)
        
        # Test 4: Test với nội dung binary
        print("\n🔍 Test 4: Xử lý từ binary content")
        binary_content = test_text.encode('utf-8')
        
        processor = FileProcessor(file_content=binary_content, file_type='txt', verbose=True)
        text = processor.extract_text()
        metadata = processor.extract_metadata()
        processor.cleanup()
        
        print(f"  ✓ Text from binary: {len(text)} characters")
        print(f"  ✓ File type detected: {metadata.get('file_type')}")
        
        # Test 5: Test hàm tiện ích process_file
        print("\n🔍 Test 5: Hàm process_file")
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
            f.write("Test content for process_file function")
            test_file = f.name
        
        try:
            result = process_file(test_file, verbose=True)
            
            print(f"  ✓ Success: {result['success']}")
            print(f"  ✓ File type: {result['file_type']}")
            print(f"  ✓ Text length: {len(result['text']) if result['text'] else 0}")
            print(f"  ✓ Has metadata: {'metadata' in result and result['metadata'] is not None}")
            
        finally:
            os.unlink(test_file)
        
        # Test 6: Test error handling
        print("\n🔍 Test 6: Error handling")
        
        try:
            # Test với file không tồn tại
            processor = FileProcessor(file_path="/nonexistent/file.txt")
            print("  ✗ Should have raised FileNotFoundError")
        except FileNotFoundError:
            print("  ✓ FileNotFoundError handled correctly")
        except Exception as e:
            print(f"  ✓ Exception handled: {type(e).__name__}")
        
        # Test với file type không hỗ trợ
        try:
            processor = FileProcessor(file_content=b"test", file_type="unsupported_type")
            if not processor.is_supported_file_type():
                print("  ✓ Unsupported file type detected correctly")
        except Exception as e:
            print(f"  ✓ Unsupported file type handled: {type(e).__name__}")
        
        print("\n🎉 TẤT CẢ TESTS FILEPROCESSOR HOÀN THÀNH THÀNH CÔNG!")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Thiết lập logging
    logging.basicConfig(level=logging.INFO, 
                       format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    success = test_file_processor()
    print(f"\n📊 Kết quả: {'SUCCESS' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
