# 📚 WebSearchAgentLocalMerged API Documentation

## 🔍 Overview

WebSearchAgentLocalMerged là một enterprise-grade web search agent với khả năng tìm kiếm thông qua nhiều search engines, caching thông minh, và xử lý đa ngôn ngữ.

## 🏗️ Class Definition

```python
class WebSearchAgentLocalMerged:
    """
    Advanced web search agent với real search functionality và performance optimization.
    """
```

## 🚀 Constructor

### `__init__(self, **kwargs)`

Khởi tạo WebSearchAgentLocalMerged instance.

**Parameters:**
- `verbose` (bool, optional): Enable verbose logging. Default: False
- `timeout` (int, optional): Request timeout in seconds. Default: 30
- `max_retries` (int, optional): Maximum retry attempts. Default: 3
- `use_cache` (bool, optional): Enable smart caching. Default: True
- `cache_ttl` (int, optional): Cache TTL in seconds. Default: 3600

**Example:**
```python
agent = WebSearchAgentLocalMerged(
    verbose=True,
    timeout=30,
    use_cache=True,
    cache_ttl=3600
)
```

## 🔍 Core Search Methods

### `search(query, **kwargs)`

Main search method với comprehensive options.

**Parameters:**
- `query` (str): Search query
- `num_results` (int, optional): Number of results. Default: 10
- `get_content` (bool, optional): Extract full content. Default: False
- `evaluate_question` (bool, optional): Evaluate question complexity. Default: False
- `evaluate_answer` (bool, optional): Evaluate answer quality. Default: False
- `use_deep_research` (bool, optional): Use deep research mode. Default: False

**Returns:**
- `Dict[str, Any]`: Search results với metadata

**Example:**
```python
results = agent.search(
    query="Python programming tutorial",
    num_results=15,
    get_content=True,
    evaluate_question=True
)
```

### `_perform_real_search(query, num_results=10)`

Thực hiện tìm kiếm thật với các search engines.

**Parameters:**
- `query` (str): Truy vấn tìm kiếm
- `num_results` (int, optional): Số lượng kết quả mong muốn. Default: 10

**Returns:**
- `List[Dict[str, Any]]`: Danh sách kết quả tìm kiếm với các trường:
  - `title` (str): Tiêu đề kết quả
  - `url` (str): URL của kết quả
  - `snippet` (str): Đoạn trích nội dung
  - `source` (str): Nguồn search engine
  - `score` (float): Điểm số relevance

## 🌐 Search Engine Methods

### `_search_with_searxng(query, num_results)`

Tìm kiếm với SearXNG (ưu tiên local instances).

**Parameters:**
- `query` (str): Search query
- `num_results` (int): Number of results

**Returns:**
- `List[Dict[str, Any]]`: SearXNG search results

### `_search_with_duckduckgo(query, num_results)`

Tìm kiếm với DuckDuckGo (không cần API key).

**Parameters:**
- `query` (str): Search query
- `num_results` (int): Number of results

**Returns:**
- `List[Dict[str, Any]]`: DuckDuckGo search results

### `_search_with_bing(query, num_results)`

Tìm kiếm với Bing Search API (cần API key).

**Parameters:**
- `query` (str): Search query
- `num_results` (int): Number of results

**Returns:**
- `List[Dict[str, Any]]`: Bing search results

### `_search_with_google(query, num_results)`

Tìm kiếm với Google Custom Search API (cần API key).

**Parameters:**
- `query` (str): Search query
- `num_results` (int): Number of results

**Returns:**
- `List[Dict[str, Any]]`: Google search results

## 🧠 Smart Caching Methods

### `get_cache_stats()`

Lấy thống kê cache.

**Returns:**
- `Dict[str, Any]`: Cache statistics including hits, misses, size

### `clear_cache()`

Xóa toàn bộ cache.

**Returns:**
- `bool`: True if successful

### `_get_cache_key(query, **kwargs)`

Tạo cache key cho query.

**Parameters:**
- `query` (str): Search query
- `**kwargs`: Additional parameters

**Returns:**
- `str`: Unique cache key

## 🌍 Multi-language Methods

### `detect_content_language(content)`

Phát hiện ngôn ngữ của nội dung.

**Parameters:**
- `content` (str): Text content

**Returns:**
- `str`: Language code (e.g., 'vi', 'en')

### `normalize_content_language(content, target_language)`

Chuẩn hóa nội dung theo ngôn ngữ.

**Parameters:**
- `content` (str): Text content
- `target_language` (str): Target language code

**Returns:**
- `str`: Normalized content

### `extract_keywords_multilingual(content, language)`

Trích xuất keywords đa ngôn ngữ.

**Parameters:**
- `content` (str): Text content
- `language` (str): Language code

**Returns:**
- `List[str]`: Extracted keywords

## 🔧 Query Optimization Methods

### `optimize_query(query)`

Tối ưu hóa search query.

**Parameters:**
- `query` (str): Original query

**Returns:**
- `str`: Optimized query

### `generate_alternative_queries(query, num_alternatives)`

Tạo các truy vấn thay thế.

**Parameters:**
- `query` (str): Original query
- `num_alternatives` (int): Number of alternatives

**Returns:**
- `List[str]`: Alternative queries

### `detect_query_intent(query)`

Phát hiện ý định của query.

**Parameters:**
- `query` (str): Search query

**Returns:**
- `Dict[str, Any]`: Intent analysis

## 🕷️ Advanced Crawling Methods

### `crawl_with_javascript_support(url, **kwargs)`

Crawl websites với JavaScript support.

**Parameters:**
- `url` (str): URL to crawl
- `wait_for_selector` (str, optional): CSS selector to wait for
- `timeout` (int, optional): Timeout in seconds

**Returns:**
- `Dict[str, Any]`: Crawled content

### `crawl_spa_website(url, **kwargs)`

Crawl Single Page Applications.

**Parameters:**
- `url` (str): SPA URL
- `wait_time` (int, optional): Wait time for content loading

**Returns:**
- `Dict[str, Any]`: SPA content

### `crawl_with_infinite_scroll(url, **kwargs)`

Crawl pages với infinite scroll.

**Parameters:**
- `url` (str): URL with infinite scroll
- `max_scrolls` (int, optional): Maximum scroll attempts

**Returns:**
- `Dict[str, Any]`: All scrolled content

## ⚡ Performance Methods

### `get_performance_stats()`

Lấy thống kê hiệu suất.

**Returns:**
- `Dict[str, Any]`: Performance metrics including:
  - `uptime_seconds` (float): Agent uptime
  - `total_requests` (int): Total requests made
  - `successful_requests` (int): Successful requests
  - `success_rate` (float): Success rate percentage
  - `cache_stats` (Dict): Cache statistics
  - `memory_usage` (Dict): Memory usage info

### `optimize_performance(**kwargs)`

Tối ưu hóa hiệu suất agent.

**Parameters:**
- `enable_compression` (bool, optional): Enable response compression. Default: True
- `enable_keep_alive` (bool, optional): Enable connection keep-alive. Default: True
- `max_pool_connections` (int, optional): Maximum pool connections. Default: 10

### `batch_process_urls(urls, **kwargs)`

Xử lý URLs theo batch.

**Parameters:**
- `urls` (List[str]): List of URLs to process
- `batch_size` (int, optional): Batch size. Default: 5
- `delay_between_batches` (float, optional): Delay between batches. Default: 1.0

**Returns:**
- `List[Dict[str, Any]]`: Batch processing results

### `adaptive_timeout(url, base_timeout=None)`

Tính toán timeout thích ứng.

**Parameters:**
- `url` (str): URL to process
- `base_timeout` (int, optional): Base timeout

**Returns:**
- `int`: Adjusted timeout

## 🔧 Utility Methods

### `cleanup_resources()`

Dọn dẹp resources và connections.

**Returns:**
- `bool`: True if successful

### `_check_searxng_health(searxng_url, timeout=3)`

Kiểm tra SearXNG instance health.

**Parameters:**
- `searxng_url` (str): SearXNG instance URL
- `timeout` (int, optional): Health check timeout. Default: 3

**Returns:**
- `bool`: True if healthy, False otherwise

## 🚨 Error Handling

### Common Exceptions

- `ConnectionError`: Network connection issues
- `TimeoutError`: Request timeout
- `ValueError`: Invalid parameters
- `RuntimeError`: Search engine failures

### Error Response Format

```python
{
    'success': False,
    'error': 'Error description',
    'error_type': 'ErrorType',
    'results': []
}
```

## 🔧 Configuration

### Environment Variables

```bash
# SearXNG Configuration
export SEARXNG_LOCAL_URL="http://localhost:8080"

# API Keys (Optional)
export BING_SEARCH_API_KEY="your_bing_api_key"
export GOOGLE_SEARCH_API_KEY="your_google_api_key"
export GOOGLE_CSE_ID="your_google_cse_id"
```

### Default Settings

```python
DEFAULT_SETTINGS = {
    'timeout': 30,
    'max_retries': 3,
    'cache_ttl': 3600,
    'max_results': 10,
    'user_agent': 'WebSearchAgentLocalMerged/1.0'
}
```

## 📊 Response Format

### Standard Search Response

```python
{
    'success': True,
    'results': [
        {
            'title': 'Result title',
            'url': 'https://example.com',
            'snippet': 'Content snippet',
            'source': 'searxng',
            'score': 0.95,
            'published_date': '2024-01-01',
            'credibility_score': 0.8
        }
    ],
    'metadata': {
        'search_time': 1.234,
        'total_results': 150,
        'sources_used': ['searxng', 'duckduckgo'],
        'from_cache': False,
        'query_optimized': True
    },
    'simple_answer': 'Summarized answer...',
    'question_evaluation': {
        'complexity_score': 0.7,
        'intent': 'informational'
    },
    'answer_evaluation': {
        'quality_score': 0.85,
        'completeness': 0.9
    }
}
```

## 🎯 Best Practices

1. **Always handle exceptions** when calling search methods
2. **Use caching** for repeated queries to improve performance
3. **Set appropriate timeouts** based on your use case
4. **Monitor performance stats** for optimization opportunities
5. **Clean up resources** when done with the agent
6. **Use SearXNG local** for best performance and privacy
