#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test toàn bộ Utils Modules đã được tạo
"""

import sys
import os
import tempfile
import time
import json

# Thêm đường dẫn để import
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_cache_utils():
    """Test Cache Utils"""
    print("\n🔍 Testing Cache Utils")
    
    try:
        from src.deep_research_core.utils.cache_utils import Cache, get_cache, create_cache_key
        
        # Test basic cache
        cache = Cache(default_ttl=5, max_size=3)
        
        # Test set/get
        cache.set("key1", "value1")
        cache.set("key2", "value2", ttl=10)
        
        assert cache.get("key1") == "value1"
        assert cache.get("key2") == "value2"
        assert cache.get("nonexistent") is None
        
        # Test TTL
        cache.set("short_ttl", "value", ttl=1)
        time.sleep(1.1)
        assert cache.get("short_ttl") is None
        
        # Test cache key creation
        key = create_cache_key("search", "python", limit=10)
        assert key.startswith("search:")
        
        # Test stats
        stats = cache.get_stats()
        assert "hits" in stats
        assert "misses" in stats
        
        print("  ✅ Cache Utils: PASSED")
        return True
        
    except Exception as e:
        print(f"  ❌ Cache Utils: FAILED - {e}")
        return False

def test_network_utils():
    """Test Network Utils"""
    print("\n🔍 Testing Network Utils")
    
    try:
        from src.deep_research_core.utils.network_utils import HTTPClient, RateLimiter, UserAgentRotator
        
        # Test Rate Limiter
        limiter = RateLimiter(max_requests=2, time_window=5)
        
        assert limiter.can_proceed() == True
        limiter.add_request()
        assert limiter.can_proceed() == True
        limiter.add_request()
        assert limiter.can_proceed() == False
        
        # Test User Agent Rotator
        ua_rotator = UserAgentRotator()
        ua1 = ua_rotator.get_user_agent()
        ua2 = ua_rotator.get_user_agent()
        
        assert len(ua1) > 0
        assert len(ua2) > 0
        
        # Test domain sticky
        ua_domain = ua_rotator.get_user_agent("example.com")
        ua_domain2 = ua_rotator.get_user_agent("example.com")
        assert ua_domain == ua_domain2
        
        # Test HTTP Client (without actual requests)
        try:
            client = HTTPClient(rate_limit=5, rate_window=60)
            stats = client.get_stats()
            assert "requests_made" in stats
            client.close()
        except ImportError:
            print("  ⚠️ requests library not available, skipping HTTP client test")
        
        print("  ✅ Network Utils: PASSED")
        return True
        
    except Exception as e:
        print(f"  ❌ Network Utils: FAILED - {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_utils():
    """Test Config Utils"""
    print("\n🔍 Testing Config Utils")
    
    try:
        from src.deep_research_core.utils.config_utils import ConfigLoader, EnvironmentDetector
        
        # Test Environment Detector
        env = EnvironmentDetector.get_environment()
        assert env in ['development', 'testing', 'staging', 'production']
        
        is_dev = EnvironmentDetector.is_development()
        is_prod = EnvironmentDetector.is_production()
        assert isinstance(is_dev, bool)
        assert isinstance(is_prod, bool)
        
        # Test Config Loader với temp directory
        with tempfile.TemporaryDirectory() as temp_dir:
            config_dir = os.path.join(temp_dir, "config")
            os.makedirs(config_dir)
            
            # Tạo config file
            config_data = {
                "app": {
                    "name": "test_app",
                    "version": "1.0.0"
                },
                "database": {
                    "host": "localhost",
                    "port": 5432
                }
            }
            
            config_file = os.path.join(config_dir, "config.json")
            with open(config_file, 'w') as f:
                json.dump(config_data, f)
            
            # Test loader
            loader = ConfigLoader(config_dir=config_dir)
            
            assert loader.get("app.name") == "test_app"
            assert loader.get("app.version") == "1.0.0"
            assert loader.get("database.host") == "localhost"
            assert loader.get("database.port") == 5432
            assert loader.get("nonexistent.key", "default") == "default"
            
            # Test set
            loader.set("app.debug", True)
            assert loader.get("app.debug") == True
            
            # Test get_all
            all_config = loader.get_all()
            assert "app" in all_config
            assert "database" in all_config
        
        print("  ✅ Config Utils: PASSED")
        return True
        
    except Exception as e:
        print(f"  ❌ Config Utils: FAILED - {e}")
        import traceback
        traceback.print_exc()
        return False

def test_logging_utils():
    """Test Logging Utils"""
    print("\n🔍 Testing Logging Utils")
    
    try:
        from src.deep_research_core.utils.logging_utils import (
            LoggingManager, PerformanceLogger, ErrorTracker, 
            performance_timer, log_errors
        )
        
        # Test với temp directory
        with tempfile.TemporaryDirectory() as temp_dir:
            # Test Logging Manager
            manager = LoggingManager(
                log_dir=temp_dir,
                log_level="INFO",
                console_output=False  # Tắt console để test clean
            )
            
            # Test get logger
            logger = manager.get_logger("test_logger")
            
            # Test context
            logger.set_context(user_id=123, session_id="abc")
            logger.info("Test message with context")
            
            context = logger.get_context()
            assert context["user_id"] == 123
            assert context["session_id"] == "abc"
            
            logger.clear_context()
            assert logger.get_context() == {}
            
            # Test Performance Logger
            perf_logger = PerformanceLogger()
            perf_logger.start_timer("test_operation")
            time.sleep(0.1)
            perf_logger.end_timer("test_operation", {"operation_type": "test"})
            
            perf_logger.log_metric("test_metric", 42, "units")
            
            # Test Error Tracker
            error_tracker = ErrorTracker()
            
            try:
                raise ValueError("Test error")
            except ValueError as e:
                error_tracker.track_error(e, {"context": "test"})
            
            stats = error_tracker.get_error_stats()
            assert len(stats) > 0
            
            # Test decorators
            @performance_timer("test_timer")
            @log_errors("test_errors")
            def test_function():
                time.sleep(0.05)
                return "success"
            
            result = test_function()
            assert result == "success"
            
            # Test function with error
            @log_errors("test_errors")
            def error_function():
                raise RuntimeError("Test error")
            
            try:
                error_function()
            except RuntimeError:
                pass  # Expected
            
            # Kiểm tra log files được tạo
            log_files = list(os.listdir(temp_dir))
            assert any(f.endswith('.log') for f in log_files)
        
        print("  ✅ Logging Utils: PASSED")
        return True
        
    except Exception as e:
        print(f"  ❌ Logging Utils: FAILED - {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_processor():
    """Test File Processor (đã test trước đó)"""
    print("\n🔍 Testing File Processor")
    
    try:
        from src.deep_research_core.utils.shared.file_processor import FileProcessor
        
        # Test đơn giản
        processor = FileProcessor(
            file_content=b"Hello World Test",
            file_type="txt",
            extract_metadata=False
        )
        
        text = processor.extract_text()
        assert "Hello World Test" in text
        
        processor.cleanup()
        
        print("  ✅ File Processor: PASSED")
        return True
        
    except Exception as e:
        print(f"  ❌ File Processor: FAILED - {e}")
        return False

def main():
    """Chạy tất cả tests"""
    print("🚀 TESTING ALL UTILS MODULES")
    print("=" * 60)
    
    tests = [
        test_cache_utils,
        test_network_utils,
        test_config_utils,
        test_logging_utils,
        test_file_processor,
    ]
    
    results = []
    
    for test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"  ❌ {test_func.__name__}: FAILED - {e}")
            results.append(False)
    
    # Tóm tắt kết quả
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")
    print(f"📈 Success Rate: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 ALL UTILS MODULES TESTS PASSED!")
        return True
    else:
        print(f"\n⚠️ {total - passed} tests failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
