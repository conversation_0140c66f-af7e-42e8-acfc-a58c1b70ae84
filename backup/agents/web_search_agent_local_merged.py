#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
WebSearchAgentLocal Merged - <PERSON><PERSON><PERSON> bả<PERSON> hợp nhất tất cả tính năng hay nhất.

<PERSON><PERSON><PERSON> bản này kết hợp:
- Base từ deepresearch/web_search_agent_local.py (Feature Rich)
- Credibility evaluation từ src/deep_research_core/agents/web_search_agent_local.py
- LLM integration từ src/deep_research_core/websearch_agent_local.py
"""

import time
import logging
import random
import os
import re
import requests
import math
from typing import Dict, List, Any, Optional, Tuple, Set, Union
from bs4 import BeautifulSoup
import json
import hashlib
import urllib.parse
import concurrent.futures
from datetime import datetime, timedelta
import tempfile
import shutil
from pathlib import Path
import string
import csv
import difflib
import uuid
import sys
import socket
from urllib.robotparser import RobotFileParser
from collections import defaultdict, Counter, deque
import traceback
import warnings
from concurrent.futures import ThreadPoolExecutor, as_completed

# <PERSON><PERSON><PERSON> nghĩa logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

# Suppressing deprecation warnings
warnings.filterwarnings("ignore", category=DeprecationWarning)

# Mô tả version
__version__ = "2.0.0"
__author__ = "Deep Research Team"
__description__ = "WebSearchAgentLocal Merged - Integrated all best features"

# Cố gắng import các dependencies không bắt buộc - Chỉ import khi cần
NLTK_AVAILABLE = False
TRANSFORMERS_AVAILABLE = False
SPACY_AVAILABLE = False
NEWSPAPER_AVAILABLE = False
PLAYWRIGHT_AVAILABLE = False
PYPDF_AVAILABLE = False
DOCX_AVAILABLE = False
PANDAS_AVAILABLE = False

# Cố gắng import FileProcessor - Sử dụng phiên bản hiện có
try:
    from src.deep_research_core.utils.file_processor import FileProcessor, extract_file
except ImportError:
    try:
        from ..utils.file_processor import FileProcessor, extract_file
    except ImportError:
        print("Warning: FileProcessor could not be imported")
        FileProcessor = None
        extract_file = None

class WebSearchAgentLocalMerged:
    """
    Agent tìm kiếm web cục bộ với các tính năng nâng cao tích hợp.
    
    Phiên bản hợp nhất với:
    - Phân tích độ tin cậy
    - Trích xuất nội dung nâng cao
    - Tăng cường truy vấn
    - Phân tích ngữ nghĩa
    - Tích hợp LLM
    - Phát hiện tin giả
    - Tối ưu hóa hiệu suất
    """
    
    def __init__(self, **kwargs):
        """
        Khởi tạo WebSearchAgentLocalMerged.
        
        Args:
            **kwargs: Các tham số tùy chọn
        """
        # Cấu hình cơ bản
        self.max_results = kwargs.get('max_results', 10)
        self.max_retries = kwargs.get('max_retries', 3)
        self.timeout = kwargs.get('timeout', 30)
        self.user_agent = kwargs.get('user_agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
        self.respect_robots_txt = kwargs.get('respect_robots_txt', True)
        self.proxy = kwargs.get('proxy', None)
        
        # Cache cho robots.txt
        self._robots_cache = {}
        
        # Thư mục dữ liệu và cấu hình cơ bản
        self.data_dir = kwargs.get('data_dir', os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'data'))
        os.makedirs(self.data_dir, exist_ok=True)
        
        # Thư mục cache
        self.cache_dir = os.path.join(self.data_dir, 'cache')
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # Thư mục cho từ điển
        self.dictionaries_dir = kwargs.get('dictionaries_dir', os.path.join(self.data_dir, 'dictionaries'))
        os.makedirs(self.dictionaries_dir, exist_ok=True)
        
        # Tùy chọn credibility evaluation
        self.enable_credibility_evaluation = kwargs.get('enable_credibility_evaluation', True)
        self.filter_unreliable_sources = kwargs.get('filter_unreliable_sources', True)
        self.rerank_by_credibility = kwargs.get('rerank_by_credibility', True)
        self.min_credibility_score = kwargs.get('min_credibility_score', 0.5)
        
        # Tùy chọn query enhancement
        self.enable_query_enhancement = kwargs.get('enable_query_enhancement', True)
        
        # Tùy chọn advanced extraction
        self.use_advanced_extraction = kwargs.get('use_advanced_extraction', True)
        
        # Cấu hình LLM
        self.use_local_llm = kwargs.get('use_local_llm', False)
        self.llm_model = kwargs.get('llm_model', 'gpt-3.5-turbo')
        
        # Phát hiện tin giả
        self.fake_news_detection = kwargs.get('fake_news_detection', False)
        
        # Phân tích ngữ nghĩa
        self.semantic_analysis = kwargs.get('semantic_analysis', False)
        
        # Captcha handler
        self.captcha_handler = kwargs.get('captcha_handler', None)
        
        # Cấu hình cache
        self.cache_ttl = kwargs.get('cache_ttl', 86400)  # 24 giờ mặc định
        
        # Cấu hình request
        self.parallel_requests = kwargs.get('parallel_requests', 5)
        
        # Khởi tạo thành phần
        self.use_default_components = kwargs.get('use_default_components', True)
        
        # Hỗ trợ ngôn ngữ
        self.language_support = kwargs.get('language_support', ['en', 'vi'])
        
        # Khởi tạo bộ nhớ cache
        self.result_cache = {}
        
        # Lazy-load các components
        self.search_enhancer = None
        self.credibility_evaluator = None
        self.fake_news_detector = None
        self.question_evaluator = None
        self.answer_evaluator = None
        self.query_decomposer = None
        self.semantic_analyzer = None
        self.llm_analyzer = None
        self.crawler = None
        
        # Kiểm tra từ điển
        self._verify_dictionaries()
        
        logger.info(f"WebSearchAgentLocalMerged initialized with data_dir: {self.data_dir}")
    
    def _initialize_components(self):
        """
        Khởi tạo các thành phần cần thiết cho agent.
        """
        # Tính năng này sẽ được gọi lazily khi cần
        pass
    
    def search(self, query: str, **kwargs):
        """
        Tìm kiếm thông tin dựa trên truy vấn với logic thực tế.

        Args:
            query: Truy vấn tìm kiếm
            **kwargs: Các tham số tùy chọn
                - num_results: Số lượng kết quả tối đa (mặc định: 10)
                - use_deep_search: Có sử dụng tìm kiếm sâu không (mặc định: False)
                - create_simple_answer: Có tạo câu trả lời đơn giản không (mặc định: True)
                - evaluate_answer: Có đánh giá chất lượng câu trả lời không (mặc định: False)

        Returns:
            Dict chứa kết quả tìm kiếm
        """
        try:
            # Lấy các tham số
            num_results = kwargs.get('num_results', self.max_results)
            use_deep_search = kwargs.get('use_deep_search', False)
            create_simple_answer = kwargs.get('create_simple_answer', True)
            evaluate_answer = kwargs.get('evaluate_answer', False)

            # Kiểm tra đầu vào
            if not query or not query.strip():
                return {
                    "query": query,
                    "results": [],
                    "simple_answer": "Vui lòng nhập truy vấn hợp lệ.",
                    "error": "Empty query"
                }

            # Chuẩn hóa truy vấn
            query = query.strip()

            # Đánh giá độ phức tạp câu hỏi
            complexity_evaluation = self.evaluate_question_complexity(query)

            # Xác định phương pháp tìm kiếm dựa trên độ phức tạp
            if complexity_evaluation.get('complexity_score', 0.5) > 0.7 or use_deep_search:
                method = "adaptive_search"
                results = self._perform_adaptive_search(query, num_results)
            else:
                method = "standard_search"
                results = self._perform_standard_search(query, num_results)

            # Đánh giá độ tin cậy của kết quả
            if self.enable_credibility_evaluation:
                for result in results:
                    domain = self._extract_domain(result.get('url', ''))
                    credibility_score = self._get_domain_credibility(domain)
                    if credibility_score is not None:
                        result['credibility_score'] = credibility_score
                    else:
                        result['credibility_score'] = 0.5  # Mặc định

            # Tạo câu trả lời đơn giản
            simple_answer = ""
            if create_simple_answer:
                simple_answer = self._create_simple_answer(results, query)

            # Đánh giá chất lượng câu trả lời
            answer_evaluation = None
            if evaluate_answer and simple_answer:
                answer_evaluation = self.evaluate_answer_quality(query, simple_answer)

            # Chuẩn bị kết quả trả về
            response = {
                "query": query,
                "results": results[:num_results],
                "simple_answer": simple_answer,
                "method_used": method,
                "complexity_evaluation": complexity_evaluation,
                "timestamp": datetime.now().isoformat()
            }

            if answer_evaluation:
                response["answer_evaluation"] = answer_evaluation

            return response

        except Exception as e:
            logger.error(f"Lỗi trong quá trình tìm kiếm: {str(e)}")
            return {
                "query": query,
                "results": [],
                "simple_answer": f"Xin lỗi, đã xảy ra lỗi trong quá trình tìm kiếm: {str(e)}",
                "error": str(e)
            }

    def _verify_dictionaries(self):
        """
        Kiểm tra và tạo các từ điển cần thiết nếu chưa tồn tại.
        """
        # Danh sách các từ điển cần thiết
        dictionaries = {
            'reliable_domains.json': {
                'description': 'Danh sách các domain đáng tin cậy',
                'default': {
                    'trusted_news': ['bbc.com', 'reuters.com', 'apnews.com', 'npr.org', 'cnn.com', 'nytimes.com'],
                    'trusted_education': ['edu', 'ac.uk', 'harvard.edu', 'mit.edu', 'stanford.edu'],
                    'trusted_government': ['gov', 'europa.eu', 'who.int', 'un.org'],
                    'trusted_reference': ['wikipedia.org', 'britannica.com', 'merriam-webster.com']
                }
            },
            'unreliable_domains.json': {
                'description': 'Danh sách các domain không đáng tin cậy',
                'default': {
                    'fake_news': [],
                    'click_bait': [],
                    'conspiracy': [],
                    'spam': []
                }
            },
            'search_engines.json': {
                'description': 'Cấu hình cho các search engine',
                'default': {
                    'general': ['google', 'bing', 'duckduckgo'],
                    'academic': ['google_scholar', 'semantic_scholar', 'pubmed'],
                    'news': ['google_news', 'bing_news'],
                    'vietnamese': ['coccoc', 'google_vn'],
                    'specialized': []
                }
            },
            'query_patterns.json': {
                'description': 'Các mẫu truy vấn phổ biến',
                'default': {
                    'comparison': ['so sánh', 'compare', 'vs', 'versus', 'differences between'],
                    'definition': ['là gì', 'what is', 'define', 'definition of', 'meaning of'],
                    'how_to': ['làm thế nào', 'how to', 'how do i', 'cách', 'phương pháp'],
                    'cause_effect': ['tại sao', 'why', 'causes of', 'effects of', 'nguyên nhân', 'hậu quả'],
                    'list': ['danh sách', 'list of', 'examples of', 'types of'],
                    'fact': ['có thật không', 'is it true', 'fact check', 'kiểm chứng']
                }
            },
            'vietnamese_stopwords.txt': {
                'description': 'Danh sách các stopword tiếng Việt',
                'default': '\n'.join([
                    'và', 'của', 'cho', 'là', 'để', 'trong', 'được', 'với', 'có', 'không',
                    'người', 'những', 'này', 'khi', 'từ', 'một', 'bạn', 'đã', 'các', 'đến',
                    'sẽ', 'về', 'vì', 'như', 'ra', 'nên', 'cũng', 'thì', 'tôi', 'lại',
                    'còn', 'theo', 'nhưng', 'rất', 'nhiều', 'đó', 'cần', 'mà', 'ai', 'lên',
                    'chỉ', 'sau', 'trên', 'vào', 'nếu', 'nào', 'đang', 'hay', 'tại', 'sự'
                ])
            }
        }
        
        # Kiểm tra và tạo từng từ điển
        for filename, info in dictionaries.items():
            file_path = os.path.join(self.dictionaries_dir, filename)
            
            # Nếu file chưa tồn tại, tạo mới với dữ liệu mặc định
            if not os.path.exists(file_path):
                logger.info(f"Creating dictionary file: {filename}")
                
                # Xác định loại file dựa trên phần mở rộng
                if filename.endswith('.json'):
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(info['default'], f, indent=4, ensure_ascii=False)
                else:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(info['default'])
                        
                logger.info(f"Created {filename}: {info['description']}")
            else:
                logger.debug(f"Dictionary file exists: {filename}") 

    def _fix_vietnamese_encoding(self, text: str) -> str:
        """
        Sửa lỗi encoding tiếng Việt phổ biến.
        
        Args:
            text: Văn bản cần sửa
            
        Returns:
            Văn bản đã sửa lỗi encoding
        """
        if not text:
            return ""
        
        # Danh sách các cặp thay thế phổ biến
        replacements = [
            # Các dấu thanh
            ('à', 'à'), ('á', 'á'), ('ả', 'ả'), ('ã', 'ã'), ('ạ', 'ạ'),
            ('ă', 'ă'), ('ằ', 'ằ'), ('ắ', 'ắ'), ('ẳ', 'ẳ'), ('ẵ', 'ẵ'), ('ặ', 'ặ'),
            ('â', 'â'), ('ầ', 'ầ'), ('ấ', 'ấ'), ('ẩ', 'ẩ'), ('ẫ', 'ẫ'), ('ậ', 'ậ'),
            ('è', 'è'), ('é', 'é'), ('ẻ', 'ẻ'), ('ẽ', 'ẽ'), ('ẹ', 'ẹ'),
            ('ê', 'ê'), ('ề', 'ề'), ('ế', 'ế'), ('ể', 'ể'), ('ễ', 'ễ'), ('ệ', 'ệ'),
            ('ì', 'ì'), ('í', 'í'), ('ỉ', 'ỉ'), ('ĩ', 'ĩ'), ('ị', 'ị'),
            ('ò', 'ò'), ('ó', 'ó'), ('ỏ', 'ỏ'), ('õ', 'õ'), ('ọ', 'ọ'),
            ('ô', 'ô'), ('ồ', 'ồ'), ('ố', 'ố'), ('ổ', 'ổ'), ('ỗ', 'ỗ'), ('ộ', 'ộ'),
            ('ơ', 'ơ'), ('ờ', 'ờ'), ('ớ', 'ớ'), ('ở', 'ở'), ('ỡ', 'ỡ'), ('ợ', 'ợ'),
            ('ù', 'ù'), ('ú', 'ú'), ('ủ', 'ủ'), ('ũ', 'ũ'), ('ụ', 'ụ'),
            ('ư', 'ư'), ('ừ', 'ừ'), ('ứ', 'ứ'), ('ử', 'ử'), ('ữ', 'ữ'), ('ự', 'ự'),
            ('ỳ', 'ỳ'), ('ý', 'ý'), ('ỷ', 'ỷ'), ('ỹ', 'ỹ'), ('ỵ', 'ỵ'),
            ('đ', 'đ'),
            
            # Các lỗi phổ biến khi encode/decode (đã chuyển sang dạng an toàn)
            ('Ã\xc2\xa4', 'à'), ('Ã\xc2\xa1', 'á'), ('áº\xc2\xa3', 'ả'), ('Ã\xc2\xa3', 'ã'), ('áº\xc2\xa1', 'ạ'),
            ('Ã\xc2\x84\xc2\x83', 'ă'), ('áº\xc2\xb1', 'ằ'), ('áº\xc2\xaf', 'ắ'), ('áº\xc2\xb3', 'ẳ'), ('áº\xc2\xb5', 'ẵ'), ('áº\xc2\xb7', 'ặ'),
            ('Ã\xc2\xa2', 'â'), ('áº\xc2\xa7', 'ầ'), ('áº\xc2\xa5', 'ấ'), ('áº\xc2\xa9', 'ẩ'), ('áº\xc2\xab', 'ẫ'), ('áº\xc2\xad', 'ậ'),
            ('Ã\xc2\xa8', 'è'), ('Ã\xc2\xa9', 'é'), ('áº\xc2\xbb', 'ẻ'), ('áº\xc2\xbd', 'ẽ'), ('áº\xc2\xb9', 'ẹ'),
            ('Ã\xc2\xaa', 'ê'), ('á»\xc2\x81', 'ề'), ('áº\xc2\xbf', 'ế'), ('á»\xc2\x83', 'ể'), ('á»\xc2\x85', 'ễ'), ('á»\xc2\x87', 'ệ'),
            ('Ã\xc2\xac', 'ì'), ('Ã\xc2\xad', 'í'), ('á»\xc2\x89', 'ỉ'), ('Ã\xc2\x84\xc2\xa9', 'ĩ'), ('á»\xc2\x8b', 'ị'),
            ('Ã\xc2\xb2', 'ò'), ('Ã\xc2\xb3', 'ó'), ('á»\xc2\x8f', 'ỏ'), ('Ã\xc2\xb5', 'õ'), ('á»\xc2\x8d', 'ọ'),
            ('Ã\xc2\xb4', 'ô'), ('á»\xc2\x93', 'ồ'), ('á»\xc2\x91', 'ố'), ('á»\xc2\x95', 'ổ'), ('á»\xc2\x97', 'ỗ'), ('á»\xc2\x99', 'ộ'),
            ('Ã\xc2\x86\xc2\xa1', 'ơ'), ('á»\xc2\x9d', 'ờ'), ('á»\xc2\x9b', 'ớ'), ('á»\xc2\x9f', 'ở'), ('á»\xc2\xa1', 'ỡ'), ('á»\xc2\xa3', 'ợ'),
            ('Ã\xc2\xb9', 'ù'), ('Ã\xc2\xba', 'ú'), ('á»\xc2\xa7', 'ủ'), ('Ã\xc2\x85\xc2\xa9', 'ũ'), ('á»\xc2\xa5', 'ụ'),
            ('Ã\xc2\x86\xc2\xb0', 'ư'), ('á»\xc2\xab', 'ừ'), ('á»\xc2\xa9', 'ứ'), ('á»\xc2\xad', 'ử'), ('á»\xc2\xaf', 'ữ'), ('á»\xc2\xb1', 'ự'),
            ('á»\xc2\xb3', 'ỳ'), ('Ã\xc2\xbd', 'ý'), ('á»\xc2\xb7', 'ỷ'), ('á»\xc2\xb9', 'ỹ'), ('á»\xc2\xb5', 'ỵ'),
            ('Ã\xc2\x84\xc2\x91', 'đ'), ('&#273;', 'đ'), ('&#272;', 'Đ')
        ]
        
        # Thực hiện các thay thế
        for wrong, correct in replacements:
            text = text.replace(wrong, correct)
        
        # Xử lý các entity HTML phổ biến
        text = text.replace('&nbsp;', ' ')
        text = text.replace('&amp;', '&')
        text = text.replace('&lt;', '<')
        text = text.replace('&gt;', '>')
        text = text.replace('&quot;', '"')
        
        # Xử lý các khoảng trắng dư thừa
        text = re.sub(r'\s+', ' ', text)
        
        return text.strip() 

    def _is_vietnamese_text(self, text: str) -> bool:
        """
        Kiểm tra xem văn bản có phải là tiếng Việt không.
        
        Args:
            text: Văn bản cần kiểm tra
            
        Returns:
            True nếu văn bản là tiếng Việt, False nếu không
        """
        if not text or len(text.strip()) < 10:
            return False
        
        # Danh sách các ký tự đặc biệt trong tiếng Việt
        vietnamese_chars = set('áàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđ')
        
        # Đếm số lượng ký tự tiếng Việt
        text_lower = text.lower()
        vn_char_count = sum(1 for c in text_lower if c in vietnamese_chars)
        
        # Nếu có ít nhất 2 ký tự tiếng Việt và tỷ lệ ký tự tiếng Việt so với tổng số ký tự (không kể khoảng trắng) > 5%
        total_chars = len([c for c in text_lower if c.isalpha()])
        if total_chars == 0:
            return False
            
        vn_char_ratio = vn_char_count / total_chars
        
        # Kiểm tra các từ tiếng Việt phổ biến
        vietnamese_words = ['của', 'và', 'các', 'những', 'trong', 'với', 'cho', 'được', 'không', 'này', 'đó', 'có', 'là']
        word_count = sum(1 for word in vietnamese_words if word in text_lower)
        
        return (vn_char_count >= 2 and vn_char_ratio >= 0.05) or word_count >= 3 

    def _combine_vietnamese_diacritic(self, vowel: str, diacritic: str) -> str:
        """
        Kết hợp nguyên âm và dấu trong tiếng Việt.
        
        Args:
            vowel: Nguyên âm
            diacritic: Dấu
            
        Returns:
            Ký tự có dấu
        """
        # Bảng ánh xạ các nguyên âm không dấu sang các nguyên âm có dấu
        # Dạng: {nguyên_âm: {dấu: ký_tự_có_dấu}}
        diacritic_map = {
            'a': {
                'grave': 'à', 'acute': 'á', 'hook': 'ả', 'tilde': 'ã', 'dot': 'ạ'
            },
            'ă': {
                'grave': 'ằ', 'acute': 'ắ', 'hook': 'ẳ', 'tilde': 'ẵ', 'dot': 'ặ'
            },
            'â': {
                'grave': 'ầ', 'acute': 'ấ', 'hook': 'ẩ', 'tilde': 'ẫ', 'dot': 'ậ'
            },
            'e': {
                'grave': 'è', 'acute': 'é', 'hook': 'ẻ', 'tilde': 'ẽ', 'dot': 'ẹ'
            },
            'ê': {
                'grave': 'ề', 'acute': 'ế', 'hook': 'ể', 'tilde': 'ễ', 'dot': 'ệ'
            },
            'i': {
                'grave': 'ì', 'acute': 'í', 'hook': 'ỉ', 'tilde': 'ĩ', 'dot': 'ị'
            },
            'o': {
                'grave': 'ò', 'acute': 'ó', 'hook': 'ỏ', 'tilde': 'õ', 'dot': 'ọ'
            },
            'ô': {
                'grave': 'ồ', 'acute': 'ố', 'hook': 'ổ', 'tilde': 'ỗ', 'dot': 'ộ'
            },
            'ơ': {
                'grave': 'ờ', 'acute': 'ớ', 'hook': 'ở', 'tilde': 'ỡ', 'dot': 'ợ'
            },
            'u': {
                'grave': 'ù', 'acute': 'ú', 'hook': 'ủ', 'tilde': 'ũ', 'dot': 'ụ'
            },
            'ư': {
                'grave': 'ừ', 'acute': 'ứ', 'hook': 'ử', 'tilde': 'ữ', 'dot': 'ự'
            },
            'y': {
                'grave': 'ỳ', 'acute': 'ý', 'hook': 'ỷ', 'tilde': 'ỹ', 'dot': 'ỵ'
            }
        }
        
        # Xử lý chữ in hoa
        is_upper = vowel.isupper()
        vowel_lower = vowel.lower()
        
        # Kiểm tra nguyên âm có trong ánh xạ không
        if vowel_lower not in diacritic_map:
            return vowel  # Trả về nguyên âm gốc nếu không tìm thấy
        
        # Kiểm tra dấu có trong ánh xạ không
        if diacritic not in diacritic_map[vowel_lower]:
            return vowel  # Trả về nguyên âm gốc nếu không tìm thấy
        
        # Lấy ký tự có dấu
        result = diacritic_map[vowel_lower][diacritic]
        
        # Trả về dạng in hoa nếu nguyên âm ban đầu là in hoa
        return result.upper() if is_upper else result
    
    def _decode_html_entity(self, entity: str) -> str:
        """
        Giải mã HTML entity.
        
        Args:
            entity: HTML entity cần giải mã
            
        Returns:
            Ký tự tương ứng
        """
        # Danh sách các HTML entity phổ biến
        html_entities = {
            '&nbsp;': ' ', '&lt;': '<', '&gt;': '>', '&amp;': '&', '&quot;': '"', '&apos;': "'",
            '&cent;': '¢', '&pound;': '£', '&yen;': '¥', '&euro;': '€', '&copy;': '©', '&reg;': '®',
            
            # Các dấu tiếng Việt
            '&agrave;': 'à', '&aacute;': 'á', '&acirc;': 'â', '&atilde;': 'ã', '&auml;': 'ä', '&aring;': 'å',
            '&aelig;': 'æ', '&ccedil;': 'ç', '&egrave;': 'è', '&eacute;': 'é', '&ecirc;': 'ê', '&euml;': 'ë',
            '&igrave;': 'ì', '&iacute;': 'í', '&icirc;': 'î', '&iuml;': 'ï', '&eth;': 'ð', '&ntilde;': 'ñ',
            '&ograve;': 'ò', '&oacute;': 'ó', '&ocirc;': 'ô', '&otilde;': 'õ', '&ouml;': 'ö',
            '&oslash;': 'ø', '&ugrave;': 'ù', '&uacute;': 'ú', '&ucirc;': 'û', '&uuml;': 'ü',
            '&yacute;': 'ý', '&thorn;': 'þ', '&yuml;': 'ÿ', '&fnof;': 'ƒ',
            
            # Các entity tiếng Việt đặc biệt
            '&#273;': 'đ', '&#272;': 'Đ',
            '&#432;': 'ư', '&#431;': 'Ư',
            '&#417;': 'ơ', '&#416;': 'Ơ'
        }
        
        # Kiểm tra có phải entity dạng tên không
        if entity in html_entities:
            return html_entities[entity]
        
        # Kiểm tra có phải entity dạng số không (&#123;)
        if entity.startswith('&#'):
            try:
                # Loại bỏ '&#' và ';' rồi chuyển thành số
                code_point = int(entity[2:-1])
                # Chuyển mã số thành ký tự Unicode
                return chr(code_point)
            except (ValueError, OverflowError):
                return entity  # Trả về entity gốc nếu không thể chuyển đổi
        
        # Trả về entity gốc nếu không phù hợp với bất kỳ trường hợp nào
        return entity
    
    def _improve_vietnamese_paragraphs(self, content: str) -> str:
        """
        Cải thiện đoạn văn tiếng Việt, đảm bảo đúng cú pháp và định dạng.
        
        Args:
            content: Nội dung cần cải thiện
            
        Returns:
            Nội dung đã được cải thiện
        """
        if not content or not self._is_vietnamese_text(content):
            return content
            
        # Sửa lỗi mã hóa tiếng Việt
        content = self._fix_vietnamese_encoding(content)
        
        # Loại bỏ khoảng trắng dư thừa
        content = re.sub(r'\s+', ' ', content)
        
        # Sửa lỗi dấu câu
        content = re.sub(r'(?<=[.,!?]) (?=[A-ZÁÀẢÃẠĂẮẰẲẴẶÂẤẦẨẪẬÉÈẺẼẸÊẾỀỂỄỆÍÌỈĨỊÓÒỎÕỌÔỐỒỔỖỘƠỚỜỞỠỢÚÙỦŨỤƯỨỪỬỮỰÝỲỶỸỴĐ])', '. ', content)
        
        # Chuẩn hóa dấu câu (đảm bảo khoảng trắng sau dấu câu)
        content = re.sub(r'([.,!?;:])([^\s])', r'\1 \2', content)
        
        # Sửa lỗi viết hoa
        sentences = re.split(r'([.!?] )', content)
        result = []
        for i in range(0, len(sentences), 2):
            if i+1 < len(sentences):
                sentence = sentences[i]
                if sentence and sentence[0].islower():
                    sentence = sentence[0].upper() + sentence[1:]
                result.append(sentence + sentences[i+1])
            else:
                result.append(sentences[i])
        
        content = ''.join(result)
        
        # Chia thành các đoạn văn hợp lý
        paragraphs = content.split('\n')
        improved_paragraphs = []
        
        for p in paragraphs:
            # Loại bỏ đoạn quá ngắn hoặc không có ý nghĩa
            if len(p.strip()) < 10 or not any(c.isalpha() for c in p):
                continue
                
            improved_paragraphs.append(p.strip())
        
        # Kết hợp lại thành văn bản hoàn chỉnh
        return '\n\n'.join(improved_paragraphs)
    
    def _remove_vietnamese_boilerplate(self, content: str) -> str:
        """
        Loại bỏ nội dung trùng lặp, không cần thiết trong văn bản tiếng Việt.
        
        Args:
            content: Nội dung cần xử lý
            
        Returns:
            Nội dung đã được làm sạch
        """
        if not content:
            return ""
            
        # Danh sách các mẫu boilerplate phổ biến trong trang web tiếng Việt
        boilerplate_patterns = [
            r'(?i)bản quyền thuộc về.*',
            r'(?i)copyright.*\d{4}.*',
            r'(?i)all rights reserved.*',
            r'(?i)tất cả các quyền được bảo lưu.*',
            r'(?i)liên hệ quảng cáo.*',
            r'(?i)liên hệ:.*@.*\.(com|vn|net)',
            r'(?i)địa chỉ:.*quận.*thành phố.*',
            r'(?i)theo dõi chúng tôi trên.*',
            r'(?i)follow us on.*',
            r'(?i)hotline:.*\d{10}',
            r'(?i)giấy phép.*số.*cục báo chí.*',
            r'(?i)chịu trách nhiệm quản lý nội dung:.*',
            r'(?i)không sao chép nội dung này dưới mọi hình thức.*',
            r'(?i)nội dung được sao chép không được quá.*%.*',
            r'(?i)đăng ký nhận tin.*',
            r'(?i)đăng ký.*nhận bản tin.*',
            r'(?i)email của bạn sẽ không.*công khai.*',
            r'(?i)để lại bình luận.*',
            r'(?i)thông báo về.*',
            r'(?i)trang chủ.*giới thiệu.*liên hệ.*',
            r'(?i)tin nóng.*tin mới.*'
        ]
        
        # Áp dụng các mẫu để loại bỏ boilerplate
        for pattern in boilerplate_patterns:
            content = re.sub(pattern, '', content)
        
        # Loại bỏ các dòng quá ngắn (thường là menu, tiêu đề, v.v.)
        lines = content.split('\n')
        cleaned_lines = [line for line in lines if len(line.strip()) > 20 or (len(line.strip()) > 0 and sum(c.isalpha() for c in line) / len(line) > 0.7)]
        
        # Loại bỏ các dòng trùng lặp
        unique_lines = []
        for line in cleaned_lines:
            line_stripped = line.strip()
            if line_stripped and line_stripped not in unique_lines:
                unique_lines.append(line_stripped)
        
        # Kết hợp lại thành văn bản sạch
        return '\n'.join(unique_lines)
    
    def _remove_vietnamese_tones(self, text: str) -> str:
        """
        Loại bỏ dấu trong tiếng Việt.
        
        Args:
            text: Văn bản cần xử lý
            
        Returns:
            Văn bản không dấu
        """
        if not text:
            return ""
            
        # Bảng ánh xạ các ký tự có dấu sang không dấu
        tone_marks = {
            'à': 'a', 'á': 'a', 'ả': 'a', 'ã': 'a', 'ạ': 'a',
            'ă': 'a', 'ằ': 'a', 'ắ': 'a', 'ẳ': 'a', 'ẵ': 'a', 'ặ': 'a',
            'â': 'a', 'ầ': 'a', 'ấ': 'a', 'ẩ': 'a', 'ẫ': 'a', 'ậ': 'a',
            'è': 'e', 'é': 'e', 'ẻ': 'e', 'ẽ': 'e', 'ẹ': 'e',
            'ê': 'e', 'ề': 'e', 'ế': 'e', 'ể': 'e', 'ễ': 'e', 'ệ': 'e',
            'ì': 'i', 'í': 'i', 'ỉ': 'i', 'ĩ': 'i', 'ị': 'i',
            'ò': 'o', 'ó': 'o', 'ỏ': 'o', 'õ': 'o', 'ọ': 'o',
            'ô': 'o', 'ồ': 'o', 'ố': 'o', 'ổ': 'o', 'ỗ': 'o', 'ộ': 'o',
            'ơ': 'o', 'ờ': 'o', 'ớ': 'o', 'ở': 'o', 'ỡ': 'o', 'ợ': 'o',
            'ù': 'u', 'ú': 'u', 'ủ': 'u', 'ũ': 'u', 'ụ': 'u',
            'ư': 'u', 'ừ': 'u', 'ứ': 'u', 'ử': 'u', 'ữ': 'u', 'ự': 'u',
            'ỳ': 'y', 'ý': 'y', 'ỷ': 'y', 'ỹ': 'y', 'ỵ': 'y',
            'đ': 'd',
            'À': 'A', 'Á': 'A', 'Ả': 'A', 'Ã': 'A', 'Ạ': 'A',
            'Ă': 'A', 'Ằ': 'A', 'Ắ': 'A', 'Ẳ': 'A', 'Ẵ': 'A', 'Ặ': 'A',
            'Â': 'A', 'Ầ': 'A', 'Ấ': 'A', 'Ẩ': 'A', 'Ẫ': 'A', 'Ậ': 'A',
            'È': 'E', 'É': 'E', 'Ẻ': 'E', 'Ẽ': 'E', 'Ẹ': 'E',
            'Ê': 'E', 'Ề': 'E', 'Ế': 'E', 'Ể': 'E', 'Ễ': 'E', 'Ệ': 'E',
            'Ì': 'I', 'Í': 'I', 'Ỉ': 'I', 'Ĩ': 'I', 'Ị': 'I',
            'Ò': 'O', 'Ó': 'O', 'Ỏ': 'O', 'Õ': 'O', 'Ọ': 'O',
            'Ô': 'O', 'Ồ': 'O', 'Ố': 'O', 'Ổ': 'O', 'Ỗ': 'O', 'Ộ': 'O',
            'Ơ': 'O', 'Ờ': 'O', 'Ớ': 'O', 'Ở': 'O', 'Ỡ': 'O', 'Ợ': 'O',
            'Ù': 'U', 'Ú': 'U', 'Ủ': 'U', 'Ũ': 'U', 'Ụ': 'U',
            'Ư': 'U', 'Ừ': 'U', 'Ứ': 'U', 'Ử': 'U', 'Ữ': 'U', 'Ự': 'U',
            'Ỳ': 'Y', 'Ý': 'Y', 'Ỷ': 'Y', 'Ỹ': 'Y', 'Ỵ': 'Y',
            'Đ': 'D'
        }
        
        # Thực hiện thay thế
        for char, replace in tone_marks.items():
            text = text.replace(char, replace)
            
        return text

    def evaluate_question_complexity(self, query: str) -> Dict[str, Any]:
        """
        Đánh giá độ phức tạp của câu hỏi.

        Args:
            query: Câu hỏi cần đánh giá

        Returns:
            Dictionary chứa thông tin đánh giá
        """
        if not query:
            return {
                "complexity_level": "low",
                "complexity_score": 0.1,
                "question_type": "invalid",
                "question_characteristics": ["Câu hỏi trống hoặc không hợp lệ"],
                "entities": [],
                "keywords": [],
                "recommended_strategy": {
                    "search_method": "simple",
                    "max_depth": 1,
                    "max_pages": 3,
                    "decompose_query": False,
                    "use_multiple_engines": False
                }
            }

        # Tính toán điểm phức tạp
        complexity_score = self._calculate_query_complexity(query)

        # Xác định mức độ phức tạp
        if complexity_score < 0.3:
            complexity_level = "low"
        elif complexity_score < 0.7:
            complexity_level = "medium"
        else:
            complexity_level = "high"

        # Phân tích loại câu hỏi
        question_type = self._analyze_question_type(query)

        # Trích xuất thực thể và từ khóa
        entities = self._extract_entities(query)
        keywords = self._extract_keywords(query)

        # Đề xuất chiến lược tìm kiếm
        recommended_strategy = self._recommend_search_strategy(complexity_score, question_type)

        return {
            "complexity_level": complexity_level,
            "complexity_score": complexity_score,
            "question_type": question_type,
            "question_characteristics": self._analyze_question_characteristics(query),
            "entities": entities,
            "keywords": keywords,
            "recommended_strategy": recommended_strategy
        }

    def _analyze_question_type(self, query: str) -> str:
        """
        Phân tích loại câu hỏi.

        Args:
            query: Câu hỏi cần phân tích

        Returns:
            Loại câu hỏi
        """
        query_lower = query.lower()

        # Câu hỏi định nghĩa
        if any(pattern in query_lower for pattern in ['là gì', 'what is', 'define', 'definition']):
            return "definition"

        # Câu hỏi so sánh
        if any(pattern in query_lower for pattern in ['so sánh', 'compare', 'vs', 'versus', 'khác nhau']):
            return "comparison"

        # Câu hỏi hướng dẫn
        if any(pattern in query_lower for pattern in ['làm thế nào', 'how to', 'cách', 'phương pháp']):
            return "how_to"

        # Câu hỏi nguyên nhân
        if any(pattern in query_lower for pattern in ['tại sao', 'why', 'nguyên nhân', 'lý do']):
            return "causal"

        # Câu hỏi danh sách
        if any(pattern in query_lower for pattern in ['danh sách', 'list', 'examples', 'ví dụ']):
            return "list"

        # Câu hỏi thông tin
        return "informational"

    def _analyze_question_characteristics(self, query: str) -> List[str]:
        """
        Phân tích đặc điểm của câu hỏi.

        Args:
            query: Câu hỏi cần phân tích

        Returns:
            Danh sách các đặc điểm
        """
        characteristics = []
        query_lower = query.lower()

        # Kiểm tra độ dài
        word_count = len(query.split())
        if word_count > 15:
            characteristics.append("Câu hỏi dài")
        elif word_count < 5:
            characteristics.append("Câu hỏi ngắn")

        # Kiểm tra ngôn ngữ
        if self._is_vietnamese_text(query):
            characteristics.append("Tiếng Việt")
        else:
            characteristics.append("Tiếng Anh")

        # Kiểm tra tính phức tạp
        if any(word in query_lower for word in ['analyze', 'evaluate', 'phân tích', 'đánh giá']):
            characteristics.append("Yêu cầu phân tích")

        if '?' in query:
            characteristics.append("Câu hỏi trực tiếp")

        return characteristics

    def _recommend_search_strategy(self, complexity_score: float, question_type: str) -> Dict[str, Any]:
        """
        Đề xuất chiến lược tìm kiếm dựa trên độ phức tạp và loại câu hỏi.

        Args:
            complexity_score: Điểm phức tạp
            question_type: Loại câu hỏi

        Returns:
            Chiến lược tìm kiếm được đề xuất
        """
        strategy = {
            "search_method": "standard",
            "max_depth": 1,
            "max_pages": 5,
            "decompose_query": False,
            "use_multiple_engines": False,
            "use_deep_crawl": False
        }

        # Điều chỉnh dựa trên độ phức tạp
        if complexity_score > 0.7:
            strategy["search_method"] = "adaptive"
            strategy["max_depth"] = 3
            strategy["max_pages"] = 15
            strategy["decompose_query"] = True
            strategy["use_multiple_engines"] = True
            strategy["use_deep_crawl"] = True
        elif complexity_score > 0.4:
            strategy["max_depth"] = 2
            strategy["max_pages"] = 10
            strategy["use_multiple_engines"] = True

        # Điều chỉnh dựa trên loại câu hỏi
        if question_type in ["comparison", "causal"]:
            strategy["max_pages"] = max(strategy["max_pages"], 10)
            strategy["use_multiple_engines"] = True
        elif question_type == "definition":
            strategy["max_pages"] = 5
        elif question_type == "how_to":
            strategy["max_pages"] = 8

        return strategy

    def evaluate_answer_quality(self, query: str, answer: str) -> Dict[str, Any]:
        """
        Đánh giá chất lượng câu trả lời.

        Args:
            query: Câu hỏi gốc
            answer: Câu trả lời cần đánh giá

        Returns:
            Dictionary chứa kết quả đánh giá
        """
        if not answer or not answer.strip():
            return {
                "quality_score": 0.0,
                "accuracy_score": 0.0,
                "completeness_score": 0.0,
                "relevance_score": 0.0,
                "clarity_score": 0.0,
                "explanation": "Câu trả lời trống",
                "strengths": [],
                "weaknesses": ["Không có nội dung"],
                "suggestions": ["Cần cung cấp câu trả lời có nội dung"]
            }

        # Đánh giá các khía cạnh
        accuracy_score = self._evaluate_accuracy(answer, query)
        completeness_score = self._evaluate_completeness(query, answer)
        relevance_score = self._evaluate_relevance(query, answer)
        clarity_score = self._evaluate_clarity(answer)

        # Tính điểm tổng thể
        quality_score = (accuracy_score + completeness_score + relevance_score + clarity_score) / 4

        # Phân tích điểm mạnh và yếu
        strengths, weaknesses, suggestions = self._analyze_answer_quality(
            accuracy_score, completeness_score, relevance_score, clarity_score
        )

        return {
            "quality_score": quality_score,
            "accuracy_score": accuracy_score,
            "completeness_score": completeness_score,
            "relevance_score": relevance_score,
            "clarity_score": clarity_score,
            "explanation": self._generate_quality_explanation(quality_score),
            "strengths": strengths,
            "weaknesses": weaknesses,
            "suggestions": suggestions
        }

    def _extract_domain(self, url: str) -> str:
        """
        Trích xuất tên miền từ URL.
        
        Args:
            url: URL cần trích xuất tên miền
            
        Returns:
            Tên miền đã trích xuất hoặc chuỗi rỗng nếu không thể trích xuất
        """
        try:
            # Chuẩn hóa URL
            if not url.startswith(('http://', 'https://')):
                url = 'http://' + url
                
            # Phân tích URL
            parsed_url = urllib.parse.urlparse(url)
            domain = parsed_url.netloc
            
            # Loại bỏ www nếu có
            domain = re.sub(r'^www\.', '', domain)
            
            return domain.lower() if domain else ""
        except Exception as e:
            logger.warning(f"Không thể trích xuất domain từ URL {url}: {str(e)}")
            return ""
    
    def _get_domain_credibility(self, domain: str) -> Optional[float]:
        """
        Đánh giá độ tin cậy của tên miền.
        
        Args:
            domain: Tên miền cần đánh giá
            
        Returns:
            Điểm tin cậy từ 0.0 đến 1.0, hoặc None nếu không xác định được
        """
        if not domain:
            return None
            
        # Danh sách các tên miền tin cậy cao (có thể mở rộng hoặc tải từ file)
        trusted_domains = {
            # Tin tức quốc tế
            'reuters.com': 0.95,
            'apnews.com': 0.95,
            'bbc.com': 0.90,
            'bbc.co.uk': 0.90,
            'npr.org': 0.88,
            'washingtonpost.com': 0.85,
            'nytimes.com': 0.85,
            'wsj.com': 0.85,
            'bloomberg.com': 0.87,
            'economist.com': 0.87,
            
            # Khoa học
            'nature.com': 0.92,
            'science.org': 0.92,
            'scientificamerican.com': 0.90,
            'nationalgeographic.com': 0.89,
            'smithsonianmag.com': 0.88,
            
            # Các tin tức tổng hợp
            'theguardian.com': 0.83,
            'ft.com': 0.86,
            'time.com': 0.80,
            'theatlantic.com': 0.82,
            'cnn.com': 0.75,
            
            # Nguồn học thuật và chính phủ
            'edu': 0.85,
            'gov': 0.85,
            'who.int': 0.90,
            'nih.gov': 0.90,
            'cdc.gov': 0.90,
            
            # Nguồn Việt Nam
            'vnexpress.net': 0.80,
            'tuoitre.vn': 0.80,
            'thanhnien.vn': 0.80,
            'vietnamnet.vn': 0.75,
            'dantri.com.vn': 0.75,
            'nhandan.vn': 0.80,
            'baomoi.com': 0.70,
            'vtv.vn': 0.85,
            'vov.vn': 0.80,
            'gov.vn': 0.85,
            'moh.gov.vn': 0.85,
            'moet.gov.vn': 0.85
        }
        
        # Danh sách các tên miền không tin cậy (có thể mở rộng hoặc tải từ file)
        untrusted_domains = {
            'theonion.com': 0.10,  # Satirical news
            'clickhole.com': 0.10,  # Satirical news
            'infowars.com': 0.15,
            'naturalnews.com': 0.15,
            'worldnewsdailyreport.com': 0.10,
            'empirenews.net': 0.10,
            'nationalreport.net': 0.10,
            'huzlers.com': 0.10,
            'thedcgazette.com': 0.15,
            'libertywriters.com': 0.15,
            'react365.com': 0.10
        }
        
        # Kiểm tra nếu domain trùng khớp chính xác với danh sách tin cậy
        if domain in trusted_domains:
            return trusted_domains[domain]
            
        # Kiểm tra nếu domain trùng khớp chính xác với danh sách không tin cậy
        if domain in untrusted_domains:
            return untrusted_domains[domain]
            
        # Kiểm tra nếu domain kết thúc bằng một trong các domain tin cậy
        for known_domain, score in trusted_domains.items():
            if domain.endswith(known_domain):
                return score
                
        # Kiểm tra nếu domain kết thúc bằng một trong các domain không tin cậy
        for known_domain, score in untrusted_domains.items():
            if domain.endswith(known_domain):
                return score
                
        # Nếu không tìm thấy, trả về None (không biết)
        return None
    
    def _calculate_query_complexity(self, query: str) -> float:
        """
        Tính toán độ phức tạp của truy vấn.
        
        Args:
            query: Truy vấn cần đánh giá
            
        Returns:
            Điểm độ phức tạp từ 0.0 đến 1.0
        """
        if not query:
            return 0.0
        
        complexity = 0.0
        
        # 1. Độ dài truy vấn
        words = query.split()
        word_count = len(words)
        
        if word_count <= 3:
            complexity += 0.1
        elif word_count <= 6:
            complexity += 0.3
        elif word_count <= 10:
            complexity += 0.5
        elif word_count <= 15:
            complexity += 0.7
        else:
            complexity += 0.9
        
        # 2. Số lượng mệnh đề (ước lượng thông qua dấu phẩy, liên từ)
        clause_indicators = [",", " and ", " or ", " but ", " because ", " since ", 
                            " as ", " if ", " when ", " while ",
                            " và ", " hoặc ", " nhưng ", " bởi vì ", " vì ", " nếu ", 
                            " khi ", " trong khi "]
        
        clause_count = 1  # Ít nhất 1 mệnh đề
        for indicator in clause_indicators:
            clause_count += query.lower().count(indicator)
        
        if clause_count == 1:
            complexity += 0.0
        elif clause_count == 2:
            complexity += 0.2
        elif clause_count == 3:
            complexity += 0.4
        else:
            complexity += 0.6
        
        # 3. Sự hiện diện của từ khóa phức tạp
        complex_keywords = ["compare", "contrast", "analyze", "evaluate", "explain", 
                          "describe", "define", "identify", "discuss", "examine",
                          "so sánh", "phân tích", "đánh giá", "giải thích", "mô tả", 
                          "định nghĩa", "nhận diện", "thảo luận", "kiểm tra"]
        
        keyword_count = 0
        for keyword in complex_keywords:
            if keyword in query.lower():
                keyword_count += 1
                
        if keyword_count >= 2:
            complexity += 0.4
        elif keyword_count == 1:
            complexity += 0.2
        
        # 4. Các từ chỉ câu hỏi phức tạp
        complex_question_indicators = ["why", "how", "what if", "in what way", "to what extent",
                                    "tại sao", "như thế nào", "bằng cách nào", "nếu", "đến mức nào"]
        
        for indicator in complex_question_indicators:
            if indicator in query.lower():
                complexity += 0.3
                break
        
        # 5. Phân tích ngữ nghĩa đơn giản
        query_lower = query.lower()
        
        # Truy vấn tìm kiếm về nhiều khái niệm
        if "and" in query_lower or "or" in query_lower or "vs" in query_lower or "versus" in query_lower or "compared to" in query_lower:
            complexity += 0.2
            
        # Truy vấn tìm kiếm kết quả có điều kiện
        if "if" in query_lower or "when" in query_lower or "where" in query_lower:
            complexity += 0.2
            
        # Truy vấn đòi hỏi phân tích sâu
        if "pros and cons" in query_lower or "advantages and disadvantages" in query_lower or "benefits and drawbacks" in query_lower:
            complexity += 0.3
        
        # Chuẩn hóa điểm số từ 0.0 đến 1.0
        return min(complexity, 1.0)
    
    def _extract_entities(self, query: str) -> List[str]:
        """
        Trích xuất các thực thể từ truy vấn.
        
        Args:
            query: Truy vấn cần phân tích
            
        Returns:
            Danh sách các thực thể
        """
        # Phương pháp đơn giản: tìm các cụm danh từ
        # Trong triển khai thực tế, nên sử dụng NER (Named Entity Recognition)
        
        entities = []
        words = query.split()
        
        # Tìm các từ viết hoa (có thể là tên riêng)
        capitalized_words = []
        for i, word in enumerate(words):
            if len(word) > 0 and word[0].isupper() and i > 0:  # Bỏ qua từ đầu câu
                capitalized_words.append(word)
        
        # Nếu tìm thấy các từ viết hoa liên tiếp, ghép chúng lại
        if capitalized_words:
            current_entity = ""
            for i, word in enumerate(words):
                if word in capitalized_words:
                    if current_entity:
                        current_entity += " " + word
                    else:
                        current_entity = word
                elif current_entity:
                    entities.append(current_entity)
                    current_entity = ""
            
            if current_entity:  # Thêm thực thể cuối cùng nếu có
                entities.append(current_entity)
        
        # Nếu không tìm thấy thực thể qua phương pháp trên
        if not entities:
            # Tìm các cụm từ giữa dấu ngoặc kép
            quote_pattern = r'"([^"]+)"'
            quoted_entities = re.findall(quote_pattern, query)
            entities.extend(quoted_entities)
        
        # Nếu vẫn không tìm thấy, thử tìm các cụm danh từ phổ biến
        if not entities:
            noun_phrases = []
            current_phrase = ""
            
            for word in words:
                if word.lower() in ["the", "a", "an", "this", "that", "these", "those",
                                  "các", "những", "một", "hai", "ba", "nhiều"]:
                    if current_phrase:
                        current_phrase += " " + word
                    else:
                        current_phrase = word
                elif current_phrase:
                    current_phrase += " " + word
                    if word.lower() in ["of", "in", "on", "at", "by", "with", "from", "to",
                                      "của", "trong", "trên", "tại", "bởi", "với", "từ", "đến"]:
                        continue
                    else:
                        noun_phrases.append(current_phrase.strip())
                        current_phrase = ""
            
            if current_phrase:  # Thêm cụm từ cuối cùng nếu có
                noun_phrases.append(current_phrase.strip())
            
            # Loại bỏ các cụm từ quá ngắn
            noun_phrases = [np for np in noun_phrases if len(np.split()) > 1]
            entities.extend(noun_phrases)
        
        return entities
    
    def _extract_main_topic(self, query: str) -> str:
        """
        Trích xuất chủ đề chính từ truy vấn.
        
        Args:
            query: Truy vấn cần phân tích
            
        Returns:
            Chủ đề chính hoặc chuỗi rỗng nếu không tìm thấy
        """
        # Trích xuất các thực thể
        entities = self._extract_entities(query)
        
        if entities:
            # Ưu tiên thực thể dài nhất
            entities.sort(key=len, reverse=True)
            return entities[0]
        
        # Nếu không tìm thấy thực thể, thử tìm cụm từ quan trọng
        words = query.split()
        
        # Loại bỏ các từ hỏi và liên từ ở đầu câu
        question_words = ["what", "when", "where", "which", "who", "whom", "whose", "why", "how",
                         "cái gì", "khi nào", "ở đâu", "nào", "ai", "của ai", "tại sao", "như thế nào"]
        
        for qw in question_words:
            if query.lower().startswith(qw):
                cleaned_query = query[len(qw):].strip()
                if cleaned_query.startswith("is") or cleaned_query.startswith("are") or cleaned_query.startswith("does") or cleaned_query.startswith("do"):
                    cleaned_query = cleaned_query[cleaned_query.find(" ")+1:].strip()
                if cleaned_query.startswith("là") or cleaned_query.startswith("có phải"):
                    cleaned_query = cleaned_query[cleaned_query.find(" ")+1:].strip()
                
                # Lấy tối đa 4 từ tiếp theo làm chủ đề
                return " ".join(cleaned_query.split()[:4])
        
        # Nếu không phải câu hỏi, lấy 3-4 từ đầu tiên
        return " ".join(words[:min(4, len(words))])
    
    def _extract_facets(self, query: str) -> List[str]:
        """
        Trích xuất các khía cạnh từ truy vấn đa khía cạnh.
        
        Args:
            query: Truy vấn cần phân tích
            
        Returns:
            Danh sách các khía cạnh
        """
        query_lower = query.lower()
        facets = []
        
        # Danh sách các khía cạnh phổ biến
        common_facets = [
            "history", "culture", "economy", "politics", "geography", "climate", 
            "population", "language", "religion", "education", "healthcare", "technology",
            "lịch sử", "văn hóa", "kinh tế", "chính trị", "địa lý", "khí hậu",
            "dân số", "ngôn ngữ", "tôn giáo", "giáo dục", "y tế", "công nghệ"
        ]
        
        # Tìm các khía cạnh được đề cập trong câu hỏi
        for facet in common_facets:
            if facet in query_lower:
                facets.append(facet)
        
        # Tìm các khía cạnh được liệt kê (phân tách bởi dấu phẩy hoặc "and")
        facet_list_patterns = [
            r"(?:about|on|regarding|concerning|về|liên quan đến|liên quan)\s+((?:\w+(?:,\s+|\s+and\s+|\s+và\s+))*\w+)",
            r"(?:aspects|khía cạnh|vấn đề|nội dung)\s+(?:of|về|liên quan đến)\s+((?:\w+(?:,\s+|\s+and\s+|\s+và\s+))*\w+)"
        ]
        
        for pattern in facet_list_patterns:
            matches = re.search(pattern, query_lower)
            if matches:
                facet_list = matches.group(1)
                individual_facets = re.split(r',\s+|\s+and\s+|\s+và\s+', facet_list)
                facets.extend(individual_facets)
        
        # Nếu không tìm thấy khía cạnh cụ thể
        if not facets:
            # Thêm một số khía cạnh mặc định dựa trên loại truy vấn
            if "history" in query_lower or "lịch sử" in query_lower:
                facets = ["origin", "development", "important events", "timeline"]
            elif "compare" in query_lower or "so sánh" in query_lower:
                facets = ["similarities", "differences", "advantages", "disadvantages"]
            elif "effect" in query_lower or "impact" in query_lower or "ảnh hưởng" in query_lower:
                facets = ["positive effects", "negative effects", "long-term impact", "solutions"]
        
        return facets
    
    def _extract_keywords(self, query: str) -> List[str]:
        """
        Trích xuất từ khóa quan trọng từ truy vấn.
        
        Args:
            query: Truy vấn cần phân tích
            
        Returns:
            Danh sách các từ khóa quan trọng
        """
        # Danh sách stopwords (từ không quan trọng)
        stopwords = ["the", "a", "an", "and", "or", "but", "if", "then", "so", "because", 
                   "as", "that", "this", "these", "those", "to", "of", "in", "on", "at", 
                   "by", "with", "about", "for", "from",
                   "các", "những", "và", "hoặc", "nhưng", "nếu", "thì", "vậy", "bởi vì", 
                   "như", "đó", "này", "kia", "của", "trong", "trên", "tại", "bởi", "với", 
                   "về", "cho", "từ"]
        
        # Tách từ và chuyển thành chữ thường
        words = query.lower().split()
        
        # Loại bỏ stopwords và từ quá ngắn
        keywords = [word for word in words if word not in stopwords and len(word) > 2]
        
        # Sắp xếp theo độ dài từ (ưu tiên từ dài hơn, thường quan trọng hơn)
        keywords.sort(key=len, reverse=True)
        
        return keywords
    
    def _decompose_query(self, query: str) -> List[str]:
        """
        Phân rã truy vấn phức tạp thành các truy vấn đơn giản hơn.
        
        Args:
            query: Truy vấn cần phân rã
            
        Returns:
            Danh sách các truy vấn con
        """
        if not query or len(query.strip()) < 10:
            return []
        
        # Bước 1: Kiểm tra độ phức tạp của truy vấn
        complexity_score = self._calculate_query_complexity(query)
        
        # Nếu truy vấn đơn giản, không cần phân rã
        if complexity_score < 0.5:
            return []
        
        # Bước 2: Phân tích cấu trúc câu
        sub_queries = []
        
        # Kiểm tra truy vấn nhiều phần
        # 2.1. Phân tách dựa trên liên từ và dấu câu
        conjunctions = ["and", "or", "but", "as well as", "along with", "including", "plus",
                        "và", "cùng với", "hoặc", "nhưng", "bao gồm", "kèm theo", "cộng với"]
        
        # Chuẩn bị truy vấn để phân tích
        query_lower = query.lower()
        
        # Thử phân tách bằng liên từ
        for conj in conjunctions:
            if f" {conj} " in query_lower:
                parts = re.split(f" {conj} ", query, flags=re.IGNORECASE)
                if len(parts) > 1:
                    for part in parts:
                        part = part.strip()
                        if len(part) > 5:  # Bỏ qua phần quá ngắn
                            sub_queries.append(part)
                    break  # Dừng sau khi tìm thấy liên từ đầu tiên
        
        # 2.2. Phân tách dựa trên dấu phẩy nếu không tìm thấy liên từ
        if not sub_queries and "," in query:
            parts = query.split(",")
            if len(parts) > 1:
                for part in parts:
                    part = part.strip()
                    if len(part) > 5:  # Bỏ qua phần quá ngắn
                        sub_queries.append(part)
        
        # Bước 3: Phân tích ngữ nghĩa câu hỏi
        if not sub_queries:
            # 3.1. Xác định loại câu hỏi
            question_types = []
            
            # Câu hỏi so sánh
            comparison_indicators = ["compare", "difference between", "versus", "vs", "similarities", "differences",
                                   "so sánh", "sự khác nhau giữa", "điểm giống nhau", "điểm khác nhau"]
            
            for indicator in comparison_indicators:
                if indicator in query_lower:
                    question_types.append("comparison")
                    break
            
            # Câu hỏi đa khía cạnh
            multi_facet_indicators = ["aspects of", "features of", "characteristics of", "attributes of",
                                    "khía cạnh của", "đặc điểm của", "thuộc tính của"]
            
            for indicator in multi_facet_indicators:
                if indicator in query_lower:
                    question_types.append("multi_facet")
                    break
            
            # Câu hỏi nguyên nhân-kết quả
            cause_effect_indicators = ["causes of", "effects of", "results in", "leads to", "impacts of",
                                     "nguyên nhân của", "tác động của", "kết quả của", "dẫn đến", "gây ra"]
            
            for indicator in cause_effect_indicators:
                if indicator in query_lower:
                    question_types.append("cause_effect")
                    break
            
            # 3.2. Tạo các sub-queries dựa trên loại câu hỏi
            if "comparison" in question_types:
                # Ví dụ: "So sánh A và B" -> "A là gì", "B là gì", "A và B khác nhau thế nào"
                entities = self._extract_entities(query)
                if len(entities) >= 2:
                    for entity in entities[:2]:  # Chỉ lấy 2 thực thể đầu tiên
                        sub_queries.append(f"{entity} là gì")
                    sub_queries.append(f"{entities[0]} và {entities[1]} khác nhau thế nào")
            
            elif "multi_facet" in question_types:
                # Ví dụ: "Giới thiệu về lịch sử, văn hóa và ẩm thực của Việt Nam"
                topic = self._extract_main_topic(query)
                facets = self._extract_facets(query)
                
                if topic and facets:
                    for facet in facets:
                        sub_queries.append(f"{facet} của {topic}")
            
            elif "cause_effect" in question_types:
                # Ví dụ: "Nguyên nhân và hậu quả của biến đổi khí hậu"
                topic = self._extract_main_topic(query)
                if topic:
                    sub_queries.append(f"Nguyên nhân của {topic}")
                    sub_queries.append(f"Hậu quả của {topic}")
        
        # Bước 4: Tạo truy vấn bổ sung cho câu hỏi phức tạp
        if not sub_queries and len(query.split()) >= 8:
            # Trích xuất từ khóa chính và tạo truy vấn đơn giản hơn
            keywords = self._extract_keywords(query)
            if len(keywords) >= 2:
                sub_queries.append(" ".join(keywords[:3]))
        
        # Bước 5: Loại bỏ truy vấn trùng lặp và quá ngắn
        unique_queries = []
        seen = set()
        
        for sq in sub_queries:
            sq_normalized = sq.lower().strip()
            if sq_normalized not in seen and len(sq_normalized) > 5:
                seen.add(sq_normalized)
                unique_queries.append(sq)
        
        # Giới hạn số lượng sub-queries
        max_sub_queries = 3
        return unique_queries[:max_sub_queries]
    
    def _evaluate_factual_accuracy(self, content: str, query: str = None, sources: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Đánh giá độ chính xác của thông tin dựa trên nội dung và nguồn.
        
        Args:
            content: Nội dung cần đánh giá
            query: Truy vấn gốc (tùy chọn)
            sources: Danh sách các nguồn tham khảo (tùy chọn)
            
        Returns:
            Dict chứa kết quả đánh giá
        """
        result = {
            "score": 0.0,
            "confidence": 0.0,
            "explanation": "",
            "issues": [],
            "factual_statements": [],
            "uncertain_statements": [],
            "contradictions": []
        }
        
        # Kiểm tra nếu content trống
        if not content or len(content.strip()) < 10:
            result["explanation"] = "Nội dung quá ngắn để đánh giá"
            return result
            
        # Trích xuất các phát biểu thực tế từ nội dung
        statements = self._extract_factual_statements(content)
        
        # Kiểm tra các phát biểu với nguồn tham khảo nếu có
        if sources and len(sources) > 0:
            # Kết hợp nội dung từ các nguồn
            sources_content = ""
            for source in sources:
                if "content" in source and isinstance(source["content"], dict) and "text" in source["content"]:
                    sources_content += source["content"]["text"] + "\n\n"
                elif "content" in source and isinstance(source["content"], str):
                    sources_content += source["content"] + "\n\n"
                elif "snippet" in source:
                    sources_content += source["snippet"] + "\n\n"
                    
            # Phân tích mức độ nhất quán giữa phát biểu và nguồn
            result["factual_statements"] = []
            result["uncertain_statements"] = []
            result["contradictions"] = []
            
            for statement in statements:
                # Kiểm tra xem phát biểu có trong nguồn không
                support_level = self._check_statement_support(statement, sources_content)
                
                if support_level > 0.7:  # Phát biểu được hỗ trợ mạnh mẽ
                    result["factual_statements"].append(statement)
                elif 0.3 <= support_level <= 0.7:  # Phát biểu có một số hỗ trợ
                    result["uncertain_statements"].append(statement)
                else:  # Phát biểu không được hỗ trợ hoặc mâu thuẫn
                    # Kiểm tra thêm xem có mâu thuẫn không
                    if self._check_contradiction(statement, sources_content):
                        result["contradictions"].append(statement)
                    else:
                        result["uncertain_statements"].append(statement)
                        
            # Tính điểm dựa trên tỷ lệ phát biểu chính xác
            total_statements = len(statements)
            if total_statements > 0:
                factual_ratio = len(result["factual_statements"]) / total_statements
                uncertain_penalty = len(result["uncertain_statements"]) / total_statements * 0.5
                contradiction_penalty = len(result["contradictions"]) / total_statements * 2
                
                # Công thức tính điểm: factual_ratio - contradiction_penalty - uncertain_penalty
                raw_score = factual_ratio - contradiction_penalty - uncertain_penalty
                result["score"] = max(0.0, min(1.0, raw_score))
                
                # Độ tin cậy dựa trên số lượng statements
                result["confidence"] = min(0.95, 0.5 + 0.05 * total_statements)
            else:
                result["score"] = 0.5  # Điểm trung bình nếu không có statement
                result["confidence"] = 0.3  # Độ tin cậy thấp
                
            # Thêm giải thích
            if result["score"] >= 0.8:
                result["explanation"] = "Nội dung có độ chính xác cao và được hỗ trợ tốt bởi các nguồn"
            elif result["score"] >= 0.6:
                result["explanation"] = "Nội dung phần lớn chính xác với một số phát biểu chưa xác minh"
            elif result["score"] >= 0.4:
                result["explanation"] = "Nội dung có sự kết hợp giữa các phát biểu chính xác và chưa xác minh"
            else:
                result["explanation"] = "Nội dung có vấn đề nghiêm trọng về tính chính xác hoặc mâu thuẫn"
                
        # Nếu không có nguồn, sử dụng phương pháp phân tích ngôn ngữ
        else:
            # Phân tích tính mạch lạc của nội dung
            coherence_score = 0.7  # Giá trị mặc định trong trường hợp không có phương thức đánh giá
            
            # Kiểm tra các từ chỉ sự không chắc chắn
            uncertainty_words = ["maybe", "perhaps", "possibly", "might", "could", "potentially",
                              "có thể", "có lẽ", "dường như", "hình như", "nghe nói", "theo đồn"]
            
            uncertainty_count = sum(1 for word in uncertainty_words if word in content.lower())
            uncertainty_score = min(1.0, uncertainty_count / 10)  # Giới hạn ở 1.0
            
            # Kiểm tra các từ ngữ cực đoan
            extreme_words = ["always", "never", "all", "none", "definitely", "absolutely", "certainly",
                          "luôn luôn", "không bao giờ", "tất cả", "không ai", "chắc chắn", "tuyệt đối"]
            
            extreme_count = sum(1 for word in extreme_words if word in content.lower())
            extreme_score = min(1.0, extreme_count / 10)  # Giới hạn ở 1.0
            
            # Tính toán điểm dựa trên các yếu tố phân tích
            result["score"] = 0.5 + (coherence_score * 0.2) - (uncertainty_score * 0.2) - (extreme_score * 0.2)
            result["score"] = max(0.0, min(1.0, result["score"]))
            
            # Đặt độ tin cậy thấp hơn vì không có nguồn
            result["confidence"] = 0.4
            
            # Thêm giải thích
            result["explanation"] = "Đánh giá dựa trên phân tích ngôn ngữ mà không có nguồn tham khảo"
            
            # Phân tách câu để tạo statements
            sentences = re.split(r'[.!?]', content)
            sentences = [s.strip() for s in sentences if len(s.strip()) > 10]
            
            # Phân loại các câu thành factual hoặc uncertain
            result["factual_statements"] = [s for s in sentences if not any(uw in s.lower() for uw in uncertainty_words)]
            result["uncertain_statements"] = [s for s in sentences if any(uw in s.lower() for uw in uncertainty_words)]
            
        # Thêm các vấn đề nếu có
        if result["contradictions"]:
            result["issues"].append("Nội dung chứa phát biểu mâu thuẫn với nguồn")
            
        if len(result["uncertain_statements"]) > len(result["factual_statements"]) and len(statements) > 3:
            result["issues"].append("Nội dung chứa nhiều phát biểu chưa xác minh hơn phát biểu đã xác minh")
            
        if result["score"] < 0.4:
            result["issues"].append("Độ chính xác tổng thể thấp")
            
        return result
    
    def _extract_factual_statements(self, content: str) -> List[str]:
        """
        Trích xuất các phát biểu thực tế từ nội dung.
        
        Args:
            content: Nội dung cần phân tích
            
        Returns:
            Danh sách các phát biểu thực tế
        """
        # Phân tách nội dung thành các câu
        sentences = re.split(r'[.!?]', content)
        sentences = [s.strip() for s in sentences if len(s.strip()) > 10]
        
        # Lọc các câu có khả năng là phát biểu thực tế
        factual_statements = []
        
        for sentence in sentences:
            # Loại bỏ các câu hỏi
            if sentence.endswith("?") or any(qw in sentence.lower() for qw in ["what", "who", "where", "when", "why", "how"]):
                continue
                
            # Loại bỏ các phát biểu ý kiến
            opinion_indicators = ["i think", "i believe", "in my opinion", "i feel", "personally",
                               "tôi nghĩ", "tôi tin", "theo ý kiến của tôi", "tôi cảm thấy", "cá nhân tôi"]
            
            if any(oi in sentence.lower() for oi in opinion_indicators):
                continue
                
            # Loại bỏ các câu quá ngắn hoặc quá dài
            word_count = len(sentence.split())
            if word_count < 5 or word_count > 50:
                continue
                
            # Thêm vào danh sách phát biểu thực tế
            factual_statements.append(sentence)
        
        return factual_statements
    
    def _check_statement_support(self, statement: str, sources_content: str) -> float:
        """
        Kiểm tra mức độ hỗ trợ của nguồn tham khảo cho một phát biểu.
        
        Args:
            statement: Phát biểu cần kiểm tra
            sources_content: Nội dung từ các nguồn tham khảo
            
        Returns:
            Điểm hỗ trợ từ 0.0 đến 1.0
        """
        if not statement or not sources_content:
            return 0.0
            
        # Trích xuất từ khóa từ phát biểu
        keywords = self._extract_keywords(statement)
        
        # Đếm số từ khóa xuất hiện trong nguồn
        keyword_count = sum(1 for kw in keywords if kw.lower() in sources_content.lower())
        
        # Tính tỷ lệ từ khóa được hỗ trợ
        if not keywords:
            return 0.0
            
        keyword_ratio = keyword_count / len(keywords)
        
        # Kiểm tra sự xuất hiện của cụm từ
        phrases = self._extract_key_phrases(statement)
        phrase_count = sum(1 for p in phrases if p.lower() in sources_content.lower())
        phrase_ratio = phrase_count / len(phrases) if phrases else 0.0
        
        # Tính điểm hỗ trợ tổng hợp (trọng số: 60% từ khóa, 40% cụm từ)
        return keyword_ratio * 0.6 + phrase_ratio * 0.4
    
    def _extract_key_phrases(self, text: str) -> List[str]:
        """
        Trích xuất các cụm từ quan trọng từ văn bản.
        
        Args:
            text: Văn bản cần phân tích
            
        Returns:
            Danh sách các cụm từ quan trọng
        """
        # Phân tách thành các từ
        words = text.split()
        
        # Tạo các cụm từ 2-3 từ
        phrases = []
        
        # Cụm 2 từ
        for i in range(len(words) - 1):
            phrase = ' '.join(words[i:i+2])
            if len(phrase) > 5:
                phrases.append(phrase)
                
        # Cụm 3 từ
        for i in range(len(words) - 2):
            phrase = ' '.join(words[i:i+3])
            if len(phrase) > 8:
                phrases.append(phrase)
                
        return phrases
    
    def _check_contradiction(self, statement: str, sources_content: str) -> bool:
        """
        Kiểm tra xem phát biểu có mâu thuẫn với nguồn không.
        
        Args:
            statement: Phát biểu cần kiểm tra
            sources_content: Nội dung từ các nguồn tham khảo
            
        Returns:
            True nếu có mâu thuẫn, False nếu không
        """
        # Các từ khóa phủ định
        negation_words = ["not", "no", "never", "nobody", "nothing", "neither", "nor",
                       "không", "chẳng", "không phải", "không bao giờ", "chưa từng"]
        
        # Tách statement thành các từ
        statement_words = statement.lower().split()
        
        # Kiểm tra xem statement có chứa từ phủ định không
        has_negation = any(neg in statement_words for neg in negation_words)
        
        # Nếu statement có phủ định, tạo phiên bản không phủ định
        if has_negation:
            # Tạo phiên bản không phủ định (loại bỏ từ phủ định đầu tiên)
            for neg in negation_words:
                if neg in statement_words:
                    non_negated = ' '.join([w for w in statement_words if w != neg])
                    
                    # Kiểm tra xem phiên bản không phủ định có xuất hiện trong nguồn không
                    if non_negated in sources_content.lower():
                        return True
        else:
            # Nếu statement không có phủ định, tạo phiên bản phủ định
            for neg in negation_words[:5]:  # Chỉ sử dụng 5 từ phủ định tiếng Anh
                negated = neg + " " + ' '.join(statement_words)
                
                # Kiểm tra xem phiên bản phủ định có xuất hiện trong nguồn không
                if negated in sources_content.lower():
                    return True
                    
        # Kiểm tra các mẫu mâu thuẫn cụ thể (ví dụ: A nhưng không phải A)
        contradiction_patterns = [
            r'is not (.*), but is (.*)',
            r'is (.*), not (.*)',
            r'không phải (.*) mà là (.*)',
            r'là (.*), không phải (.*)'
        ]
        
        for pattern in contradiction_patterns:
            matches = re.search(pattern, sources_content.lower())
            if matches and matches.group(1) in statement.lower() and matches.group(2) in statement.lower():
                return True
                
        return False
    
    def _evaluate_relevance(self, content: str, query: str) -> Dict[str, Any]:
        """
        Đánh giá độ liên quan của nội dung với truy vấn.
        
        Args:
            content: Nội dung cần đánh giá
            query: Truy vấn gốc
            
        Returns:
            Dict chứa kết quả đánh giá độ liên quan
        """
        result = {
            "relevance_score": 0.0,
            "key_term_coverage": 0.0,
            "topic_alignment": 0.0,
            "semantic_similarity": 0.0,
            "direct_answer": False,
            "explanation": "",
            "relevant_sections": [],
            "missing_aspects": []
        }
        
        # Kiểm tra nếu content hoặc query trống
        if not content or not query or len(content.strip()) < 10 or len(query.strip()) < 2:
            result["explanation"] = "Không thể đánh giá do nội dung hoặc truy vấn quá ngắn"
            return result
            
        # Bước 1: Đánh giá độ phủ của từ khóa
        query_keywords = self._extract_keywords(query)
        
        # Tính tỷ lệ từ khóa xuất hiện trong nội dung
        if query_keywords:
            matched_keywords = sum(1 for kw in query_keywords if kw.lower() in content.lower())
            key_term_coverage = matched_keywords / len(query_keywords)
            result["key_term_coverage"] = key_term_coverage
        else:
            result["key_term_coverage"] = 0.0
            
        # Bước 2: Đánh giá sự phù hợp về chủ đề
        # Trích xuất chủ đề từ truy vấn
        query_topic = self._extract_main_topic(query)
        
        # Kiểm tra xem chủ đề có xuất hiện trong nội dung không
        if query_topic:
            topic_frequency = content.lower().count(query_topic.lower())
            topic_alignment = min(1.0, topic_frequency / 3)  # Giới hạn ở 1.0
            result["topic_alignment"] = topic_alignment
        else:
            result["topic_alignment"] = 0.0
            
        # Bước 3: Đánh giá mức độ trả lời trực tiếp
        # Phân tích loại câu hỏi
        question_types = []
        question_words = ["what", "when", "where", "who", "why", "how", "which", 
                        "cái gì", "khi nào", "ở đâu", "ai", "tại sao", "như thế nào", "nào"]
        
        query_lower = query.lower()
        
        # Xác định loại câu hỏi
        for qw in question_words:
            if query_lower.startswith(qw) or f" {qw} " in query_lower:
                if qw in ["what", "cái gì"]:
                    question_types.append("definition")
                elif qw in ["when", "khi nào"]:
                    question_types.append("time")
                elif qw in ["where", "ở đâu"]:
                    question_types.append("location")
                elif qw in ["who", "ai"]:
                    question_types.append("person")
                elif qw in ["why", "tại sao"]:
                    question_types.append("reason")
                elif qw in ["how", "như thế nào"]:
                    question_types.append("method")
                elif qw in ["which", "nào"]:
                    question_types.append("choice")
                break
                
        # Các mẫu câu trả lời phù hợp với từng loại câu hỏi
        answer_patterns = {
            "definition": [r'(?:is|are|refers to|means|là|được định nghĩa|có nghĩa) (?:a|an|the|một|các|những)?', 
                        r'(?:can be defined|được hiểu|được xem) as'],
            "time": [r'(?:in|on|at|during|vào|trong) (?:the year|năm)? \d+', 
                   r'(?:yesterday|today|tomorrow|hôm qua|hôm nay|ngày mai)', 
                   r'(?:morning|afternoon|evening|sáng|chiều|tối|đêm)'],
            "location": [r'(?:in|on|at|near|vào|tại|gần) (?:the|một|các)?', 
                       r'located (?:in|on|at|near|vào|tại|gần)'],
            "person": [r'(?:he|she|they|người|ông|bà|anh|chị|họ) (?:is|are|was|were|là|đã|sẽ|đang)',
                     r'born (?:in|on|at|vào|tại) (?:the year|năm)? \d+'],
            "reason": [r'(?:because|since|as|due to|bởi vì|vì|do|tại)', 
                     r'(?:the reason|lý do|nguyên nhân) (?:is|was|là)'],
            "method": [r'(?:by|through|using|với|bằng cách|thông qua|sử dụng)', 
                     r'(?:first|second|third|next|then|đầu tiên|thứ hai|thứ ba|tiếp theo|sau đó)'],
            "choice": [r'(?:better|best|worse|worst|preferred|tốt hơn|tốt nhất|kém hơn|kém nhất|ưu tiên)', 
                     r'(?:chosen|selected|recommended|được chọn|được lựa chọn|được khuyến nghị)']
        }
        
        # Kiểm tra xem nội dung có chứa mẫu câu trả lời phù hợp không
        has_direct_answer = False
        for qt in question_types:
            if qt in answer_patterns:
                for pattern in answer_patterns[qt]:
                    if re.search(pattern, content.lower()):
                        has_direct_answer = True
                        break
                if has_direct_answer:
                    break
                    
        result["direct_answer"] = has_direct_answer
        
        # Bước 4: Đánh giá độ tương đồng ngữ nghĩa (phiên bản đơn giản)
        # Trích xuất các khía cạnh từ truy vấn
        query_facets = self._extract_facets(query)
        
        # Kiểm tra xem nội dung đề cập đến các khía cạnh nào
        if query_facets:
            matched_facets = sum(1 for facet in query_facets if facet.lower() in content.lower())
            semantic_similarity = matched_facets / len(query_facets) if query_facets else 0.0
            result["semantic_similarity"] = semantic_similarity
            
            # Xác định các khía cạnh còn thiếu
            result["missing_aspects"] = [facet for facet in query_facets if facet.lower() not in content.lower()]
        else:
            result["semantic_similarity"] = result["key_term_coverage"]  # Dùng độ phủ từ khóa nếu không có khía cạnh
            
        # Bước 5: Tìm các đoạn văn liên quan nhất
        sentences = re.split(r'[.!?]', content)
        sentences = [s.strip() for s in sentences if len(s.strip()) > 10]
        
        # Tính điểm liên quan cho từng câu
        relevant_sentences = []
        for sentence in sentences:
            sentence_relevance = 0.0
            
            # Kiểm tra từ khóa
            if query_keywords:
                matched_kw = sum(1 for kw in query_keywords if kw.lower() in sentence.lower())
                sentence_relevance += (matched_kw / len(query_keywords)) * 0.6
                
            # Kiểm tra chủ đề
            if query_topic and query_topic.lower() in sentence.lower():
                sentence_relevance += 0.4
                
            # Lưu câu có điểm > 0.3
            if sentence_relevance > 0.3:
                relevant_sentences.append({
                    "text": sentence,
                    "relevance": sentence_relevance
                })
                
        # Sắp xếp theo độ liên quan giảm dần và lấy 3 câu liên quan nhất
        relevant_sentences.sort(key=lambda x: x["relevance"], reverse=True)
        result["relevant_sections"] = relevant_sentences[:3]
        
        # Bước 6: Tính điểm liên quan tổng thể
        # Trọng số: 40% độ phủ từ khóa, 25% sự phù hợp chủ đề, 25% độ tương đồng ngữ nghĩa, 10% trả lời trực tiếp
        relevance_score = (
            result["key_term_coverage"] * 0.4 +
            result["topic_alignment"] * 0.25 +
            result["semantic_similarity"] * 0.25 +
            (0.1 if result["direct_answer"] else 0.0)
        )
        
        result["relevance_score"] = max(0.0, min(1.0, relevance_score))
        
        # Thêm giải thích
        if result["relevance_score"] >= 0.8:
            result["explanation"] = "Nội dung rất liên quan đến truy vấn, bao gồm hầu hết các từ khóa và khía cạnh quan trọng"
        elif result["relevance_score"] >= 0.6:
            result["explanation"] = "Nội dung khá liên quan đến truy vấn, bao gồm nhiều từ khóa quan trọng"
        elif result["relevance_score"] >= 0.4:
            result["explanation"] = "Nội dung có một số liên quan đến truy vấn, nhưng thiếu một số khía cạnh quan trọng"
        elif result["relevance_score"] >= 0.2:
            result["explanation"] = "Nội dung có ít liên quan đến truy vấn, chỉ đề cập đến một số từ khóa"
        else:
            result["explanation"] = "Nội dung hầu như không liên quan đến truy vấn"
            
        return result
    
    def _evaluate_completeness(self, content: str, query: str = None, topic: str = None) -> Dict[str, Any]:
        """
        Đánh giá tính đầy đủ của nội dung.
        
        Args:
            content: Nội dung cần đánh giá
            query: Truy vấn gốc (tùy chọn)
            topic: Chủ đề (tùy chọn, được sử dụng nếu không có query)
            
        Returns:
            Dict chứa kết quả đánh giá tính đầy đủ
        """
        result = {
            "completeness_score": 0.0,
            "length_score": 0.0,
            "depth_score": 0.0,
            "breadth_score": 0.0,
            "structure_score": 0.0,
            "missing_elements": [],
            "explanation": "",
            "suggested_improvements": []
        }
        
        # Kiểm tra nếu content trống
        if not content or len(content.strip()) < 10:
            result["explanation"] = "Nội dung quá ngắn để đánh giá"
            return result
            
        # Xác định chủ đề từ query nếu chưa có
        main_topic = topic
        if not main_topic and query:
            main_topic = self._extract_main_topic(query)
            
        # Bước 1: Đánh giá độ dài (length)
        content_length = len(content)
        word_count = len(content.split())
        
        # Đánh giá độ dài dựa trên số từ
        if word_count >= 500:  # Nội dung dài, có khả năng đầy đủ
            length_score = 1.0
        elif word_count >= 300:
            length_score = 0.8
        elif word_count >= 150:
            length_score = 0.6
        elif word_count >= 80:
            length_score = 0.4
        else:
            length_score = 0.2  # Nội dung quá ngắn, có khả năng thiếu
            
        result["length_score"] = length_score
        
        # Nếu quá ngắn, thêm gợi ý cải thiện
        if word_count < 150:
            result["suggested_improvements"].append("Mở rộng nội dung để bao gồm thêm thông tin")
            result["missing_elements"].append("insufficient_length")
            
        # Bước 2: Đánh giá chiều sâu (depth)
        # Phân tích số lượng dữ liệu cụ thể: số liệu, ngày tháng, tên riêng, trích dẫn
        
        # Đếm số liệu
        numbers = len(re.findall(r'\b\d+(?:,\d+)*(?:\.\d+)?\b', content))
        
        # Đếm ngày tháng
        dates = len(re.findall(r'\b\d{1,2}[-/]\d{1,2}[-/]\d{2,4}\b|\b(?:January|February|March|April|May|June|July|August|September|October|November|December|Jan|Feb|Mar|Apr|Jun|Jul|Aug|Sep|Oct|Nov|Dec|Tháng \d{1,2})\s+\d{1,2}(?:st|nd|rd|th)?,?\s+\d{2,4}\b', content))
        
        # Đếm tên riêng (ước lượng bằng cách đếm từ viết hoa không ở đầu câu)
        capitalized_words = len(re.findall(r'(?<!^)(?<!\. )[A-Z][a-z]+', content))
        
        # Đếm trích dẫn
        quotes = len(re.findall(r'"([^"]+)"|\(([^)]+)\)', content))
        
        # Tính điểm chiều sâu
        specific_data_count = numbers + dates + min(capitalized_words, 20) + quotes
        
        if specific_data_count >= 15:
            depth_score = 1.0
        elif specific_data_count >= 10:
            depth_score = 0.8
        elif specific_data_count >= 5:
            depth_score = 0.6
        elif specific_data_count >= 2:
            depth_score = 0.4
        else:
            depth_score = 0.2
            
        result["depth_score"] = depth_score
        
        # Nếu thiếu chi tiết, thêm gợi ý cải thiện
        if specific_data_count < 5:
            result["suggested_improvements"].append("Bổ sung thêm dữ liệu cụ thể: số liệu, ngày tháng, tên riêng, trích dẫn")
            result["missing_elements"].append("insufficient_details")
            
        # Bước 3: Đánh giá chiều rộng (breadth)
        # Xác định có bao nhiêu khía cạnh khác nhau được đề cập
        
        # Nếu có query, trích xuất các khía cạnh từ query
        expected_aspects = []
        if query:
            expected_aspects = self._extract_facets(query)
            
        # Nếu không có khía cạnh từ query, sử dụng các khía cạnh phổ biến dựa trên chủ đề
        if not expected_aspects and main_topic:
            # Ánh xạ chủ đề sang các khía cạnh phổ biến
            common_aspects = {
                "country": ["history", "geography", "economy", "culture", "politics", "demographics"],
                "person": ["early life", "career", "achievements", "personal life", "legacy"],
                "organization": ["history", "structure", "operations", "achievements", "controversy"],
                "event": ["causes", "timeline", "participants", "outcomes", "significance"],
                "product": ["features", "specifications", "history", "usage", "comparison"],
                "concept": ["definition", "history", "applications", "examples", "importance"],
                "process": ["steps", "requirements", "timeline", "advantages", "limitations"],
                "problem": ["causes", "symptoms", "solutions", "prevention", "impact"]
            }
            
            # Phát hiện loại chủ đề cơ bản
            topic_type = "concept"  # Mặc định
            topic_lower = main_topic.lower()
            
            # Thử xác định loại chủ đề
            if any(kw in topic_lower for kw in ["country", "nation", "city", "region", "quốc gia", "thành phố", "vùng", "tỉnh"]):
                topic_type = "country"
            elif any(kw in topic_lower for kw in ["person", "người", "ông", "bà", "anh", "chị"]):
                topic_type = "person"
            elif any(kw in topic_lower for kw in ["company", "organization", "association", "công ty", "tổ chức", "hiệp hội"]):
                topic_type = "organization"
            elif any(kw in topic_lower for kw in ["event", "incident", "ceremony", "sự kiện", "biến cố", "lễ"]):
                topic_type = "event"
            elif any(kw in topic_lower for kw in ["product", "device", "tool", "sản phẩm", "thiết bị", "công cụ"]):
                topic_type = "product"
            elif any(kw in topic_lower for kw in ["process", "method", "technique", "quy trình", "phương pháp", "kỹ thuật"]):
                topic_type = "process"
            elif any(kw in topic_lower for kw in ["problem", "issue", "challenge", "vấn đề", "thách thức", "khó khăn"]):
                topic_type = "problem"
                
            expected_aspects = common_aspects.get(topic_type, ["definition", "examples", "applications"])
            
        # Đếm số khía cạnh được đề cập trong nội dung
        covered_aspects = []
        missing_aspects = []
        
        for aspect in expected_aspects:
            if aspect.lower() in content.lower():
                covered_aspects.append(aspect)
            else:
                missing_aspects.append(aspect)
                
        # Thêm vào kết quả
        result["missing_elements"].extend(missing_aspects)
        
        # Tính điểm chiều rộng
        if expected_aspects:
            breadth_score = len(covered_aspects) / len(expected_aspects)
        else:
            # Nếu không có expected aspects, đánh giá dựa trên cấu trúc và phân đoạn
            paragraphs = content.split('\n\n')
            if len(paragraphs) >= 5:
                breadth_score = 1.0
            elif len(paragraphs) >= 3:
                breadth_score = 0.7
            else:
                breadth_score = 0.4
                
        result["breadth_score"] = breadth_score
        
        # Nếu thiếu khía cạnh, thêm gợi ý cải thiện
        if missing_aspects:
            missing_aspects_str = ", ".join(missing_aspects[:3])
            result["suggested_improvements"].append(f"Bổ sung thêm các khía cạnh: {missing_aspects_str}")
            
        # Bước 4: Đánh giá cấu trúc (structure)
        # Kiểm tra có bao nhiêu yếu tố cấu trúc: đoạn văn, tiêu đề, danh sách, etc.
        
        # Đếm số đoạn văn
        paragraphs = len([p for p in content.split('\n\n') if len(p.strip()) > 0])
        
        # Đếm số tiêu đề (dòng ngắn và độc lập)
        headings = len(re.findall(r'(?:^|\n)([A-Z][A-Za-z0-9\s]{1,50})(?:\n|$)', content))
        
        # Đếm số danh sách (dòng bắt đầu bằng dấu gạch đầu dòng hoặc số)
        list_items = len(re.findall(r'(?:^|\n)[\-\*\•\d+\)\.][ \t]+\w+', content))
        
        # Tính điểm cấu trúc
        structure_elements = paragraphs + headings + list_items
        
        if structure_elements >= 10:
            structure_score = 1.0
        elif structure_elements >= 7:
            structure_score = 0.8
        elif structure_elements >= 4:
            structure_score = 0.6
        elif structure_elements >= 2:
            structure_score = 0.4
        else:
            structure_score = 0.2
            
        result["structure_score"] = structure_score
        
        # Nếu cấu trúc kém, thêm gợi ý cải thiện
        if paragraphs < 3:
            result["suggested_improvements"].append("Cải thiện cấu trúc bài viết: thêm đoạn văn, tiêu đề, danh sách")
            result["missing_elements"].append("poor_structure")
            
        # Bước 5: Tính điểm đầy đủ tổng thể
        # Trọng số: 25% độ dài, 30% chiều sâu, 30% chiều rộng, 15% cấu trúc
        completeness_score = (
            result["length_score"] * 0.25 +
            result["depth_score"] * 0.30 +
            result["breadth_score"] * 0.30 +
            result["structure_score"] * 0.15
        )
        
        result["completeness_score"] = max(0.0, min(1.0, completeness_score))
        
        # Thêm giải thích
        if result["completeness_score"] >= 0.8:
            result["explanation"] = "Nội dung rất đầy đủ, bao gồm đủ chiều sâu và chiều rộng"
        elif result["completeness_score"] >= 0.6:
            result["explanation"] = "Nội dung khá đầy đủ, nhưng có thể cải thiện thêm một số khía cạnh"
        elif result["completeness_score"] >= 0.4:
            result["explanation"] = "Nội dung còn thiếu một số yếu tố quan trọng, cần bổ sung thêm"
        else:
            result["explanation"] = "Nội dung không đầy đủ, thiếu nhiều yếu tố quan trọng"
            
        return result
    
    def _evaluate_source_diversity(self, sources: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Đánh giá sự đa dạng của nguồn thông tin.
        
        Args:
            sources: Danh sách các nguồn thông tin
            
        Returns:
            Dict chứa kết quả đánh giá sự đa dạng nguồn
        """
        result = {
            "diversity_score": 0.0,
            "domain_diversity_score": 0.0,
            "type_diversity_score": 0.0,
            "perspective_diversity_score": 0.0,
            "freshness_score": 0.0,
            "domain_distribution": {},
            "type_distribution": {},
            "date_distribution": {},
            "perspective_analysis": {},
            "explanation": "",
            "suggestions": []
        }
        
        # Kiểm tra nếu không có nguồn
        if not sources or len(sources) == 0:
            result["explanation"] = "Không có nguồn thông tin để đánh giá"
            result["suggestions"].append("Thêm nguồn thông tin từ nhiều trang web khác nhau")
            return result
            
        # Bước 1: Phân tích sự đa dạng về domain
        domains = []
        domain_counts = {}
        
        for source in sources:
            # Trích xuất domain từ URL
            url = source.get("url", "")
            if url:
                domain = self._extract_domain(url)
                domains.append(domain)
                
                # Đếm số lần xuất hiện của mỗi domain
                domain_counts[domain] = domain_counts.get(domain, 0) + 1
                
        # Tính điểm đa dạng domain
        # Nếu tất cả các nguồn đều từ các domain khác nhau, điểm là 1.0
        unique_domains = len(set(domains))
        
        if len(domains) > 0:
            # Điểm phân bố domain (dựa trên số lượng domain duy nhất)
            domain_diversity_score = unique_domains / len(domains)
            
            # Tính chỉ số phân tán (diversity index)
            # Sử dụng chỉ số Simpson's Diversity Index: 1 - sum((n/N)^2)
            # n: số lượng của mỗi domain, N: tổng số nguồn
            total_sources = len(domains)
            simpson_index = 1.0 - sum((count / total_sources) ** 2 for count in domain_counts.values())
            
            # Kết hợp hai điểm số (trọng số: 40% số lượng domain duy nhất, 60% phân bố)
            domain_diversity_score = 0.4 * domain_diversity_score + 0.6 * simpson_index
        else:
            domain_diversity_score = 0.0
            
        result["domain_diversity_score"] = domain_diversity_score
        result["domain_distribution"] = domain_counts
        
        # Bước 2: Phân tích sự đa dạng về loại nguồn
        # Phân loại nguồn dựa trên domain
        source_types = {
            "news": ["news", "bbc", "cnn", "reuters", "guardian", "nytimes", "washingtonpost", "foxnews", 
                   "vnexpress", "tuoitre", "thanhnien", "dantri", "vietnamnet", "baomoi", "vtv", "vov"],
            "academic": ["edu", "ac.uk", "academia", "researchgate", "scholar", "sciencedirect", "ieee", 
                       "springer", "nature", "science", "wiley", "ncbi", "pubmed", "jstor"],
            "government": ["gov", "europa.eu", "un.org", "who.int", "worldbank.org", "imf.org", 
                         "gov.vn", "chinhphu.vn"],
            "encyclopedia": ["wikipedia", "britannica", "dictionary", "encyclopedia", "wiki"],
            "blog": ["blog", "medium", "wordpress", "blogger", "tumblr", "substack"],
            "social_media": ["facebook", "twitter", "linkedin", "reddit", "instagram", "tiktok", "youtube"],
            "commercial": ["com", "co", "biz", "shop", "store", "amazon", "ebay"]
        }
        
        # Đếm số lượng của mỗi loại nguồn
        type_counts = {}
        
        for domain in domains:
            source_type = "other"  # Mặc định
            
            # Kiểm tra domain thuộc loại nào
            for type_name, type_keywords in source_types.items():
                if any(kw in domain for kw in type_keywords):
                    source_type = type_name
                    break
                    
            # Đếm số lượng của mỗi loại
            type_counts[source_type] = type_counts.get(source_type, 0) + 1
            
        # Tính điểm đa dạng loại nguồn
        unique_types = len(type_counts)
        
        if len(domains) > 0:
            # Điểm đa dạng dựa trên số loại nguồn
            # Lý tưởng nhất là có ít nhất 3 loại nguồn khác nhau
            type_count_score = min(1.0, unique_types / 3)
            
            # Tính chỉ số phân tán Simpson cho loại nguồn
            total_sources = len(domains)
            simpson_index = 1.0 - sum((count / total_sources) ** 2 for count in type_counts.values())
            
            # Kết hợp hai điểm số (trọng số: 40% số lượng loại, 60% phân bố)
            type_diversity_score = 0.4 * type_count_score + 0.6 * simpson_index
        else:
            type_diversity_score = 0.0
            
        result["type_diversity_score"] = type_diversity_score
        result["type_distribution"] = type_counts
        
        # Bước 3: Phân tích sự đa dạng về thời gian (freshness)
        # Phân tích thời gian xuất bản (nếu có)
        date_counts = {
            "recent": 0,  # < 1 năm
            "moderate": 0,  # 1-3 năm
            "old": 0  # > 3 năm
        }
        
        # Lấy thời gian hiện tại
        current_time = time.time()
        one_year_ago = current_time - (365 * 24 * 60 * 60)
        three_years_ago = current_time - (3 * 365 * 24 * 60 * 60)
        
        for source in sources:
            # Thử lấy thời gian từ các trường khác nhau
            pub_date = None
            
            if "date" in source:
                pub_date = source["date"]
            elif "published_date" in source:
                pub_date = source["published_date"]
            elif "timestamp" in source:
                pub_date = source["timestamp"]
                
            # Nếu có thời gian, phân loại
            if pub_date:
                try:
                    # Thử chuyển đổi sang timestamp
                    if isinstance(pub_date, str):
                        # Xử lý một số định dạng phổ biến
                        formats = [
                            "%Y-%m-%d", "%Y/%m/%d", "%d-%m-%Y", "%d/%m/%Y",
                            "%Y-%m-%d %H:%M:%S", "%Y/%m/%d %H:%M:%S",
                            "%b %d, %Y", "%B %d, %Y", "%d %b %Y", "%d %B %Y"
                        ]
                        
                        for fmt in formats:
                            try:
                                dt = datetime.strptime(pub_date, fmt)
                                pub_timestamp = dt.timestamp()
                                break
                            except:
                                continue
                        else:
                            # Nếu không khớp với bất kỳ định dạng nào
                            pub_timestamp = None
                    elif isinstance(pub_date, (int, float)):
                        pub_timestamp = pub_date
                    else:
                        pub_timestamp = None
                        
                    # Phân loại thời gian
                    if pub_timestamp:
                        if pub_timestamp > one_year_ago:
                            date_counts["recent"] += 1
                        elif pub_timestamp > three_years_ago:
                            date_counts["moderate"] += 1
                        else:
                            date_counts["old"] += 1
                except:
                    # Nếu có lỗi khi xử lý thời gian, bỏ qua
                    pass
                    
        # Tính điểm đa dạng thời gian và độ mới
        total_dated_sources = sum(date_counts.values())
        
        if total_dated_sources > 0:
            # Tỷ lệ nguồn mới
            recent_ratio = date_counts["recent"] / total_dated_sources
            
            # Tính điểm đa dạng thời gian
            # Lý tưởng là có sự kết hợp giữa các nguồn mới và cũ
            has_recent = date_counts["recent"] > 0
            has_moderate = date_counts["moderate"] > 0
            has_old = date_counts["old"] > 0
            
            time_diversity = 0.0
            if has_recent and has_moderate and has_old:
                time_diversity = 1.0
            elif (has_recent and has_moderate) or (has_recent and has_old) or (has_moderate and has_old):
                time_diversity = 0.7
            elif has_recent or has_moderate or has_old:
                time_diversity = 0.3
                
            # Ưu tiên nguồn mới hơn, nhưng vẫn đánh giá cao sự đa dạng
            freshness_score = 0.7 * recent_ratio + 0.3 * time_diversity
        else:
            freshness_score = 0.0
            
        result["freshness_score"] = freshness_score
        result["date_distribution"] = date_counts
        
        # Bước 4: Phân tích sự đa dạng về quan điểm (perspective)
        # Đây là bản ước lượng đơn giản, phân tích thực tế cần NLP nâng cao
        
        # Phân loại nguồn theo khuynh hướng
        perspective_indicators = {
            "left": ["guardian", "nytimes", "washingtonpost", "vox", "msnbc", "huffpost", "buzzfeed"],
            "center": ["reuters", "apnews", "bbc", "npr", "pbs", "abc", "cbs"],
            "right": ["foxnews", "breitbart", "dailycaller", "nypost", "washingtonexaminer", "wsj"],
            "scientific": ["nature", "science", "sciencedirect", "springer", "wiley", "ieee", "cell", "nejm"],
            "educational": ["edu", "ac.uk", ".edu.", "wikipedia", "britannica"],
            "government": ["gov", ".gov.", "un.org", "who.int", "europa.eu"]
        }
        
        # Đếm số lượng của mỗi khuynh hướng
        perspective_counts = {}
        
        for domain in domains:
            perspective = "unknown"  # Mặc định
            
            # Kiểm tra domain thuộc khuynh hướng nào
            for persp, indicators in perspective_indicators.items():
                if any(ind in domain for ind in indicators):
                    perspective = persp
                    break
                    
            # Đếm số lượng của mỗi khuynh hướng
            perspective_counts[perspective] = perspective_counts.get(perspective, 0) + 1
            
        # Tính điểm đa dạng khuynh hướng
        unique_perspectives = len([k for k, v in perspective_counts.items() if k != "unknown" and v > 0])
        
        # Lý tưởng là có ít nhất 3 khuynh hướng khác nhau
        if len(domains) > 0:
            perspective_count_score = min(1.0, unique_perspectives / 3)
            
            # Loại bỏ "unknown" khi tính chỉ số phân tán
            known_sources = len(domains) - perspective_counts.get("unknown", 0)
            
            if known_sources > 0:
                # Tính chỉ số phân tán Simpson cho khuynh hướng (chỉ với khuynh hướng đã biết)
                simpson_index = 1.0 - sum((perspective_counts.get(persp, 0) / known_sources) ** 2 
                                      for persp in perspective_counts if persp != "unknown")
                
                # Kết hợp hai điểm số (trọng số: 40% số lượng khuynh hướng, 60% phân bố)
                perspective_diversity_score = 0.4 * perspective_count_score + 0.6 * simpson_index
            else:
                perspective_diversity_score = 0.0
        else:
            perspective_diversity_score = 0.0
            
        result["perspective_diversity_score"] = perspective_diversity_score
        result["perspective_analysis"] = perspective_counts
        
        # Bước 5: Tính điểm đa dạng tổng thể
        # Trọng số: 40% domain, 25% loại nguồn, 20% khuynh hướng, 15% thời gian
        diversity_score = (
            result["domain_diversity_score"] * 0.4 +
            result["type_diversity_score"] * 0.25 +
            result["perspective_diversity_score"] * 0.20 +
            result["freshness_score"] * 0.15
        )
        
        result["diversity_score"] = max(0.0, min(1.0, diversity_score))
        
        # Thêm giải thích và gợi ý
        if result["diversity_score"] >= 0.8:
            result["explanation"] = "Nguồn thông tin rất đa dạng, bao gồm nhiều domain, loại nguồn và quan điểm khác nhau"
        elif result["diversity_score"] >= 0.6:
            result["explanation"] = "Nguồn thông tin khá đa dạng, nhưng có thể cải thiện thêm về một số khía cạnh"
        elif result["diversity_score"] >= 0.4:
            result["explanation"] = "Nguồn thông tin có độ đa dạng trung bình, cần thêm nhiều nguồn khác nhau"
        else:
            result["explanation"] = "Nguồn thông tin thiếu đa dạng, quá tập trung vào một số ít nguồn"
            
        # Thêm các gợi ý cụ thể
        if unique_domains < 3:
            result["suggestions"].append("Thêm nguồn từ các trang web khác nhau")
            
        if unique_types < 2:
            result["suggestions"].append("Thêm nguồn từ các loại khác nhau (học thuật, tin tức, chính phủ)")
            
        if unique_perspectives < 2:
            result["suggestions"].append("Thêm nguồn có quan điểm đa dạng hơn")
            
        if freshness_score < 0.5:
            result["suggestions"].append("Thêm các nguồn cập nhật gần đây")
            
        return result
    
    def _evaluate_content_richness(self, content: str, query: str = None) -> Dict[str, Any]:
        """
        Đánh giá sự phong phú của nội dung.
        
        Args:
            content: Nội dung cần đánh giá
            query: Truy vấn gốc (tùy chọn)
            
        Returns:
            Dict chứa kết quả đánh giá sự phong phú của nội dung
        """
        result = {
            "richness_score": 0.0,
            "information_density_score": 0.0,
            "vocabulary_richness_score": 0.0,
            "media_enrichment_score": 0.0,
            "structure_complexity_score": 0.0,
            "explanation": "",
            "strengths": [],
            "weaknesses": []
        }
        
        # Kiểm tra nếu content trống
        if not content or len(content.strip()) < 10:
            result["explanation"] = "Nội dung quá ngắn để đánh giá"
            return result
            
        # Bước 1: Đánh giá mật độ thông tin (information density)
        # Đếm các yếu tố thông tin: con số, ngày tháng, tên riêng, thuật ngữ chuyên ngành
        
        # Đếm số lượng
        numbers = len(re.findall(r'\b\d+(?:,\d+)*(?:\.\d+)?\b', content))
        
        # Đếm ngày tháng
        dates = len(re.findall(r'\b\d{1,2}[-/]\d{1,2}[-/]\d{2,4}\b|\b(?:January|February|March|April|May|June|July|August|September|October|November|December|Jan|Feb|Mar|Apr|Jun|Jul|Aug|Sep|Oct|Nov|Dec|Tháng \d{1,2})\s+\d{1,2}(?:st|nd|rd|th)?,?\s+\d{2,4}\b', content))
        
        # Đếm tên riêng (ước lượng bằng cách đếm từ viết hoa không ở đầu câu)
        proper_nouns = len(re.findall(r'(?<!^)(?<!\. )[A-Z][a-z]+', content))
        
        # Đếm trích dẫn
        quotes = len(re.findall(r'"([^"]+)"|\(([^)]+)\)', content))
        
        # Đếm các liên kết
        links = len(re.findall(r'https?://\S+|www\.\S+', content))
        
        # Đếm số từ
        word_count = len(content.split())
        
        # Tính mật độ thông tin
        if word_count > 0:
            info_elements = numbers + dates + min(proper_nouns, 20) + quotes + links
            info_density = info_elements / (word_count / 100)  # Số yếu tố thông tin trên 100 từ
            
            # Chuẩn hóa điểm mật độ thông tin
            if info_density >= 10:
                information_density_score = 1.0
            elif info_density >= 7:
                information_density_score = 0.8
            elif info_density >= 5:
                information_density_score = 0.6
            elif info_density >= 3:
                information_density_score = 0.4
            else:
                information_density_score = 0.2
        else:
            information_density_score = 0.0
            
        result["information_density_score"] = information_density_score
        
        # Thêm điểm mạnh/yếu dựa trên mật độ thông tin
        if information_density_score >= 0.7:
            result["strengths"].append("Nội dung giàu thông tin với nhiều dữ liệu cụ thể")
        elif information_density_score <= 0.3:
            result["weaknesses"].append("Thiếu dữ liệu cụ thể, nội dung có ít yếu tố thông tin")
            
        # Bước 2: Đánh giá sự phong phú của từ vựng (vocabulary richness)
        words = re.findall(r'\b[a-zA-Z\u00C0-\u1EF9]+\b', content.lower())  # Bao gồm cả từ tiếng Việt
        
        # Đếm số từ unique
        unique_words = len(set(words))
        
        # Tính tỷ lệ type-token (TTR)
        if len(words) > 0:
            ttr = unique_words / len(words)
            
            # Chuẩn hóa TTR (type-token ratio)
            # TTR thường giảm khi độ dài văn bản tăng, nên cần điều chỉnh theo độ dài
            if len(words) <= 100:
                normalized_ttr = ttr
            else:
                # Sử dụng Root TTR để chuẩn hóa theo độ dài
                normalized_ttr = unique_words / math.sqrt(len(words))
                normalized_ttr = min(1.0, normalized_ttr / 10)  # Chuẩn hóa về thang điểm 0-1
                
            # Đếm từ hiếm (từ dài)
            long_words = sum(1 for word in words if len(word) > 7)
            long_word_ratio = long_words / len(words) if len(words) > 0 else 0
            
            # Tính điểm phong phú từ vựng tổng hợp
            vocabulary_richness_score = 0.7 * normalized_ttr + 0.3 * min(1.0, long_word_ratio * 5)
        else:
            vocabulary_richness_score = 0.0
            
        result["vocabulary_richness_score"] = vocabulary_richness_score
        
        # Thêm điểm mạnh/yếu dựa trên sự phong phú từ vựng
        if vocabulary_richness_score >= 0.7:
            result["strengths"].append("Sử dụng từ vựng phong phú và đa dạng")
        elif vocabulary_richness_score <= 0.3:
            result["weaknesses"].append("Từ vựng đơn điệu, sử dụng nhiều từ lặp lại")
            
        # Bước 3: Đánh giá sự phong phú về phương tiện (media enrichment)
        # Kiểm tra xem nội dung có chứa hình ảnh, video, biểu đồ, bảng, etc.
        
        # Đếm số lượng hình ảnh
        images = len(re.findall(r'!\[.*?\]\(.*?\)|<img[^>]+>|\.(jpg|jpeg|png|gif|svg|webp)["\s\)]', content, re.IGNORECASE))
        
        # Đếm số lượng video
        videos = len(re.findall(r'<video[^>]+>|youtube\.com\/watch|youtu\.be\/|vimeo\.com\/|<iframe[^>]+youtube|<iframe[^>]+vimeo', content, re.IGNORECASE))
        
        # Đếm bảng và biểu đồ
        tables = len(re.findall(r'<table[^>]*>|(\|[^|]*\|[^|]*\|)|<tr>|<th>', content, re.IGNORECASE))
        
        # Đếm danh sách
        lists = len(re.findall(r'<[uo]l>|<li>|^[\*\-\+]\s|\d+\.\s', content, re.MULTILINE))
        
        # Tính điểm phong phú phương tiện
        media_elements = images + videos * 3 + tables * 2 + min(lists, 5)
        
        if media_elements >= 10:
            media_enrichment_score = 1.0
        elif media_elements >= 6:
            media_enrichment_score = 0.8
        elif media_elements >= 3:
            media_enrichment_score = 0.6
        elif media_elements >= 1:
            media_enrichment_score = 0.4
        else:
            media_enrichment_score = 0.0
            
        result["media_enrichment_score"] = media_enrichment_score
        
        # Thêm điểm mạnh/yếu dựa trên sự phong phú phương tiện
        if media_enrichment_score >= 0.6:
            result["strengths"].append("Nội dung phong phú với hình ảnh, biểu đồ hoặc phương tiện đa dạng")
        elif media_enrichment_score == 0:
            result["weaknesses"].append("Thiếu phương tiện minh họa như hình ảnh, biểu đồ hoặc bảng")
            
        # Bước 4: Đánh giá sự phức tạp cấu trúc (structure complexity)
        # Phân tích cấu trúc văn bản: đoạn văn, tiêu đề, danh sách, etc.
        
        # Đếm số đoạn văn
        paragraphs = len(content.split('\n\n'))
        
        # Đếm số tiêu đề
        headings = len(re.findall(r'^#{1,6}\s+[^\n]+$|<h[1-6][^>]*>[^<]+</h[1-6]>', content, re.MULTILINE))
        
        # Đếm số liên kết
        links = len(re.findall(r'\[([^\]]+)\]\(([^)]+)\)|<a[^>]+href=["\'](.*?)["\']', content))
        
        # Đếm các cấu trúc phức tạp (bảng, danh sách, code block)
        complex_structures = tables + lists + len(re.findall(r'```[^`]*```|`[^`]+`', content))
        
        # Tính điểm phức tạp cấu trúc
        structure_elements = paragraphs + headings * 2 + links + complex_structures
        
        if word_count > 0:
            # Chuẩn hóa theo độ dài
            normalized_structure = structure_elements / (word_count / 100)
            
            if normalized_structure >= 8:
                structure_complexity_score = 1.0
            elif normalized_structure >= 6:
                structure_complexity_score = 0.8
            elif normalized_structure >= 4:
                structure_complexity_score = 0.6
            elif normalized_structure >= 2:
                structure_complexity_score = 0.4
            else:
                structure_complexity_score = 0.2
        else:
            structure_complexity_score = 0.0
            
        result["structure_complexity_score"] = structure_complexity_score
        
        # Thêm điểm mạnh/yếu dựa trên độ phức tạp cấu trúc
        if structure_complexity_score >= 0.7:
            result["strengths"].append("Cấu trúc nội dung tốt với nhiều đoạn văn, tiêu đề và tổ chức rõ ràng")
        elif structure_complexity_score <= 0.3:
            result["weaknesses"].append("Cấu trúc đơn giản, thiếu tổ chức và phân cấp rõ ràng")
            
        # Bước 5: Tính điểm phong phú tổng thể
        # Trọng số: 40% mật độ thông tin, 30% phong phú từ vựng, 15% phong phú phương tiện, 15% phức tạp cấu trúc
        richness_score = (
            result["information_density_score"] * 0.4 +
            result["vocabulary_richness_score"] * 0.3 +
            result["media_enrichment_score"] * 0.15 +
            result["structure_complexity_score"] * 0.15
        )
        
        result["richness_score"] = max(0.0, min(1.0, richness_score))
        
        # Thêm giải thích
        if result["richness_score"] >= 0.8:
            result["explanation"] = "Nội dung rất phong phú với mật độ thông tin cao, từ vựng đa dạng và cấu trúc tốt"
        elif result["richness_score"] >= 0.6:
            result["explanation"] = "Nội dung khá phong phú, cân bằng tốt giữa thông tin và cách trình bày"
        elif result["richness_score"] >= 0.4:
            result["explanation"] = "Nội dung có độ phong phú trung bình, cần cải thiện một số khía cạnh"
        else:
            result["explanation"] = "Nội dung thiếu phong phú, cần bổ sung thêm thông tin và cải thiện cách trình bày"
            
        # Thêm thông tin về truy vấn nếu có
        if query:
            # Đánh giá độ liên quan với truy vấn (đơn giản)
            query_terms = set(query.lower().split())
            content_words = set(content.lower().split())
            query_coverage = len(query_terms.intersection(content_words)) / len(query_terms) if query_terms else 0
            
            # Bổ sung thông tin về độ phủ truy vấn
            if query_coverage >= 0.7:
                result["strengths"].append("Nội dung bao quát tốt các từ khóa trong truy vấn")
            elif query_coverage <= 0.3:
                result["weaknesses"].append("Nội dung chưa đề cập đầy đủ các từ khóa trong truy vấn")
                
        return result
    
    def _evaluate_search_results_quality(self, query: str, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Đánh giá chất lượng tổng thể của các kết quả tìm kiếm.
        
        Args:
            query: Truy vấn gốc
            results: Danh sách kết quả tìm kiếm
            
        Returns:
            Dict chứa kết quả đánh giá chất lượng
        """
        result = {
            "overall_quality_score": 0.0,
            "relevance_score": 0.0,
            "diversity_score": 0.0,
            "credibility_score": 0.0,
            "freshness_score": 0.0,
            "completeness_score": 0.0,
            "explanation": "",
            "high_quality_results": [],
            "low_quality_results": [],
            "improvements_needed": []
        }
        
        # Kiểm tra nếu không có kết quả
        if not results or len(results) == 0:
            result["explanation"] = "Không tìm thấy kết quả nào"
            result["improvements_needed"].append("Thử các từ khóa tìm kiếm khác")
            return result
            
        # Bước 1: Đánh giá độ liên quan (relevance)
        # Tính điểm liên quan trung bình của các kết quả
        relevance_scores = []
        
        for i, search_result in enumerate(results):
            # Lấy điểm liên quan từ kết quả (nếu có)
            if "relevance_score" in search_result:
                relevance_scores.append(search_result["relevance_score"])
            elif "relevance" in search_result:
                relevance_scores.append(search_result["relevance"])
            else:
                # Tính điểm liên quan dựa trên sự xuất hiện của từ khóa trong tiêu đề và snippet
                query_terms = set(query.lower().split())
                title = search_result.get("title", "").lower()
                snippet = search_result.get("snippet", "").lower()
                
                # Đếm số lượng từ khóa trong tiêu đề và snippet
                title_match = sum(1 for term in query_terms if term in title)
                snippet_match = sum(1 for term in query_terms if term in snippet)
                
                # Tính điểm liên quan (trọng số: 60% tiêu đề, 40% snippet)
                if len(query_terms) > 0:
                    title_score = title_match / len(query_terms)
                    snippet_score = snippet_match / len(query_terms)
                    rel_score = title_score * 0.6 + snippet_score * 0.4
                    relevance_scores.append(rel_score)
                else:
                    relevance_scores.append(0.5)  # Điểm mặc định
                    
        # Tính điểm liên quan trung bình
        if relevance_scores:
            # Ưu tiên các kết quả đầu tiên
            weighted_scores = []
            for i, score in enumerate(relevance_scores):
                weight = max(0.1, 1.0 - (i * 0.1))  # Trọng số giảm dần theo thứ tự
                weighted_scores.append(score * weight)
                
            # Tính điểm trung bình có trọng số
            relevance_score = sum(weighted_scores) / sum(max(0.1, 1.0 - (i * 0.1)) for i in range(len(relevance_scores)))
        else:
            relevance_score = 0.0
            
        result["relevance_score"] = relevance_score
        
        # Bước 2: Đánh giá độ đa dạng (diversity)
        # Sử dụng phương thức đã có
        diversity_result = self._evaluate_source_diversity(results)
        result["diversity_score"] = diversity_result["diversity_score"]
        
        # Bước 3: Đánh giá độ tin cậy (credibility)
        # Tính điểm tin cậy trung bình
        credibility_scores = []
        
        for search_result in results:
            # Lấy điểm tin cậy từ kết quả (nếu có)
            if "credibility_score" in search_result:
                credibility_scores.append(search_result["credibility_score"])
            elif "credibility" in search_result and isinstance(search_result["credibility"], dict):
                if "overall_score" in search_result["credibility"]:
                    credibility_scores.append(search_result["credibility"]["overall_score"])
                elif "source" in search_result["credibility"] and "score" in search_result["credibility"]["source"]:
                    credibility_scores.append(search_result["credibility"]["source"]["score"])
            else:
                # Ước tính độ tin cậy dựa trên domain
                url = search_result.get("url", "")
                domain_score = self._get_domain_credibility(self._extract_domain(url))
                
                if domain_score is not None:
                    credibility_scores.append(domain_score)
                else:
                    credibility_scores.append(0.5)  # Điểm mặc định
                    
        # Tính điểm tin cậy trung bình
        if credibility_scores:
            credibility_score = sum(credibility_scores) / len(credibility_scores)
        else:
            credibility_score = 0.5  # Điểm mặc định
            
        result["credibility_score"] = credibility_score
        
        # Bước 4: Đánh giá độ mới (freshness)
        # Đếm số lượng kết quả mới và cũ
        current_time = time.time()
        one_year_ago = current_time - (365 * 24 * 60 * 60)
        
        recent_results = 0
        dated_results = 0
        
        for search_result in results:
            # Tìm thời gian trong kết quả
            result_date = None
            
            if "date" in search_result:
                result_date = search_result["date"]
            elif "published_date" in search_result:
                result_date = search_result["published_date"]
            elif "timestamp" in search_result:
                result_date = search_result["timestamp"]
                
            # Nếu có thời gian, kiểm tra xem có phải là gần đây
            if result_date:
                try:
                    # Chuyển đổi thành timestamp
                    if isinstance(result_date, str):
                        # Xử lý các định dạng phổ biến
                        formats = ["%Y-%m-%d", "%Y/%m/%d", "%d-%m-%Y", "%d/%m/%Y", 
                                 "%Y-%m-%d %H:%M:%S", "%Y/%m/%d %H:%M:%S"]
                        
                        for fmt in formats:
                            try:
                                dt = datetime.strptime(result_date, fmt)
                                result_timestamp = dt.timestamp()
                                break
                            except:
                                continue
                        else:
                            result_timestamp = None
                    elif isinstance(result_date, (int, float)):
                        result_timestamp = result_date
                    else:
                        result_timestamp = None
                        
                    # Phân loại thời gian
                    if result_timestamp and result_timestamp > one_year_ago:
                        recent_results += 1
                    else:
                        dated_results += 1
                except:
                    # Nếu có lỗi khi xử lý thời gian, bỏ qua
                    pass
                    
        # Tính điểm độ mới
        total_dated = recent_results + dated_results
        if total_dated > 0:
            freshness_score = recent_results / total_dated
        else:
            # Nếu không có thông tin về thời gian, giả định là 0.5
            freshness_score = 0.5
            
        result["freshness_score"] = freshness_score
        
        # Bước 5: Đánh giá tính đầy đủ (completeness)
        # Kiểm tra xem có bao phủ đủ các khía cạnh của truy vấn không
        
        # Trích xuất các khía cạnh từ truy vấn
        query_facets = self._extract_facets(query)
        
        # Nếu không có khía cạnh, trích xuất từ khóa
        if not query_facets:
            query_facets = self._extract_keywords(query)
            
        # Đếm xem có bao nhiêu khía cạnh được đề cập trong kết quả
        facet_coverage = {}
        
        for facet in query_facets:
            facet_coverage[facet] = 0
            
        for search_result in results:
            title = search_result.get("title", "").lower()
            snippet = search_result.get("snippet", "").lower()
            content = title + " " + snippet
            
            for facet in query_facets:
                if facet.lower() in content:
                    facet_coverage[facet] += 1
                    
        # Tính tỷ lệ các khía cạnh được đề cập
        if query_facets:
            # Một khía cạnh cần xuất hiện trong ít nhất 2 kết quả để được coi là đủ
            well_covered_facets = sum(1 for count in facet_coverage.values() if count >= 2)
            completeness_score = well_covered_facets / len(query_facets)
            
            # Xác định các khía cạnh còn thiếu
            missing_facets = [facet for facet, count in facet_coverage.items() if count < 2]
            
            if missing_facets:
                if len(missing_facets) == 1:
                    result["improvements_needed"].append(f"Tìm thêm thông tin về khía cạnh: {missing_facets[0]}")
                else:
                    facets_str = ", ".join(missing_facets[:3])
                    result["improvements_needed"].append(f"Tìm thêm thông tin về các khía cạnh: {facets_str}")
        else:
            # Nếu không xác định được khía cạnh, đánh giá dựa trên số lượng kết quả
            completeness_score = min(1.0, len(results) / 5)  # 5+ kết quả được coi là đầy đủ
            
        result["completeness_score"] = completeness_score
        
        # Bước 6: Xác định kết quả chất lượng cao và thấp
        high_quality_threshold = 0.7
        low_quality_threshold = 0.3
        
        for i, search_result in enumerate(results):
            # Tính điểm chất lượng cho từng kết quả
            rel_score = relevance_scores[i] if i < len(relevance_scores) else 0.5
            cred_score = credibility_scores[i] if i < len(credibility_scores) else 0.5
            
            # Điểm chất lượng (trọng số: 60% độ liên quan, 40% độ tin cậy)
            quality_score = rel_score * 0.6 + cred_score * 0.4
            
            # Phân loại kết quả
            if quality_score >= high_quality_threshold:
                result["high_quality_results"].append({
                    "index": i,
                    "title": search_result.get("title", ""),
                    "quality_score": quality_score
                })
            elif quality_score <= low_quality_threshold:
                result["low_quality_results"].append({
                    "index": i,
                    "title": search_result.get("title", ""),
                    "quality_score": quality_score
                })
                
        # Bước 7: Tính điểm chất lượng tổng thể
        # Trọng số: 40% độ liên quan, 20% độ đa dạng, 20% độ tin cậy, 10% độ mới, 10% tính đầy đủ
        overall_quality_score = (
            result["relevance_score"] * 0.4 +
            result["diversity_score"] * 0.2 +
            result["credibility_score"] * 0.2 +
            result["freshness_score"] * 0.1 +
            result["completeness_score"] * 0.1
        )
        
        result["overall_quality_score"] = max(0.0, min(1.0, overall_quality_score))
        
        # Thêm giải thích và đề xuất cải thiện
        if result["overall_quality_score"] >= 0.8:
            result["explanation"] = "Kết quả tìm kiếm có chất lượng cao, liên quan và đáng tin cậy"
        elif result["overall_quality_score"] >= 0.6:
            result["explanation"] = "Kết quả tìm kiếm có chất lượng khá, bao gồm nhiều thông tin liên quan"
        elif result["overall_quality_score"] >= 0.4:
            result["explanation"] = "Kết quả tìm kiếm có chất lượng trung bình, cần cải thiện một số khía cạnh"
            
            # Thêm gợi ý cải thiện cụ thể
            if result["relevance_score"] < 0.5:
                result["improvements_needed"].append("Sử dụng các từ khóa chính xác hơn trong truy vấn")
            if result["diversity_score"] < 0.5:
                result["improvements_needed"].append("Tìm kiếm từ nhiều nguồn khác nhau")
            if result["credibility_score"] < 0.5:
                result["improvements_needed"].append("Tìm thêm các nguồn đáng tin cậy")
            if result["freshness_score"] < 0.5:
                result["improvements_needed"].append("Thêm 'mới nhất' hoặc năm hiện tại vào truy vấn")
        else:
            result["explanation"] = "Kết quả tìm kiếm có chất lượng thấp, cần cải thiện đáng kể"
            result["improvements_needed"].append("Thử từ khóa hoặc cách diễn đạt khác")
            result["improvements_needed"].append("Tìm kiếm từ các nguồn chuyên ngành hoặc đáng tin cậy")
            
        return result
    
    def _improve_search_results(self, query: str, results: List[Dict[str, Any]], evaluation_result: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Cải thiện chất lượng các kết quả tìm kiếm dựa trên đánh giá.
        
        Args:
            query: Truy vấn gốc
            results: Danh sách kết quả tìm kiếm gốc
            evaluation_result: Kết quả đánh giá (tùy chọn)
            
        Returns:
            Dict chứa danh sách kết quả đã cải thiện và thông tin bổ sung
        """
        # Nếu không có đánh giá, thực hiện đánh giá trước
        if not evaluation_result:
            evaluation_result = self._evaluate_search_results_quality(query, results)
            
        # Tạo kết quả trả về
        improved_results = {
            "results": [],
            "filtered_results": [],
            "added_results": [],
            "query_modifications": [],
            "improvement_actions": [],
            "explanation": ""
        }
        
        # Kiểm tra nếu không có kết quả hoặc kết quả đã tốt
        if not results or len(results) == 0:
            improved_results["explanation"] = "Không tìm thấy kết quả nào để cải thiện"
            improved_results["query_modifications"] = self._generate_alternative_queries(query)
            return improved_results
            
        if evaluation_result["overall_quality_score"] >= 0.8:
            improved_results["explanation"] = "Kết quả tìm kiếm đã có chất lượng cao, không cần cải thiện thêm"
            improved_results["results"] = results
            return improved_results
            
        # Bước 1: Loại bỏ kết quả chất lượng thấp
        low_quality_indices = set(item["index"] for item in evaluation_result.get("low_quality_results", []))
        
        # Tạo danh sách mới không bao gồm kết quả chất lượng thấp
        filtered_results = []
        for i, result in enumerate(results):
            if i in low_quality_indices:
                improved_results["filtered_results"].append(result)
            else:
                filtered_results.append(result)
                
        # Thêm hành động cải thiện nếu có lọc kết quả
        if improved_results["filtered_results"]:
            improved_results["improvement_actions"].append(f"Đã loại bỏ {len(improved_results['filtered_results'])} kết quả có chất lượng thấp")
            
        # Bước 2: Sắp xếp lại kết quả theo chất lượng
        # Tạo danh sách các điểm số chất lượng cho từng kết quả
        result_scores = []
        
        for i, result in enumerate(filtered_results):
            # Tính điểm chất lượng tổng hợp
            
            # Độ liên quan
            relevance = 0.0
            if "relevance_score" in result:
                relevance = result["relevance_score"]
            elif "relevance" in result:
                relevance = result["relevance"]
            else:
                # Đánh giá độ liên quan dựa trên sự xuất hiện của từ khóa trong tiêu đề và snippet
                query_terms = set(query.lower().split())
                title = result.get("title", "").lower()
                snippet = result.get("snippet", "").lower()
                
                if query_terms:
                    title_match = sum(1 for term in query_terms if term in title) / len(query_terms)
                    snippet_match = sum(1 for term in query_terms if term in snippet) / len(query_terms)
                    relevance = title_match * 0.7 + snippet_match * 0.3
                else:
                    relevance = 0.5
                    
            # Độ tin cậy
            credibility = 0.0
            if "credibility_score" in result:
                credibility = result["credibility_score"]
            else:
                # Ước tính độ tin cậy dựa trên domain
                url = result.get("url", "")
                domain_score = self._get_domain_credibility(self._extract_domain(url))
                credibility = domain_score if domain_score is not None else 0.5
                
            # Tính điểm tổng hợp (trọng số: 70% độ liên quan, 30% độ tin cậy)
            quality_score = relevance * 0.7 + credibility * 0.3
            
            # Thêm vào danh sách điểm số
            result_scores.append((i, quality_score))
            
        # Sắp xếp kết quả theo điểm chất lượng giảm dần
        result_scores.sort(key=lambda x: x[1], reverse=True)
        
        # Tạo danh sách kết quả đã sắp xếp
        sorted_results = [filtered_results[i] for i, _ in result_scores]
        
        # Thêm hành động cải thiện nếu có sắp xếp lại
        if len(sorted_results) > 1 and sorted_results != filtered_results:
            improved_results["improvement_actions"].append("Đã sắp xếp lại kết quả theo chất lượng")
            
        # Bước 3: Bổ sung thêm thông tin đánh giá cho từng kết quả
        for i, result in enumerate(sorted_results):
            # Thêm thông tin đánh giá nếu chưa có
            if "credibility_score" not in result:
                url = result.get("url", "")
                domain = self._extract_domain(url)
                domain_score = self._get_domain_credibility(domain)
                
                if domain_score is not None:
                    result["credibility_score"] = domain_score
                    result["credibility_source"] = "domain_reputation"
                else:
                    result["credibility_score"] = 0.5
                    result["credibility_source"] = "default"
                    
            # Thêm thông tin về độ liên quan nếu chưa có
            if "relevance_score" not in result:
                query_terms = set(query.lower().split())
                title = result.get("title", "").lower()
                snippet = result.get("snippet", "").lower()
                
                if query_terms:
                    title_match = sum(1 for term in query_terms if term in title) / len(query_terms)
                    snippet_match = sum(1 for term in query_terms if term in snippet) / len(query_terms)
                    result["relevance_score"] = title_match * 0.7 + snippet_match * 0.3
                    result["relevance_source"] = "keyword_matching"
                else:
                    result["relevance_score"] = 0.5
                    result["relevance_source"] = "default"
                    
        # Bước 4: Tạo các truy vấn thay thế cho các khía cạnh còn thiếu
        missing_facets = []
        facet_coverage = evaluation_result.get("facet_coverage", {})
        
        # Nếu không có thông tin facet_coverage, trích xuất từ improvements_needed
        if not facet_coverage:
            for improvement in evaluation_result.get("improvements_needed", []):
                if "khía cạnh" in improvement:
                    # Trích xuất các khía cạnh từ gợi ý cải thiện
                    match = re.search(r"khía cạnh:?\s*(.*)", improvement)
                    if match:
                        facets = match.group(1).split(", ")
                        missing_facets.extend(facets)
                        
        # Nếu vẫn không có, trích xuất từ truy vấn
        if not missing_facets:
            query_facets = self._extract_facets(query)
            
            # Đếm xem mỗi khía cạnh xuất hiện bao nhiêu lần trong kết quả
            facet_counts = {facet: 0 for facet in query_facets}
            
            for result in sorted_results:
                title = result.get("title", "").lower()
                snippet = result.get("snippet", "").lower()
                content = title + " " + snippet
                
                for facet in query_facets:
                    if facet.lower() in content:
                        facet_counts[facet] += 1
                        
            # Xác định các khía cạnh còn thiếu
            missing_facets = [facet for facet, count in facet_counts.items() if count < 2]
            
        # Tạo các truy vấn thay thế cho các khía cạnh còn thiếu
        alternative_queries = []
        
        for facet in missing_facets:
            # Tạo truy vấn tập trung vào khía cạnh còn thiếu
            alt_query = f"{query} {facet}"
            alternative_queries.append({
                "query": alt_query,
                "focus": facet,
                "reason": f"Tìm thêm thông tin về khía cạnh: {facet}"
            })
            
        # Thêm các truy vấn thay thế khác nếu cần
        if evaluation_result["relevance_score"] < 0.5:
            # Tạo các truy vấn chính xác hơn
            enhanced_queries = self._generate_alternative_queries(query)
            for eq in enhanced_queries:
                if eq not in [aq["query"] for aq in alternative_queries]:
                    alternative_queries.append({
                        "query": eq,
                        "focus": "relevance",
                        "reason": "Cải thiện độ liên quan bằng cách sử dụng từ khóa chính xác hơn"
                    })
                    
        # Nếu độ mới thấp, thêm truy vấn với giới hạn thời gian
        if evaluation_result["freshness_score"] < 0.5:
            current_year = datetime.now().year
            time_limited_query = f"{query} {current_year}"
            alternative_queries.append({
                "query": time_limited_query,
                "focus": "freshness",
                "reason": "Tìm kết quả mới hơn bằng cách thêm năm hiện tại"
            })
            
        # Nếu độ tin cậy thấp, thêm truy vấn với nguồn đáng tin cậy
        if evaluation_result["credibility_score"] < 0.5:
            credible_query = f"{query} site:.edu OR site:.gov"
            alternative_queries.append({
                "query": credible_query,
                "focus": "credibility",
                "reason": "Tìm từ các nguồn học thuật và chính phủ đáng tin cậy"
            })
            
        # Cập nhật danh sách truy vấn thay thế
        improved_results["query_modifications"] = alternative_queries
        
        # Bước 5: Hoàn thiện kết quả
        improved_results["results"] = sorted_results
        
        # Thêm giải thích
        if low_quality_indices:
            filtered_explanation = f"Đã loại bỏ {len(low_quality_indices)} kết quả chất lượng thấp và sắp xếp lại {len(sorted_results)} kết quả còn lại."
        else:
            filtered_explanation = f"Đã sắp xếp lại {len(sorted_results)} kết quả theo chất lượng."
            
        suggestion_explanation = ""
        if alternative_queries:
            suggestion_explanation = f" Đề xuất {len(alternative_queries)} truy vấn thay thế để tìm thêm thông tin."
            
        improved_results["explanation"] = filtered_explanation + suggestion_explanation
        
        return improved_results
    
    def _generate_alternative_queries(self, query: str) -> List[str]:
        """
        Tạo các truy vấn thay thế để cải thiện kết quả tìm kiếm.
        
        Args:
            query: Truy vấn gốc
            
        Returns:
            Danh sách các truy vấn thay thế
        """
        alternative_queries = []
        
        # Loại bỏ các stopword
        stopwords = ["và", "hoặc", "nhưng", "vì", "bởi", "nếu", "khi", "là", "có", "được", 
                    "tại", "trong", "ngoài", "trên", "dưới", "the", "and", "or", "but", "if", 
                    "when", "is", "are", "at", "in", "on", "of", "to", "from"]
        
        # Trích xuất từ khóa
        query_words = query.split()
        keywords = [word for word in query_words if word.lower() not in stopwords]
        
        # Nếu có quá ít từ khóa (chỉ 1-2 từ), không thay đổi nhiều
        if len(keywords) <= 2:
            # Thêm các từ khóa mở rộng
            alternative_queries.append(f"{query} là gì")
            alternative_queries.append(f"{query} cách")
            alternative_queries.append(f"{query} ví dụ")
            alternative_queries.append(f"hướng dẫn {query}")
            
            # Thêm phiên bản tiếng Anh nếu truy vấn là tiếng Việt
            if self._is_vietnamese_text(query):
                no_tone_query = self._remove_vietnamese_tones(query)
                alternative_queries.append(no_tone_query)
                
            return alternative_queries
            
        # Tạo các biến thể của truy vấn
        # 1. Đổi thứ tự các từ khóa
        if len(keywords) >= 3:
            shuffled_keywords = keywords.copy()
            for i in range(min(3, len(keywords))):
                random.shuffle(shuffled_keywords)
                alternative_query = " ".join(shuffled_keywords)
                if alternative_query != query and alternative_query not in alternative_queries:
                    alternative_queries.append(alternative_query)
                    
        # 2. Loại bỏ một số từ khóa ít quan trọng
        if len(keywords) >= 4:
            # Sắp xếp từ khóa theo độ dài (ưu tiên từ dài hơn - thường quan trọng hơn)
            sorted_keywords = sorted(keywords, key=len, reverse=True)
            important_keywords = sorted_keywords[:int(len(sorted_keywords) * 0.7)]
            alternative_query = " ".join(important_keywords)
            if alternative_query != query and alternative_query not in alternative_queries:
                alternative_queries.append(alternative_query)
                
        # 3. Thêm từ khóa chỉ định rõ hơn
        clarifying_terms = ["cụ thể", "chi tiết", "specific", "detailed"]
        for term in clarifying_terms:
            alternative_query = f"{query} {term}"
            if alternative_query not in alternative_queries:
                alternative_queries.append(alternative_query)
                
        # 4. Thêm từ khóa giới hạn cho tìm kiếm học thuật
        if any(word in query.lower() for word in ["nghiên cứu", "khoa học", "học thuật", "research", "science", "academic"]):
            academic_terms = ["paper", "journal", "scholarly", "nghiên cứu", "tạp chí", "khoa học"]
            for term in academic_terms:
                if term not in query.lower():
                    alternative_query = f"{query} {term}"
                    if alternative_query not in alternative_queries:
                        alternative_queries.append(alternative_query)
                        
        # 5. Chuyển đổi dạng câu hỏi/câu lệnh
        # Kiểm tra xem truy vấn có phải là câu hỏi không
        if any(query.lower().startswith(qw) for qw in ["what", "how", "why", "when", "where", "who", "which", 
                                                     "cái gì", "như thế nào", "tại sao", "khi nào", "ở đâu", "ai", "nào"]):
            # Chuyển từ câu hỏi sang từ khóa
            if query.endswith("?"):
                query_no_question = query[:-1]
            else:
                query_no_question = query
                
            # Loại bỏ từ để hỏi
            for qw in ["what", "how", "why", "when", "where", "who", "which", 
                      "cái gì", "như thế nào", "tại sao", "khi nào", "ở đâu", "ai", "nào"]:
                if query_no_question.lower().startswith(qw):
                    query_no_question = query_no_question[len(qw):].strip()
                    break
                    
            # Loại bỏ các từ như "is", "are", "do", "does", "là", "có phải"
            for aux in ["is", "are", "do", "does", "was", "were", "will", "should", "can", "could", 
                       "là", "có phải", "có", "sẽ", "nên", "có thể"]:
                if query_no_question.lower().startswith(aux + " "):
                    query_no_question = query_no_question[len(aux):].strip()
                    break
                    
            # Thêm vào danh sách truy vấn thay thế
            if query_no_question != query and query_no_question not in alternative_queries:
                alternative_queries.append(query_no_question)
                
        else:
            # Chuyển từ từ khóa sang câu hỏi
            question_forms = []
            
            # Trích xuất chủ đề chính
            main_topic = self._extract_main_topic(query)
            
            if main_topic:
                question_forms.append(f"What is {main_topic}?")
                question_forms.append(f"{main_topic} là gì?")
                question_forms.append(f"How does {main_topic} work?")
                question_forms.append(f"{main_topic} hoạt động như thế nào?")
                
            # Thêm vào danh sách truy vấn thay thế
            for qf in question_forms:
                if qf not in alternative_queries:
                    alternative_queries.append(qf)
                    
        # Loại bỏ các truy vấn trùng lặp hoặc quá giống với truy vấn gốc
        filtered_queries = []
        for aq in alternative_queries:
            # Kiểm tra xem truy vấn thay thế có quá giống với truy vấn gốc không
            if aq.lower() != query.lower() and self._query_similarity(query, aq) < 0.9:
                filtered_queries.append(aq)
                
        # Giới hạn số lượng truy vấn thay thế
        return filtered_queries[:5]
    
    def _query_similarity(self, query1: str, query2: str) -> float:
        """
        Tính độ tương đồng giữa hai truy vấn.
        
        Args:
            query1: Truy vấn thứ nhất
            query2: Truy vấn thứ hai
            
        Returns:
            Điểm tương đồng từ 0.0 đến 1.0
        """
        # Chuyển thành lowercase
        q1 = query1.lower()
        q2 = query2.lower()
        
        # Tách từ
        words1 = set(q1.split())
        words2 = set(q2.split())
        
        # Tính độ tương đồng Jaccard
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        if union == 0:
            return 0.0
            
        return intersection / union
    
    def _extract_pdf_info(self, file_path: str) -> Dict[str, Any]:
        """
        Trích xuất thông tin từ file PDF.
        
        Args:
            file_path: Đường dẫn đến file PDF
            
        Returns:
            Dict chứa nội dung và metadata của PDF
        """
        # Kiểm tra xem FileProcessor có khả dụng không
        if not FileProcessor:
            # Fallback nếu không có FileProcessor
            return {
                "content": f"Không thể trích xuất nội dung PDF từ {file_path}: FileProcessor không khả dụng",
                "metadata": {
                    "error": "FileProcessor not available",
                    "file_path": file_path
                }
            }
        
        # Sử dụng FileProcessor hiện có
        try:
            # Kiểm tra xem agent có _file_processor không
            if hasattr(self, '_file_processor'):
                result = self._file_processor.extract_content(file_path, 'pdf')
            else:
                # Tạo FileProcessor mới nếu cần
                file_processor = FileProcessor()
                result = file_processor.extract_content(file_path, 'pdf')
            
            # Phân tích thêm nội dung PDF nếu cần
            if result.get('success', False):
                # Thêm thông tin bổ sung về PDF
                pdf_text = result.get('content', '')
                
                # Tính số từ và số câu
                words = pdf_text.split()
                sentences = re.split(r'[.!?]', pdf_text)
                sentences = [s.strip() for s in sentences if s.strip()]
                
                if 'metadata' not in result:
                    result['metadata'] = {}
                    
                result['metadata']['word_count'] = len(words)
                result['metadata']['sentence_count'] = len(sentences)
                
                # Tìm các từ khóa phổ biến
                if words:
                    # Loại bỏ stopwords (từ không quan trọng)
                    stopwords = {"the", "a", "an", "and", "or", "but", "if", "of", "in", "on", 
                                "at", "to", "for", "with", "by", "about", "như", "là", "và", 
                                "hoặc", "nhưng", "nếu", "của", "trong", "trên", "tại", "cho"}
                    
                    # Đếm từ
                    word_counter = Counter(word.lower() for word in words if word.lower() not in stopwords and len(word) > 2)
                    
                    # Lấy 10 từ phổ biến nhất
                    result['metadata']['common_keywords'] = [word for word, _ in word_counter.most_common(10)]
                
                # Kiểm tra PDF có được bảo vệ không
                if 'encrypted' not in result['metadata']:
                    result['metadata']['encrypted'] = False  # Mặc định
                    
                    # Kiểm tra các dấu hiệu bảo vệ
                    if "This document is password protected" in pdf_text:
                        result['metadata']['encrypted'] = True
            
            return result
        except Exception as e:
            # Xử lý lỗi
            return {
                "success": False,
                "content": f"Lỗi khi trích xuất nội dung PDF: {str(e)}",
                "metadata": {
                    "error": str(e),
                    "file_path": file_path
                }
            }
    
    def _crawl_with_async(self, urls: List[str], timeout: int = 30, max_workers: int = 10) -> List[Dict[str, Any]]:
        """
        Crawl nhiều URL cùng lúc sử dụng asyncio.
        
        Args:
            urls: Danh sách URL cần crawl
            timeout: Thời gian chờ tối đa (giây)
            max_workers: Số lượng worker tối đa
            
        Returns:
            Danh sách kết quả crawl
        """
        results = []
        
        if not urls:
            return results
            
        # Giới hạn số lượng workers
        workers = min(max_workers, len(urls))
        
        # Tạo các tác vụ crawl
        with ThreadPoolExecutor(max_workers=workers) as executor:
            # Gửi các yêu cầu song song
            future_to_url = {executor.submit(self._fetch_url, url, timeout): url for url in urls}
            
            # Xử lý kết quả
            for future in as_completed(future_to_url):
                url = future_to_url[future]
                try:
                    data = future.result()
                    if data:
                        results.append(data)
                except Exception as e:
                    logger.error(f"Lỗi khi crawl URL {url}: {str(e)}")
                    results.append({
                        "url": url,
                        "success": False,
                        "error": str(e),
                        "status_code": None,
                        "content": None,
                        "headers": {},
                        "timestamp": datetime.now().isoformat()
                    })
        
        return results
    
    def _fetch_url(self, url: str, timeout: int = 30) -> Dict[str, Any]:
        """
        Tải nội dung từ một URL.
        
        Args:
            url: URL cần tải
            timeout: Thời gian chờ tối đa (giây)
            
        Returns:
            Dict chứa kết quả tải
        """
        try:
            # Chuẩn hóa URL
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
                
            # Kiểm tra robots.txt nếu cần
            if hasattr(self, 'respect_robots_txt') and self.respect_robots_txt:
                can_fetch = self._check_robots_txt(url)
                if not can_fetch:
                    return {
                        "url": url,
                        "success": False,
                        "error": "Disallowed by robots.txt",
                        "status_code": None,
                        "content": None,
                        "headers": {},
                        "timestamp": datetime.now().isoformat()
                    }
            
            # Tạo headers
            user_agent = getattr(self, 'user_agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
            headers = {
                'User-Agent': user_agent,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Cache-Control': 'max-age=0'
            }
            
            # Gửi request
            proxy = getattr(self, 'proxy', None)
            response = requests.get(url, headers=headers, timeout=timeout, verify=True, proxies=proxy)
            
            # Kiểm tra status code
            response.raise_for_status()
            
            # Phân tích nội dung
            content_type = response.headers.get('Content-Type', '').lower()
            
            # Xử lý các loại nội dung khác nhau
            if 'text/html' in content_type:
                # Phân tích HTML
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Loại bỏ các phần không cần thiết
                for tag in soup(['script', 'style', 'meta', 'noscript', 'iframe']):
                    tag.decompose()
                
                # Trích xuất nội dung text
                text = soup.get_text(separator='\n', strip=True)
                
                # Cải thiện định dạng
                text = re.sub(r'\n+', '\n', text)
                text = re.sub(r'\s+', ' ', text)
                
                return {
                    "url": url,
                    "success": True,
                    "error": None,
                    "status_code": response.status_code,
                    "content": text,
                    "title": self._extract_title(soup),
                    "headers": dict(response.headers),
                    "timestamp": datetime.now().isoformat(),
                    "links": self._extract_links(soup, url)
                }
                
            elif 'application/json' in content_type:
                # Phân tích JSON
                try:
                    json_data = response.json()
                    return {
                        "url": url,
                        "success": True,
                        "error": None,
                        "status_code": response.status_code,
                        "content": json.dumps(json_data, indent=2),
                        "json_data": json_data,
                        "headers": dict(response.headers),
                        "timestamp": datetime.now().isoformat()
                    }
                except Exception as e:
                    return {
                        "url": url,
                        "success": False,
                        "error": f"JSON parsing error: {str(e)}",
                        "status_code": response.status_code,
                        "content": response.text,
                        "headers": dict(response.headers),
                        "timestamp": datetime.now().isoformat()
                    }
                    
            elif 'application/pdf' in content_type:
                # Lưu PDF tạm thời và phân tích
                with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
                    temp_file.write(response.content)
                    temp_path = temp_file.name
                
                try:
                    # Phân tích PDF
                    pdf_info = self._extract_pdf_info(temp_path)
                    
                    return {
                        "url": url,
                        "success": True,
                        "error": None,
                        "status_code": response.status_code,
                        "content": pdf_info.get('content', ''),
                        "metadata": pdf_info.get('metadata', {}),
                        "headers": dict(response.headers),
                        "timestamp": datetime.now().isoformat(),
                        "file_type": "pdf"
                    }
                finally:
                    # Xóa file tạm
                    try:
                        os.unlink(temp_path)
                    except:
                        pass
                        
            else:
                # Các loại nội dung khác
                return {
                    "url": url,
                    "success": True,
                    "error": None,
                    "status_code": response.status_code,
                    "content": response.text[:10000],  # Giới hạn kích thước
                    "content_type": content_type,
                    "headers": dict(response.headers),
                    "timestamp": datetime.now().isoformat()
                }
                
        except requests.exceptions.RequestException as e:
            return {
                "url": url,
                "success": False,
                "error": f"Request error: {str(e)}",
                "status_code": getattr(e.response, 'status_code', None) if hasattr(e, 'response') else None,
                "content": None,
                "headers": getattr(e.response, 'headers', {}) if hasattr(e, 'response') else {},
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            return {
                "url": url,
                "success": False,
                "error": f"Unexpected error: {str(e)}",
                "status_code": None,
                "content": None,
                "headers": {},
                "timestamp": datetime.now().isoformat()
            }
    
    def _check_robots_txt(self, url: str) -> bool:
        """
        Kiểm tra quyền truy cập URL theo robots.txt.
        
        Args:
            url: URL cần kiểm tra
            
        Returns:
            True nếu được phép truy cập, False nếu không
        """
        try:
            # Phân tích URL
            parsed_url = urllib.parse.urlparse(url)
            base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
            robots_url = f"{base_url}/robots.txt"
            
            # Kiểm tra trong cache
            if robots_url in self._robots_cache:
                parser = self._robots_cache[robots_url]
            else:
                # Tạo parser mới
                parser = RobotFileParser()
                parser.set_url(robots_url)
                
                try:
                    parser.read()
                    self._robots_cache[robots_url] = parser
                except:
                    # Nếu không đọc được robots.txt, mặc định cho phép
                    return True
            
            # Kiểm tra quyền truy cập
            return parser.can_fetch(self.user_agent, url)
            
        except Exception as e:
            # Xử lý lỗi - mặc định cho phép truy cập
            return True
    
    def _extract_title(self, soup: BeautifulSoup) -> str:
        """
        Trích xuất tiêu đề từ trang web.
        
        Args:
            soup: Đối tượng BeautifulSoup đã phân tích
            
        Returns:
            Tiêu đề của trang
        """
        if soup.title:
            return soup.title.string.strip() if soup.title.string else ""
        
        # Tìm các thẻ h1
        h1_tags = soup.find_all('h1')
        if h1_tags:
            return h1_tags[0].get_text(strip=True)
            
        return ""
    
    def _extract_links(self, soup: BeautifulSoup, base_url: str) -> List[Dict[str, str]]:
        """
        Trích xuất các liên kết từ trang web.
        
        Args:
            soup: Đối tượng BeautifulSoup đã phân tích
            base_url: URL cơ sở để tạo URL tuyệt đối
            
        Returns:
            Danh sách các liên kết với URL và text
        """
        links = []
        
        # Tìm tất cả thẻ a
        for a_tag in soup.find_all('a', href=True):
            href = a_tag['href']
            text = a_tag.get_text(strip=True)
            
            # Bỏ qua các URL không hợp lệ
            if not href or href.startswith('#') or href.startswith('javascript:'):
                continue
                
            # Tạo URL tuyệt đối
            absolute_url = urllib.parse.urljoin(base_url, href)
            
            links.append({
                "url": absolute_url,
                "text": text if text else "[No text]",
                "is_internal": urllib.parse.urlparse(absolute_url).netloc == urllib.parse.urlparse(base_url).netloc
            })
            
        return links
        
    def _deep_crawl_improved(self, start_url: str, max_depth: int = 2, max_pages: int = 20, 
                           same_domain_only: bool = True, keywords: List[str] = None) -> Dict[str, Any]:
        """
        Crawl sâu từ một URL bắt đầu với các cải tiến hiệu suất.
        
        Args:
            start_url: URL bắt đầu
            max_depth: Độ sâu tối đa
            max_pages: Số trang tối đa
            same_domain_only: Chỉ crawl cùng domain
            keywords: Danh sách từ khóa để ưu tiên
            
        Returns:
            Kết quả crawl bao gồm nội dung và metadata
        """
        # Chuẩn hóa URL
        if not start_url.startswith(('http://', 'https://')):
            start_url = 'https://' + start_url
            
        # Khởi tạo kết quả
        result = {
            "start_url": start_url,
            "crawl_time": datetime.now().isoformat(),
            "max_depth": max_depth,
            "max_pages": max_pages,
            "same_domain_only": same_domain_only,
            "keywords": keywords or [],
            "pages_crawled": 0,
            "success_count": 0,
            "error_count": 0,
            "total_content_length": 0,
            "content": {},
            "links": defaultdict(list),
            "domain_stats": defaultdict(int),
            "errors": []
        }
        
        # Phân tích domain bắt đầu
        parsed_start_url = urllib.parse.urlparse(start_url)
        start_domain = parsed_start_url.netloc
        
        # Khởi tạo cấu trúc dữ liệu cho BFS
        queue = deque([(start_url, 0)])  # (url, depth)
        visited = set([start_url])
        url_scores = {}  # Điểm cho mỗi URL để ưu tiên
        
        # Cài đặt thời gian bắt đầu
        start_time = datetime.now()
        timeout_seconds = 300  # 5 phút timeout
        
        # Thực hiện BFS cải tiến
        while queue and result["pages_crawled"] < max_pages and (datetime.now() - start_time).total_seconds() < timeout_seconds:
            # Lấy URL từ queue có điểm cao nhất
            if url_scores:
                # Chọn URL có điểm cao nhất
                current_urls = []
                current_depth = None
                
                # Lấy tối đa 5 URL cùng lúc
                for _ in range(min(5, len(queue))):
                    if not queue:
                        break
                    url, depth = queue.popleft()
                    current_urls.append(url)
                    current_depth = depth
            else:
                # Chọn URL theo thứ tự queue
                url, current_depth = queue.popleft()
                current_urls = [url]
            
            # Kiểm tra độ sâu
            if current_depth > max_depth:
                continue
                
            # Crawl nhiều URL cùng lúc
            crawl_results = self._crawl_with_async(current_urls)
            
            # Xử lý kết quả
            for page_data in crawl_results:
                url = page_data["url"]
                result["pages_crawled"] += 1
                
                # Cập nhật thống kê
                parsed_url = urllib.parse.urlparse(url)
                domain = parsed_url.netloc
                result["domain_stats"][domain] += 1
                
                if page_data["success"]:
                    result["success_count"] += 1
                    
                    # Lưu nội dung
                    content = page_data.get("content", "")
                    result["content"][url] = {
                        "title": page_data.get("title", ""),
                        "content": content,
                        "length": len(content) if content else 0,
                        "timestamp": page_data.get("timestamp", datetime.now().isoformat()),
                        "status_code": page_data.get("status_code"),
                        "depth": current_depth
                    }
                    
                    # Cập nhật tổng kích thước nội dung
                    result["total_content_length"] += len(content) if content else 0
                    
                    # Xử lý các liên kết nếu chưa đạt độ sâu tối đa
                    if current_depth < max_depth:
                        links = page_data.get("links", [])
                        for link in links:
                            link_url = link["url"]
                            
                            # Bỏ qua nếu đã thăm
                            if link_url in visited:
                                continue
                                
                            # Kiểm tra domain nếu cần
                            if same_domain_only:
                                parsed_link = urllib.parse.urlparse(link_url)
                                if parsed_link.netloc != start_domain:
                                    continue
                            
                            # Thêm vào đã thăm
                            visited.add(link_url)
                            
                            # Tính điểm cho URL
                            url_score = 0
                            link_text = link.get("text", "").lower()
                            
                            # Ưu tiên nếu có từ khóa trong URL hoặc text
                            if keywords:
                                for keyword in keywords:
                                    if keyword.lower() in link_url.lower():
                                        url_score += 5
                                    if keyword.lower() in link_text:
                                        url_score += 3
                            
                            # Ưu tiên URL nội bộ
                            if link.get("is_internal", False):
                                url_score += 2
                                
                            # Ưu tiên URL ngắn hơn
                            url_score -= len(link_url) * 0.01
                            
                            # Lưu điểm
                            url_scores[link_url] = url_score
                            
                            # Thêm vào queue
                            queue.append((link_url, current_depth + 1))
                            
                            # Lưu liên kết
                            result["links"][url].append({
                                "url": link_url,
                                "text": link.get("text", ""),
                                "is_internal": link.get("is_internal", False)
                            })
                else:
                    # Lưu lỗi
                    result["error_count"] += 1
                    result["errors"].append({
                        "url": url,
                        "error": page_data.get("error", "Unknown error"),
                        "status_code": page_data.get("status_code"),
                        "timestamp": page_data.get("timestamp", datetime.now().isoformat())
                    })
            
            # Sắp xếp lại queue dựa trên điểm
            if url_scores:
                new_queue = sorted(queue, key=lambda x: url_scores.get(x[0], 0), reverse=True)
                queue = deque(new_queue)
        
        # Thêm thông tin tổng kết
        result["total_time_seconds"] = (datetime.now() - start_time).total_seconds()
        result["urls_visited"] = list(visited)
        result["queue_remaining"] = len(queue)
        
        return result