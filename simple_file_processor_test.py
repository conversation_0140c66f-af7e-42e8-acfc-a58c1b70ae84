#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test đơn giản cho FileProcessor
"""

import sys
import os
import tempfile

# Thêm đường dẫn để import
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_basic_functionality():
    """Test chức năng cơ bản của FileProcessor"""
    print("🚀 TESTING BASIC FILEPROCESSOR FUNCTIONALITY")
    print("=" * 50)
    
    try:
        from src.deep_research_core.utils.shared.file_processor import FileProcessor
        print("✅ Import FileProcessor thành công!")
        
        # Test 1: Tạo và xử lý file text đơn giản
        print("\n🔍 Test 1: Xử lý file TXT từ binary content")
        test_text = "Đây là một file text tiếng Việt.\nDòng thứ hai với nội dung khác."
        binary_content = test_text.encode('utf-8')
        
        processor = FileProcessor(
            file_content=binary_content, 
            file_type='txt', 
            verbose=True,
            extract_metadata=False  # Tắt auto metadata để tránh lỗi
        )
        
        # Kiểm tra file đã được load
        print(f"  ✓ File loaded: {processor._loaded}")
        print(f"  ✓ File type: {processor._file_type}")
        print(f"  ✓ File size: {processor._file_size} bytes")
        
        # Trích xuất text
        extracted_text = processor.extract_text()
        print(f"  ✓ Text extracted: {len(extracted_text)} characters")
        print(f"  ✓ Text preview: {extracted_text[:50]}...")
        
        # Kiểm tra supported file types
        is_supported = processor.is_supported_file_type()
        print(f"  ✓ File type supported: {is_supported}")
        
        # Cleanup
        processor.cleanup()
        print("  ✓ Cleanup completed")
        
        # Test 2: Test với file HTML đơn giản
        print("\n🔍 Test 2: Xử lý file HTML")
        html_content = """
        <html>
        <head><title>Test HTML</title></head>
        <body>
            <h1>Tiêu đề chính</h1>
            <p>Đây là một đoạn văn bản tiếng Việt.</p>
        </body>
        </html>
        """
        
        html_processor = FileProcessor(
            file_content=html_content.encode('utf-8'),
            file_type='html',
            verbose=True,
            extract_metadata=False
        )
        
        html_text = html_processor.extract_text()
        print(f"  ✓ HTML text extracted: {len(html_text)} characters")
        print(f"  ✓ HTML text preview: {html_text[:100]}...")
        
        html_processor.cleanup()
        print("  ✓ HTML cleanup completed")
        
        # Test 3: Test error handling
        print("\n🔍 Test 3: Error handling")
        
        try:
            # Test với file type không hỗ trợ
            unsupported_processor = FileProcessor(
                file_content=b"test content",
                file_type="unsupported_type",
                verbose=True,
                extract_metadata=False
            )
            
            if not unsupported_processor.is_supported_file_type():
                print("  ✓ Unsupported file type detected correctly")
            
            unsupported_processor.cleanup()
            
        except Exception as e:
            print(f"  ✓ Exception handled: {type(e).__name__}")
        
        # Test 4: Test file identification
        print("\n🔍 Test 4: File type identification")
        
        # Test PDF header
        pdf_header = b'%PDF-1.4\n'
        pdf_processor = FileProcessor(
            file_content=pdf_header + b'fake pdf content',
            extract_metadata=False
        )
        print(f"  ✓ PDF identified as: {pdf_processor._file_type}")
        pdf_processor.cleanup()
        
        # Test HTML content
        html_bytes = b'<!DOCTYPE html><html><body>Test</body></html>'
        html_id_processor = FileProcessor(
            file_content=html_bytes,
            extract_metadata=False
        )
        print(f"  ✓ HTML identified as: {html_id_processor._file_type}")
        html_id_processor.cleanup()
        
        print("\n🎉 TẤT CẢ TESTS CƠ BẢN HOÀN THÀNH THÀNH CÔNG!")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_basic_functionality()
    print(f"\n📊 Kết quả: {'SUCCESS' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
