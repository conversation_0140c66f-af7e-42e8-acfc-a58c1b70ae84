#!/usr/bin/env python3
"""
Final production readiness check cho WebSearchAgentLocalMerged.
"""

import sys
import os
import json
import time
import requests
from datetime import datetime

def check_core_functionality():
    """Kiểm tra core functionality."""
    print("🔍 CORE FUNCTIONALITY CHECK")
    print("=" * 40)
    
    checks = {
        'file_exists': False,
        'file_size_ok': False,
        'class_definition': False,
        'core_methods': False,
        'real_search_impl': False,
        'searxng_priority': False
    }
    
    try:
        # Check file exists
        agent_file = "src/deep_research_core/agents/web_search_agent_local_merged.py"
        if os.path.exists(agent_file):
            checks['file_exists'] = True
            print("✅ Agent file exists")
            
            # Check file size
            file_size = os.path.getsize(agent_file)
            if file_size > 200000:  # > 200KB
                checks['file_size_ok'] = True
                print(f"✅ File size OK: {file_size:,} bytes")
            else:
                print(f"⚠️  File size small: {file_size:,} bytes")
            
            # Check content
            with open(agent_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check class definition
            if "class WebSearchAgentLocalMerged" in content:
                checks['class_definition'] = True
                print("✅ Class definition found")
            
            # Check core methods
            core_methods = ['search', '_perform_real_search', '_search_with_searxng']
            found_methods = sum(1 for method in core_methods if f"def {method}" in content)
            if found_methods >= len(core_methods):
                checks['core_methods'] = True
                print(f"✅ Core methods found: {found_methods}/{len(core_methods)}")
            
            # Check real search implementation
            if "_perform_real_search" in content and "example.com" not in content.split("_perform_real_search")[1].split("def ")[0]:
                checks['real_search_impl'] = True
                print("✅ Real search implementation (no mock data)")
            
            # Check SearXNG priority
            if "localhost:8080" in content and "_check_searxng_health" in content:
                checks['searxng_priority'] = True
                print("✅ SearXNG local priority implemented")
        
        else:
            print("❌ Agent file not found")
    
    except Exception as e:
        print(f"❌ Error checking core functionality: {e}")
    
    return checks

def check_dependencies():
    """Kiểm tra dependencies."""
    print("\n📦 DEPENDENCIES CHECK")
    print("=" * 30)
    
    required_modules = [
        'requests', 'beautifulsoup4', 'lxml', 'urllib3'
    ]
    
    available_modules = []
    missing_modules = []
    
    for module in required_modules:
        try:
            if module == 'beautifulsoup4':
                import bs4
                available_modules.append(module)
                print(f"✅ {module}: Available")
            else:
                __import__(module)
                available_modules.append(module)
                print(f"✅ {module}: Available")
        except ImportError:
            missing_modules.append(module)
            print(f"❌ {module}: Missing")
    
    return {
        'available': available_modules,
        'missing': missing_modules,
        'coverage': len(available_modules) / len(required_modules) * 100
    }

def check_search_engines():
    """Kiểm tra search engines availability."""
    print("\n🌐 SEARCH ENGINES CHECK")
    print("=" * 35)
    
    engines = {
        'searxng_local': 'http://localhost:8080',
        'searxng_public': 'https://searx.be',
        'duckduckgo': 'https://api.duckduckgo.com',
        'httpbin': 'https://httpbin.org/get'
    }
    
    results = {}
    
    for engine, url in engines.items():
        try:
            start_time = time.time()
            response = requests.get(url, timeout=5)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                results[engine] = {
                    'status': 'available',
                    'response_time': response_time,
                    'status_code': response.status_code
                }
                print(f"✅ {engine}: Available ({response_time:.3f}s)")
            else:
                results[engine] = {
                    'status': 'error',
                    'response_time': response_time,
                    'status_code': response.status_code
                }
                print(f"❌ {engine}: HTTP {response.status_code}")
        
        except Exception as e:
            results[engine] = {
                'status': 'unavailable',
                'error': str(e)
            }
            print(f"❌ {engine}: {str(e)[:50]}")
    
    return results

def check_performance_baseline():
    """Kiểm tra performance baseline."""
    print("\n⚡ PERFORMANCE BASELINE CHECK")
    print("=" * 40)
    
    # Simple performance tests
    tests = {
        'file_read_speed': 0,
        'import_speed': 0,
        'network_latency': 0
    }
    
    try:
        # File read speed
        start_time = time.time()
        agent_file = "src/deep_research_core/agents/web_search_agent_local_merged.py"
        if os.path.exists(agent_file):
            with open(agent_file, 'r', encoding='utf-8') as f:
                content = f.read()
            tests['file_read_speed'] = time.time() - start_time
            print(f"✅ File read speed: {tests['file_read_speed']:.3f}s")
        
        # Import speed (simulate)
        start_time = time.time()
        import importlib
        tests['import_speed'] = time.time() - start_time
        print(f"✅ Import speed: {tests['import_speed']:.3f}s")
        
        # Network latency
        start_time = time.time()
        response = requests.get("https://httpbin.org/get", timeout=5)
        if response.status_code == 200:
            tests['network_latency'] = time.time() - start_time
            print(f"✅ Network latency: {tests['network_latency']:.3f}s")
    
    except Exception as e:
        print(f"❌ Performance test error: {e}")
    
    return tests

def check_documentation():
    """Kiểm tra documentation completeness."""
    print("\n📚 DOCUMENTATION CHECK")
    print("=" * 30)
    
    docs = {
        'api_documentation': 'API_DOCUMENTATION.md',
        'usage_examples': 'USAGE_EXAMPLES.md',
        'task_documentation': 'TASK_4_TESTING_DOCUMENTATION.md',
        'consolidation_plan': 'CONSOLIDATION_PLAN.md'
    }
    
    doc_status = {}
    
    for doc_name, file_path in docs.items():
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            doc_status[doc_name] = {
                'exists': True,
                'size': file_size,
                'status': 'complete' if file_size > 5000 else 'partial'
            }
            status_icon = "✅" if file_size > 5000 else "⚠️"
            print(f"{status_icon} {doc_name}: {file_size:,} bytes")
        else:
            doc_status[doc_name] = {
                'exists': False,
                'size': 0,
                'status': 'missing'
            }
            print(f"❌ {doc_name}: Missing")
    
    return doc_status

def generate_production_readiness_report():
    """Tạo báo cáo production readiness."""
    print("\n🚀 PRODUCTION READINESS ASSESSMENT")
    print("=" * 50)
    
    # Run all checks
    core_checks = check_core_functionality()
    deps_check = check_dependencies()
    engines_check = check_search_engines()
    perf_check = check_performance_baseline()
    docs_check = check_documentation()
    
    # Calculate scores
    core_score = sum(core_checks.values()) / len(core_checks) * 100
    deps_score = deps_check['coverage']
    engines_score = sum(1 for e in engines_check.values() if e['status'] == 'available') / len(engines_check) * 100
    perf_score = 100 if all(t < 1.0 for t in perf_check.values() if t > 0) else 80
    docs_score = sum(1 for d in docs_check.values() if d['status'] == 'complete') / len(docs_check) * 100
    
    overall_score = (core_score + deps_score + engines_score + perf_score + docs_score) / 5
    
    # Generate report
    report = {
        'timestamp': datetime.now().isoformat(),
        'overall_score': overall_score,
        'scores': {
            'core_functionality': core_score,
            'dependencies': deps_score,
            'search_engines': engines_score,
            'performance': perf_score,
            'documentation': docs_score
        },
        'details': {
            'core_checks': core_checks,
            'dependencies': deps_check,
            'search_engines': engines_check,
            'performance': perf_check,
            'documentation': docs_check
        },
        'production_ready': overall_score >= 80
    }
    
    # Display results
    print(f"\n📊 FINAL SCORES:")
    print(f"Core Functionality: {core_score:.1f}%")
    print(f"Dependencies: {deps_score:.1f}%")
    print(f"Search Engines: {engines_score:.1f}%")
    print(f"Performance: {perf_score:.1f}%")
    print(f"Documentation: {docs_score:.1f}%")
    print(f"\n🎯 OVERALL SCORE: {overall_score:.1f}%")
    
    # Production readiness assessment
    if overall_score >= 90:
        print("\n🎉 PRODUCTION READY - EXCELLENT!")
        print("✅ All systems are go for production deployment!")
    elif overall_score >= 80:
        print("\n✅ PRODUCTION READY - GOOD")
        print("✅ Ready for production with minor optimizations")
    elif overall_score >= 70:
        print("\n⚠️  PRODUCTION READY - WITH CAUTION")
        print("⚠️  Some issues should be addressed before production")
    else:
        print("\n❌ NOT PRODUCTION READY")
        print("❌ Critical issues must be fixed before production")
    
    # Save report
    os.makedirs('test_results', exist_ok=True)
    report_file = f"test_results/production_readiness_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Report saved: {report_file}")
    
    return report['production_ready']

if __name__ == "__main__":
    print("🔍 FINAL PRODUCTION READINESS CHECK")
    print("=" * 60)
    print("Checking WebSearchAgentLocalMerged for production deployment...")
    
    success = generate_production_readiness_report()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 PRODUCTION READINESS CHECK: PASSED")
        print("✅ WebSearchAgentLocalMerged is ready for production!")
    else:
        print("❌ PRODUCTION READINESS CHECK: FAILED")
        print("⚠️  Address issues before production deployment")
    
    sys.exit(0 if success else 1)
