#!/usr/bin/env python3
"""
Quick test để kiểm tra search functionality.
"""

import requests
import json

def test_duckduckgo_quick():
    """Test DuckDuckGo nhanh."""
    print("🦆 Testing DuckDuckGo...")
    
    try:
        # S<PERSON> dụng DuckDuckGo Instant Answer API
        url = "https://api.duckduckgo.com/"
        params = {
            'q': 'Python programming',
            'format': 'json',
            'no_html': '1',
            'skip_disambig': '1'
        }
        
        response = requests.get(url, params=params, timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            
            # <PERSON><PERSON><PERSON> tra có kết quả không
            if data.get('Abstract') or data.get('RelatedTopics'):
                print("✅ DuckDuckGo API: Working")
                print(f"   Abstract: {data.get('Abstract', 'N/A')[:100]}...")
                return True
            else:
                print("❌ DuckDuckGo API: No results")
                return False
        else:
            print(f"❌ DuckDuckGo API: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ DuckDuckGo API: {e}")
        return False

def test_httpbin():
    """Test network connectivity."""
    print("\n🌐 Testing network connectivity...")
    
    try:
        response = requests.get("https://httpbin.org/get", timeout=5)
        if response.status_code == 200:
            print("✅ Network: Working")
            return True
        else:
            print(f"❌ Network: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Network: {e}")
        return False

def main():
    """Main test."""
    print("⚡ QUICK SEARCH TEST")
    print("=" * 30)
    
    # Test network
    network_ok = test_httpbin()
    
    # Test DuckDuckGo
    ddg_ok = test_duckduckgo_quick()
    
    print("\n" + "=" * 30)
    print("📊 RESULTS")
    print("=" * 30)
    print(f"Network: {'✅' if network_ok else '❌'}")
    print(f"DuckDuckGo: {'✅' if ddg_ok else '❌'}")
    
    if network_ok and ddg_ok:
        print("\n🎉 SEARCH ENGINES ARE ACCESSIBLE!")
        print("✅ Real search functionality should work.")
    else:
        print("\n❌ SEARCH ENGINES NOT ACCESSIBLE")
        print("❌ Check network or try different search engines.")

if __name__ == "__main__":
    main()
