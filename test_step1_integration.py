#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test STEP 1, 2 & 3: AdvancedCrawlee + Error Utils + Playwright Handler Integration
"""

import sys
import os

# Add paths
sys.path.append('.')
sys.path.append('src')
sys.path.append('deepresearch/src')

def test_advanced_crawlee_import():
    """Test importing AdvancedCrawlee modules."""
    try:
        print("Testing AdvancedCrawlee import...")
        from deepresearch.src.deep_research_core.agents.advanced_crawlee import ResourceManager, MemoryOptimizedCrawler
        print("✅ AdvancedCrawlee modules imported successfully")
        return True, ResourceManager, MemoryOptimizedCrawler
    except Exception as e:
        print(f"❌ Failed to import AdvancedCrawlee: {e}")
        return False, None, None

def test_resource_manager():
    """Test ResourceManager functionality."""
    try:
        success, ResourceManager, _ = test_advanced_crawlee_import()
        if not success:
            return False
        
        print("Creating ResourceManager instance...")
        rm = ResourceManager(memory_limit_mb=512, max_concurrent_processes=2)
        print("✅ ResourceManager created")
        
        print("Testing ResourceManager methods...")
        status = rm.get_resource_usage()
        print(f"Resource usage: {status}")
        
        active = rm.get_active_processes()
        print(f"Active processes: {active}")
        
        print("✅ ResourceManager tests passed")
        return True
    except Exception as e:
        print(f"❌ ResourceManager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_memory_optimized_crawler():
    """Test MemoryOptimizedCrawler functionality."""
    try:
        success, ResourceManager, MemoryOptimizedCrawler = test_advanced_crawlee_import()
        if not success:
            return False
        
        print("Creating ResourceManager for MemoryOptimizedCrawler...")
        rm = ResourceManager(memory_limit_mb=512, max_concurrent_processes=2)
        
        print("Creating MemoryOptimizedCrawler instance...")
        crawler = MemoryOptimizedCrawler(resource_manager=rm, batch_size=5)
        print("✅ MemoryOptimizedCrawler created")
        
        print("✅ MemoryOptimizedCrawler tests passed")
        return True
    except Exception as e:
        print(f"❌ MemoryOptimizedCrawler test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_adaptive_crawler_integration():
    """Test AdaptiveCrawler integration with AdvancedCrawlee."""
    try:
        print("Testing AdaptiveCrawler integration...")
        from src.deep_research_core.agents.adaptive_crawler_consolidated_merged import AdaptiveCrawlerConsolidatedMerged

        print("Creating AdaptiveCrawler with memory optimization...")
        crawler = AdaptiveCrawlerConsolidatedMerged(
            use_memory_optimization=True,
            memory_limit_mb=512,
            max_concurrent_processes=2,
            batch_size=5
        )
        print("✅ AdaptiveCrawler created")

        print("Testing resource status...")
        status = crawler.get_resource_status()
        print(f"Resource status: {status}")

        print("Testing batch size optimization...")
        batch_size = crawler.optimize_batch_size()
        print(f"Optimized batch size: {batch_size}")

        print("Testing memory cleanup...")
        crawler.cleanup_memory()
        print("✅ Memory cleanup completed")

        print("✅ AdaptiveCrawler integration tests passed")
        return True
    except Exception as e:
        print(f"❌ AdaptiveCrawler integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_handling_integration():
    """Test Error Utils integration with AdaptiveCrawler."""
    try:
        print("Testing Error Utils integration...")
        from src.deep_research_core.agents.adaptive_crawler_consolidated_merged import AdaptiveCrawlerConsolidatedMerged

        print("Creating AdaptiveCrawler with error handling...")
        crawler = AdaptiveCrawlerConsolidatedMerged(
            use_error_handling=True,
            max_retries=2,
            retry_delay=0.5,
            retry_backoff=1.5
        )
        print("✅ AdaptiveCrawler with error handling created")

        print("Testing input validation...")
        validation_result = crawler.validate_crawl_input(
            url="https://example.com",
            max_depth=2,
            max_pages=5,
            use_playwright=True
        )
        print(f"Validation result: {validation_result}")

        print("Testing invalid input validation...")
        invalid_validation = crawler.validate_crawl_input(
            url="invalid-url",
            max_depth=-1,
            max_pages="not-a-number"
        )
        print(f"Invalid validation result: {invalid_validation}")

        print("Testing safe crawl multiple with empty list...")
        safe_result = crawler.safe_crawl_multiple([])
        print(f"Safe crawl empty result: {safe_result}")

        print("✅ Error handling integration tests passed")
        return True
    except Exception as e:
        print(f"❌ Error handling integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_playwright_handler_integration():
    """Test Playwright Handler integration with AdaptiveCrawler."""
    try:
        print("Testing Playwright Handler integration...")
        from src.deep_research_core.agents.adaptive_crawler_consolidated_merged import AdaptiveCrawlerConsolidatedMerged

        print("Creating AdaptiveCrawler with Playwright Handler...")
        crawler = AdaptiveCrawlerConsolidatedMerged(
            use_playwright_handler=True,
            playwright_headless=True,
            playwright_browser_type="chromium",
            playwright_timeout=30000
        )
        print("✅ AdaptiveCrawler with Playwright Handler created")

        print("Testing Playwright Handler status...")
        status = crawler.get_playwright_handler_status()
        print(f"Playwright Handler status: {status}")

        print("Testing Playwright context creation...")
        context = crawler.create_playwright_context()
        print(f"Playwright context created: {context is not None}")

        print("✅ Playwright Handler integration tests passed")
        return True
    except Exception as e:
        print(f"❌ Playwright Handler integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("=" * 60)
    print("STEP 1 & 2 INTEGRATION TEST: AdvancedCrawlee + Error Utils")
    print("=" * 60)
    
    tests = [
        ("AdvancedCrawlee Import", test_advanced_crawlee_import),
        ("ResourceManager", test_resource_manager),
        ("MemoryOptimizedCrawler", test_memory_optimized_crawler),
        ("AdaptiveCrawler Integration", test_adaptive_crawler_integration),
        ("Error Handling Integration", test_error_handling_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔧 Testing {test_name}...")
        print("-" * 40)
        
        if test_name == "AdvancedCrawlee Import":
            success, _, _ = test_func()
        else:
            success = test_func()
        
        results.append((test_name, success))
        
        if success:
            print(f"✅ {test_name}: PASSED")
        else:
            print(f"❌ {test_name}: FAILED")
    
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nTotal: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! STEP 1 & 2 INTEGRATION SUCCESSFUL!")
        return True
    else:
        print("⚠️  SOME TESTS FAILED. STEP 1 & 2 INTEGRATION INCOMPLETE.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
