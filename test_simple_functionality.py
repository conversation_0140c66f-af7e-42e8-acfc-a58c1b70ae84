#!/usr/bin/env python3
"""
Simple functionality test cho WebSearchAgentLocalMerged.
Tránh dependency conflicts bằng cách test trực tiếp các methods.
"""

import sys
import os
import json
import inspect
from datetime import datetime

def test_agent_structure():
    """Test cấu trúc của WebSearchAgentLocalMerged."""
    print("🔍 Testing WebSearchAgentLocalMerged Structure")
    print("=" * 50)
    
    try:
        # Import agent class
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
        
        # Import chỉ class definition, không khởi tạo
        import importlib.util
        spec = importlib.util.spec_from_file_location(
            "web_search_agent_local_merged", 
            "src/deep_research_core/agents/web_search_agent_local_merged.py"
        )
        module = importlib.util.module_from_spec(spec)
        
        # Load module mà không execute imports có thể gây conflict
        with open("src/deep_research_core/agents/web_search_agent_local_merged.py", 'r') as f:
            content = f.read()
        
        print("✅ File loaded successfully")
        
        # Kiểm tra class definition
        if "class WebSearchAgentLocalMerged" in content:
            print("✅ WebSearchAgentLocalMerged class found")
        else:
            print("❌ WebSearchAgentLocalMerged class not found")
            return False
        
        # Kiểm tra các methods mới đã thêm
        new_methods = [
            # Multi-language Processing
            "detect_content_language",
            "normalize_content_language", 
            "extract_keywords_multilingual",
            
            # Smart Caching
            "_get_cache_key",
            "_get_from_cache",
            "_save_to_cache",
            "_determine_cache_ttl",
            "get_cache_stats",
            "clear_cache",
            
            # Query Optimization
            "optimize_query",
            "generate_alternative_queries",
            "detect_query_intent",
            
            # Advanced Crawling
            "crawl_with_javascript_support",
            "crawl_spa_website",
            "crawl_with_infinite_scroll",
            "crawl_with_form_interaction",
            "crawl_with_pagination",
            
            # Performance Optimization
            "get_performance_stats",
            "optimize_performance",
            "batch_process_urls",
            "adaptive_timeout",
            
            # Resource Management
            "cleanup_resources",
            
            # Real Search Implementation
            "_perform_real_search",
            "_search_with_searxng",
            "_search_with_duckduckgo",
            "_search_with_bing",
            "_search_with_google",
            "_fallback_search",
            "_check_searxng_health"
        ]
        
        found_methods = 0
        missing_methods = []
        
        for method in new_methods:
            if f"def {method}" in content:
                print(f"✅ Method found: {method}")
                found_methods += 1
            else:
                print(f"❌ Method missing: {method}")
                missing_methods.append(method)
        
        print(f"\n📊 Methods Summary:")
        print(f"Total expected: {len(new_methods)}")
        print(f"Found: {found_methods}")
        print(f"Missing: {len(missing_methods)}")
        print(f"Coverage: {found_methods/len(new_methods)*100:.1f}%")
        
        if missing_methods:
            print(f"\n❌ Missing methods: {', '.join(missing_methods)}")
        
        # Kiểm tra real search implementation
        print(f"\n🔍 Real Search Implementation Check:")
        if "_perform_real_search" in content and "example.com" not in content.split("_perform_real_search")[1].split("def ")[0]:
            print("✅ Real search implementation found (no mock data)")
        else:
            print("❌ Still using mock data or missing real search")
        
        # Kiểm tra SearXNG local priority
        if "localhost:8080" in content and "_check_searxng_health" in content:
            print("✅ SearXNG local priority implemented")
        else:
            print("❌ SearXNG local priority missing")
        
        return found_methods >= len(new_methods) * 0.8  # 80% coverage
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_file_integrity():
    """Test tính toàn vẹn của file."""
    print("\n🔧 Testing File Integrity")
    print("=" * 30)
    
    try:
        file_path = "src/deep_research_core/agents/web_search_agent_local_merged.py"
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Kiểm tra cơ bản
        lines = content.split('\n')
        print(f"✅ File size: {len(content)} characters")
        print(f"✅ Line count: {len(lines)}")
        
        # Kiểm tra syntax cơ bản
        if content.count('def ') > 20:
            print("✅ Sufficient method definitions")
        else:
            print("❌ Too few method definitions")
        
        if content.count('class ') >= 1:
            print("✅ Class definition found")
        else:
            print("❌ No class definition")
        
        # Kiểm tra imports
        import_lines = [line for line in lines if line.strip().startswith('import ') or line.strip().startswith('from ')]
        print(f"✅ Import statements: {len(import_lines)}")
        
        # Kiểm tra docstrings
        docstring_count = content.count('"""')
        print(f"✅ Docstrings: {docstring_count // 2} methods documented")
        
        return True
        
    except Exception as e:
        print(f"❌ File integrity error: {e}")
        return False

def test_search_engines_config():
    """Test cấu hình search engines."""
    print("\n🌐 Testing Search Engines Configuration")
    print("=" * 40)
    
    try:
        file_path = "src/deep_research_core/agents/web_search_agent_local_merged.py"
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Kiểm tra SearXNG instances
        searxng_instances = [
            "localhost:8080",
            "searx.be",
            "search.sapti.me",
            "searx.tiekoetter.com"
        ]
        
        for instance in searxng_instances:
            if instance in content:
                print(f"✅ SearXNG instance: {instance}")
            else:
                print(f"❌ Missing SearXNG instance: {instance}")
        
        # Kiểm tra DuckDuckGo
        if "duckduckgo" in content.lower():
            print("✅ DuckDuckGo integration found")
        else:
            print("❌ DuckDuckGo integration missing")
        
        # Kiểm tra Bing API
        if "bing" in content.lower() and "api" in content.lower():
            print("✅ Bing API integration found")
        else:
            print("❌ Bing API integration missing")
        
        # Kiểm tra Google API
        if "google" in content.lower() and "customsearch" in content.lower():
            print("✅ Google Custom Search integration found")
        else:
            print("❌ Google Custom Search integration missing")
        
        return True
        
    except Exception as e:
        print(f"❌ Search engines config error: {e}")
        return False

def generate_test_report():
    """Tạo báo cáo test."""
    print("\n📊 GENERATING TEST REPORT")
    print("=" * 50)
    
    # Chạy tất cả tests
    structure_test = test_agent_structure()
    integrity_test = test_file_integrity()
    engines_test = test_search_engines_config()
    
    # Tạo báo cáo
    report = {
        'timestamp': datetime.now().isoformat(),
        'tests': {
            'structure_test': structure_test,
            'integrity_test': integrity_test,
            'engines_test': engines_test
        },
        'overall_status': all([structure_test, integrity_test, engines_test])
    }
    
    # Lưu báo cáo
    os.makedirs('test_results', exist_ok=True)
    report_file = f"test_results/simple_functionality_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Test report saved: {report_file}")
    
    # Summary
    passed_tests = sum([structure_test, integrity_test, engines_test])
    total_tests = 3
    success_rate = passed_tests / total_tests * 100
    
    print(f"\n🎯 FINAL RESULTS:")
    print(f"Passed tests: {passed_tests}/{total_tests}")
    print(f"Success rate: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("\n🎉 TESTS PASSED!")
        print("✅ WebSearchAgentLocalMerged structure is good!")
        return True
    else:
        print("\n❌ TESTS FAILED")
        print("⚠️  Need to fix issues before proceeding")
        return False

if __name__ == "__main__":
    success = generate_test_report()
    sys.exit(0 if success else 1)
