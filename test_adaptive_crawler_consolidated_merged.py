#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script cho AdaptiveCrawlerConsolidatedMerged.
Test tất cả tính năng đã được merge.

PHÂN TÍCH CÁC CRAWLER KHÁC VÀ KẾ HOẠCH MERGE:

## 1. PHÂN TÍCH CÁC CRAWLER HIỆN CÓ:

### WebSearchAgentLocalMerged:
**Điểm mạnh:**
- Tích hợp tìm kiếm từ nhiều nguồn (SearXNG, CocCoc, WikiTiengViet, BaoMoi)
- <PERSON><PERSON>h giá độ tin cậy nội dung (CredibilityEvaluator)
- <PERSON><PERSON><PERSON> hiện tin giả (FakeNewsDetector)
- Xử lý ngôn ngữ tiếng Việt chuyên sâu
- <PERSON><PERSON> thông minh với TTL
- Xử lý file đa dạng (PDF, DOCX, XLSX, PPTX)
- Tối ưu hóa hiệu suất với async processing
- Adaptive scraping dựa trên content type

**Điểm yếu:**
- Không có xử lý JavaScript/SPA chuyên sâu
- Thiếu xử lý infinite scroll
- Không có form handling
- Thiếu pagination detection

### AdaptiveCrawlerConsolidatedMerged:
**Điểm mạnh:**
- Xử lý JavaScript với Playwright
- Hỗ trợ SPA, infinite scroll, AJAX
- Form handling
- Pagination detection
- Site map generation
- Media file download
- Robots.txt compliance

**Điểm yếu:**
- Thiếu tích hợp tìm kiếm
- Không có đánh giá độ tin cậy
- Thiếu xử lý ngôn ngữ tiếng Việt
- Không có phát hiện tin giả

### AdvancedCrawlee:
**Điểm mạnh:**
- Memory optimization với ResourceManager
- Batch processing
- Concurrent crawling
- Performance monitoring
- Adaptive batch sizing

### Website_crawler.py:
**Điểm mạnh:**
- Command-line interface
- Domain filtering
- Comprehensive reporting

## 2. KẾ HOẠCH MERGE:

### Phase 1: Core Integration
- [ ] Merge WebSearchAgentLocalMerged search capabilities
- [ ] Integrate CredibilityEvaluator và FakeNewsDetector
- [ ] Add Vietnamese language support
- [ ] Merge file processing capabilities

### Phase 2: Advanced Features
- [ ] Integrate AdvancedCrawlee memory optimization
- [ ] Add async processing capabilities
- [ ] Merge advanced JavaScript handling
- [ ] Integrate form processing improvements

### Phase 3: Performance & Monitoring
- [ ] Add performance benchmarking
- [ ] Integrate resource monitoring
- [ ] Add comprehensive error handling
- [ ] Implement adaptive crawling strategies

### Phase 4: Testing & Validation
- [ ] Create comprehensive test suite
- [ ] Performance testing
- [ ] Integration testing
- [ ] Vietnamese content testing
"""

import sys
import os
import json
import time
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.abspath(os.path.dirname(__file__)))

def test_adaptive_crawler_consolidated_merged():
    """Test AdaptiveCrawlerConsolidatedMerged với tất cả tính năng."""
    
    print("🚀 Testing AdaptiveCrawlerConsolidatedMerged")
    print("=" * 60)
    
    try:
        # Import consolidated merged crawler
        from src.deep_research_core.agents.adaptive_crawler_consolidated_merged import AdaptiveCrawlerConsolidatedMerged
        
        # Initialize crawler với tất cả tính năng
        crawler_config = {
            # Basic settings
            "use_playwright": False,  # Use requests for testing
            "max_depth": 1,
            "max_pages": 3,
            "timeout": 10,
            
            # User-Agent rotation
            "rotate_user_agents": True,
            
            # Robots.txt handling
            "respect_robots": True,
            "robots_cache_ttl": 3600,
            
            # Advanced features
            "download_media": False,  # Tắt để test nhanh hơn
            "site_map_enabled": True,
            "extract_file_content": False,
            "enable_javascript": False,  # Tắt vì dùng requests
            
            # Feature flags
            "handle_infinite_scroll": False,
            "handle_ajax": False,
            "handle_pagination": False,
            "handle_spa": False,
            "handle_forms": False,
            "handle_captcha": False,
            "detect_language": False
        }
        
        print("✅ Initializing AdaptiveCrawlerConsolidatedMerged...")
        crawler = AdaptiveCrawlerConsolidatedMerged(**crawler_config)
        print(f"✅ Crawler initialized: {crawler.name} v{crawler.version}")
        
        # Test cases
        test_cases = [
            {
                "name": "Single URL Crawl",
                "method": "crawl",
                "args": ["https://httpbin.org/html"],
                "kwargs": {"max_depth": 1, "max_pages": 2}
            },
            {
                "name": "Multiple URL Crawl",
                "method": "crawl_multiple",
                "args": [["https://httpbin.org/html", "https://httpbin.org/json"]],
                "kwargs": {"max_depth": 1, "max_pages": 1}
            },
            {
                "name": "Website Crawl (Comprehensive)",
                "method": "crawl_website",
                "args": ["https://httpbin.org/html"],
                "kwargs": {"max_depth": 1, "max_pages": 2}
            }
        ]
        
        results = {}
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🧪 Test {i}: {test_case['name']}")
            print("-" * 40)
            
            start_time = time.time()
            
            try:
                method = getattr(crawler, test_case["method"])
                result = method(*test_case["args"], **test_case["kwargs"])
                end_time = time.time()
                
                print(f"✅ {test_case['method']} completed in {end_time - start_time:.2f}s")
                
                # Display key information
                if result.get("success"):
                    print(f"🔍 Method: {test_case['method']}")
                    
                    if "total_pages" in result:
                        print(f"📊 Total pages: {result.get('total_pages', 0)}")
                        print(f"🔗 Total links: {result.get('total_links', 0)}")
                        print(f"🖼️  Total media files: {result.get('total_media_files', 0)}")
                    
                    if "statistics" in result:
                        stats = result["statistics"]
                        print(f"📊 Statistics:")
                        print(f"   - Total pages: {stats.get('total_pages', 0)}")
                        print(f"   - Successful: {stats.get('successful_pages', 0)}")
                        print(f"   - Failed: {stats.get('failed_pages', 0)}")
                        print(f"   - Links: {stats.get('total_links', 0)}")
                        print(f"   - Media files: {stats.get('total_media_files', 0)}")
                    
                    if "successful_crawls" in result:
                        print(f"✅ Successful crawls: {result.get('successful_crawls', 0)}")
                        print(f"❌ Failed crawls: {result.get('failed_crawls', 0)}")
                    
                    # Show sample pages
                    pages = result.get("pages", [])
                    if pages:
                        print("📋 Sample pages:")
                        for j, page in enumerate(pages[:2], 1):
                            print(f"  {j}. {page.get('url', 'No URL')}")
                            print(f"     Title: {page.get('content', {}).get('title', 'No title')}")
                            print(f"     Success: {'✅' if page.get('success') else '❌'}")
                            if not page.get('success'):
                                print(f"     Error: {page.get('error', 'Unknown error')}")
                
                else:
                    print(f"❌ {test_case['method']} failed: {result.get('error', 'Unknown error')}")
                
                results[test_case["name"]] = {
                    "success": result.get("success", False),
                    "execution_time": end_time - start_time,
                    "method": test_case["method"],
                    "total_pages": result.get("total_pages", 0),
                    "total_links": result.get("total_links", 0),
                    "total_media_files": result.get("total_media_files", 0),
                    "has_statistics": "statistics" in result,
                    "has_site_map": "site_map" in result or bool(crawler.get_site_map())
                }
                
            except Exception as e:
                end_time = time.time()
                print(f"❌ Test failed: {str(e)}")
                results[test_case["name"]] = {
                    "success": False,
                    "error": str(e),
                    "execution_time": end_time - start_time,
                    "method": test_case["method"]
                }
        
        # Test additional features
        print(f"\n🔧 Testing Additional Features")
        print("-" * 40)

        # Test các tính năng từ WebSearchAgentLocalMerged
        print(f"\n📋 Testing WebSearchAgentLocalMerged Integration Features")
        print("-" * 50)

        # Test search capabilities (nếu có)
        try:
            if hasattr(crawler, 'search'):
                search_result = crawler.search("test query", num_results=3)
                print(f"✅ Search capability: {'Available' if search_result else 'Not implemented'}")
            else:
                print(f"⚠️  Search capability: Not available")
        except Exception as e:
            print(f"⚠️  Search capability test failed: {str(e)}")

        # Test credibility evaluation (nếu có)
        try:
            if hasattr(crawler, 'evaluate_credibility'):
                cred_result = crawler.evaluate_credibility("Sample content", "https://example.com")
                print(f"✅ Credibility evaluation: {'Available' if cred_result else 'Not implemented'}")
            else:
                print(f"⚠️  Credibility evaluation: Not available")
        except Exception as e:
            print(f"⚠️  Credibility evaluation test failed: {str(e)}")

        # Test Vietnamese language support (nếu có)
        try:
            if hasattr(crawler, 'detect_vietnamese') or hasattr(crawler, 'vietnamese_support'):
                print(f"✅ Vietnamese language support: Available")
            else:
                print(f"⚠️  Vietnamese language support: Not available")
        except Exception as e:
            print(f"⚠️  Vietnamese language support test failed: {str(e)}")

        # Test file processing capabilities (nếu có)
        try:
            if hasattr(crawler, 'process_file') or hasattr(crawler, 'extract_file_content'):
                print(f"✅ File processing: Available")
            else:
                print(f"⚠️  File processing: Not available")
        except Exception as e:
            print(f"⚠️  File processing test failed: {str(e)}")

        print(f"\n📋 Testing AdvancedCrawlee Integration Features")
        print("-" * 50)

        # Test memory optimization (nếu có)
        try:
            if hasattr(crawler, 'resource_manager') or hasattr(crawler, 'memory_optimization'):
                print(f"✅ Memory optimization: Available")
            else:
                print(f"⚠️  Memory optimization: Not available")
        except Exception as e:
            print(f"⚠️  Memory optimization test failed: {str(e)}")

        # Test batch processing (nếu có)
        try:
            if hasattr(crawler, 'batch_process') or hasattr(crawler, 'crawl_batch'):
                print(f"✅ Batch processing: Available")
            else:
                print(f"⚠️  Batch processing: Not available")
        except Exception as e:
            print(f"⚠️  Batch processing test failed: {str(e)}")

        # Test async capabilities (nếu có)
        try:
            if hasattr(crawler, 'async_crawl') or hasattr(crawler, 'concurrent_crawl'):
                print(f"✅ Async processing: Available")
            else:
                print(f"⚠️  Async processing: Not available")
        except Exception as e:
            print(f"⚠️  Async processing test failed: {str(e)}")

        print(f"\n📋 Testing Advanced JavaScript Features")
        print("-" * 50)

        # Test JavaScript handling
        try:
            if hasattr(crawler, 'handle_javascript') or hasattr(crawler, 'process_javascript'):
                print(f"✅ JavaScript handling: Available")
            else:
                print(f"⚠️  JavaScript handling: Not available")
        except Exception as e:
            print(f"⚠️  JavaScript handling test failed: {str(e)}")

        # Test SPA support
        try:
            if hasattr(crawler, 'handle_spa') or hasattr(crawler, 'spa_support'):
                print(f"✅ SPA support: Available")
            else:
                print(f"⚠️  SPA support: Not available")
        except Exception as e:
            print(f"⚠️  SPA support test failed: {str(e)}")

        # Test infinite scroll
        try:
            if hasattr(crawler, 'handle_infinite_scroll') or hasattr(crawler, 'infinite_scroll'):
                print(f"✅ Infinite scroll: Available")
            else:
                print(f"⚠️  Infinite scroll: Not available")
        except Exception as e:
            print(f"⚠️  Infinite scroll test failed: {str(e)}")

        # Test form handling
        try:
            if hasattr(crawler, 'handle_form') or hasattr(crawler, 'form_processing'):
                print(f"✅ Form handling: Available")
            else:
                print(f"⚠️  Form handling: Not available")
        except Exception as e:
            print(f"⚠️  Form handling test failed: {str(e)}")

        print(f"\n📋 Testing Performance & Monitoring Features")
        print("-" * 50)

        # Test performance monitoring
        try:
            if hasattr(crawler, 'performance_monitor') or hasattr(crawler, 'get_performance_stats'):
                print(f"✅ Performance monitoring: Available")
            else:
                print(f"⚠️  Performance monitoring: Not available")
        except Exception as e:
            print(f"⚠️  Performance monitoring test failed: {str(e)}")

        # Test resource monitoring
        try:
            if hasattr(crawler, 'resource_monitor') or hasattr(crawler, 'get_resource_usage'):
                print(f"✅ Resource monitoring: Available")
            else:
                print(f"⚠️  Resource monitoring: Not available")
        except Exception as e:
            print(f"⚠️  Resource monitoring test failed: {str(e)}")

        # Test adaptive strategies
        try:
            if hasattr(crawler, 'adaptive_strategy') or hasattr(crawler, 'auto_adjust'):
                print(f"✅ Adaptive strategies: Available")
            else:
                print(f"⚠️  Adaptive strategies: Not available")
        except Exception as e:
            print(f"⚠️  Adaptive strategies test failed: {str(e)}")
        
        # Test User-Agent rotation
        try:
            ua1 = crawler._get_user_agent()
            ua2 = crawler._get_user_agent()
            print(f"✅ User-Agent rotation: {'Working' if ua1 != ua2 or len(crawler.user_agents) == 1 else 'Not working'}")
        except Exception as e:
            print(f"⚠️  User-Agent rotation test failed: {str(e)}")
        
        # Test robots.txt checking
        try:
            can_fetch = crawler._can_fetch("https://httpbin.org/robots.txt")
            print(f"✅ Robots.txt check: {'Allowed' if can_fetch else 'Blocked'}")
        except Exception as e:
            print(f"⚠️  Robots.txt check failed: {str(e)}")
        
        # Test site map
        try:
            site_map = crawler.get_site_map()
            print(f"✅ Site map: {len(site_map)} entries")
            if site_map:
                sample_url = list(site_map.keys())[0]
                print(f"    Sample: {sample_url}")
        except Exception as e:
            print(f"⚠️  Site map test failed: {str(e)}")
        
        # Test cache clearing
        try:
            crawler.clear_cache()
            print(f"✅ Cache clearing: Success")
        except Exception as e:
            print(f"⚠️  Cache clearing failed: {str(e)}")
        
        # Summary
        print(f"\n📊 Test Summary")
        print("=" * 60)
        
        successful_tests = sum(1 for r in results.values() if r.get("success", False))
        total_tests = len(results)
        
        print(f"✅ Successful tests: {successful_tests}/{total_tests}")
        print(f"⏱️  Average execution time: {sum(r.get('execution_time', 0) for r in results.values()) / len(results):.2f}s")
        
        # Feature coverage
        features_tested = {
            "Single URL crawling": any(r.get("method") == "crawl" for r in results.values()),
            "Multiple URL crawling": any(r.get("method") == "crawl_multiple" for r in results.values()),
            "Website crawling": any(r.get("method") == "crawl_website" for r in results.values()),
            "Site map generation": any(r.get("has_site_map", False) for r in results.values()),
            "Statistics collection": any(r.get("has_statistics", False) for r in results.values())
        }
        
        print(f"\n🎯 Feature Coverage:")
        for feature, tested in features_tested.items():
            status = "✅" if tested else "❌"
            print(f"  {status} {feature}")
        
        # Performance analysis
        print(f"\n📈 Performance Analysis:")
        total_pages_crawled = sum(r.get("total_pages", 0) for r in results.values())
        total_links_found = sum(r.get("total_links", 0) for r in results.values())
        total_media_found = sum(r.get("total_media_files", 0) for r in results.values())
        
        print(f"  📊 Total pages crawled: {total_pages_crawled}")
        print(f"  🔗 Total links found: {total_links_found}")
        print(f"  🖼️  Total media files found: {total_media_found}")
        
        if total_pages_crawled > 0:
            avg_links_per_page = total_links_found / total_pages_crawled
            avg_media_per_page = total_media_found / total_pages_crawled
            print(f"  📈 Average links per page: {avg_links_per_page:.1f}")
            print(f"  📈 Average media files per page: {avg_media_per_page:.1f}")
        
        # Save results to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"test_results/adaptive_crawler_consolidated_merged_test_{timestamp}.json"
        
        os.makedirs("test_results", exist_ok=True)
        
        with open(results_file, "w", encoding="utf-8") as f:
            json.dump({
                "timestamp": timestamp,
                "crawler_info": {
                    "name": crawler.name,
                    "version": crawler.version
                },
                "test_results": results,
                "feature_coverage": features_tested,
                "performance": {
                    "total_pages_crawled": total_pages_crawled,
                    "total_links_found": total_links_found,
                    "total_media_found": total_media_found
                },
                "summary": {
                    "successful_tests": successful_tests,
                    "total_tests": total_tests,
                    "success_rate": successful_tests / total_tests if total_tests > 0 else 0
                }
            }, f, indent=2)
        
        print(f"\n💾 Test results saved to {results_file}")

        # In kế hoạch merge chi tiết
        print(f"\n📋 DETAILED MERGE PLAN")
        print("=" * 60)

        print(f"\n🎯 PRIORITY 1: Core Search Integration")
        print("-" * 40)
        print("✅ Merge WebSearchAgentLocalMerged.search() method")
        print("✅ Integrate SearXNG, CocCoc, WikiTiengViet, BaoMoi search engines")
        print("✅ Add query optimization and enhancement")
        print("✅ Implement search result caching with TTL")
        print("✅ Add search result ranking and filtering")

        print(f"\n🎯 PRIORITY 2: Content Analysis & Credibility")
        print("-" * 40)
        print("✅ Integrate CredibilityEvaluator for source assessment")
        print("✅ Add FakeNewsDetector for misinformation detection")
        print("✅ Implement content quality scoring")
        print("✅ Add source reputation tracking")
        print("✅ Integrate bias detection algorithms")

        print(f"\n🎯 PRIORITY 3: Vietnamese Language Support")
        print("-" * 40)
        print("✅ Add Vietnamese text processing utilities")
        print("✅ Integrate Vietnamese NLP capabilities")
        print("✅ Add Vietnamese-specific search methods")
        print("✅ Implement Vietnamese content extraction")
        print("✅ Add Vietnamese language detection")

        print(f"\n🎯 PRIORITY 4: File Processing Enhancement")
        print("-" * 40)
        print("✅ Merge advanced file extraction (PDF, DOCX, XLSX, PPTX)")
        print("✅ Add multimedia file processing (audio, video)")
        print("✅ Implement structured data extraction")
        print("✅ Add file metadata analysis")
        print("✅ Integrate file content indexing")

        print(f"\n🎯 PRIORITY 5: Memory & Performance Optimization")
        print("-" * 40)
        print("✅ Integrate AdvancedCrawlee ResourceManager")
        print("✅ Add memory-optimized batch processing")
        print("✅ Implement adaptive batch sizing")
        print("✅ Add concurrent crawling capabilities")
        print("✅ Integrate performance monitoring")

        print(f"\n🎯 PRIORITY 6: Advanced JavaScript & SPA")
        print("-" * 40)
        print("✅ Enhance JavaScript handling with advanced detection")
        print("✅ Improve SPA support with framework detection")
        print("✅ Add infinite scroll handling with smart detection")
        print("✅ Implement AJAX request monitoring")
        print("✅ Add dynamic content waiting strategies")

        print(f"\n🎯 PRIORITY 7: Form & Interaction Handling")
        print("-" * 40)
        print("✅ Enhance form detection and processing")
        print("✅ Add CAPTCHA handling improvements")
        print("✅ Implement smart form filling strategies")
        print("✅ Add form validation and error handling")
        print("✅ Integrate form submission tracking")

        print(f"\n🎯 PRIORITY 8: Error Handling & Resilience")
        print("-" * 40)
        print("✅ Implement comprehensive error categorization")
        print("✅ Add intelligent retry mechanisms")
        print("✅ Integrate circuit breaker patterns")
        print("✅ Add graceful degradation strategies")
        print("✅ Implement error recovery workflows")

        print(f"\n📊 IMPLEMENTATION STEPS:")
        print("-" * 40)
        print("1. Create backup of current adaptive_crawler_consolidated_merged.py")
        print("2. Analyze and extract key methods from WebSearchAgentLocalMerged")
        print("3. Integrate search capabilities with proper error handling")
        print("4. Add credibility and content analysis features")
        print("5. Implement Vietnamese language support")
        print("6. Merge file processing capabilities")
        print("7. Integrate memory optimization from AdvancedCrawlee")
        print("8. Enhance JavaScript and SPA handling")
        print("9. Improve form and interaction processing")
        print("10. Add comprehensive testing for all features")
        print("11. Performance testing and optimization")
        print("12. Documentation and usage examples")

        print(f"\n⚠️  POTENTIAL CHALLENGES:")
        print("-" * 40)
        print("• Dependency conflicts between modules")
        print("• Memory usage optimization with large feature set")
        print("• Maintaining backward compatibility")
        print("• Integration testing complexity")
        print("• Performance impact of additional features")
        print("• Configuration management complexity")

        print(f"\n🎯 SUCCESS METRICS:")
        print("-" * 40)
        print("• All existing tests pass")
        print("• New features work independently")
        print("• Performance within 20% of original")
        print("• Memory usage optimized")
        print("• Vietnamese content processing accuracy > 90%")
        print("• Search result quality improved")
        print("• Error handling coverage > 95%")

    except Exception as e:
        print(f"❌ Critical error: {str(e)}")
        return False

    return True

if __name__ == "__main__":
    success = test_adaptive_crawler_consolidated_merged()
    sys.exit(0 if success else 1)